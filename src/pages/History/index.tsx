import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '历史记录',
};

const default_title = '历史记录';

const Order: React.FC = () => {
  const location = useLocation();
  const [title, setTitle] = useState(default_title);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    // const _breadcrumbInfoList = global.getBreadcrumbInfo(breadcrumbInfo, location);
    let title = default_title;
    if (location.pathname.includes('distribution/record-list')) {
      title = '发卡操作记录';
    } else if (location.pathname.includes('change-expiration/record-list')) {
      title = '改效期操作记录';
    } else if (location.pathname.includes('refund/record-list')) {
      title = '退款操作记录';
    }
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      { ...breadcrumbInfo, breadcrumbName: title },
      '',
      location,
    );
    _breadcrumbInfoList[0].breadcrumbName = default_title;
    setTitle(title);

    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.breadcrumbName}</span>,
          items: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default Order;
