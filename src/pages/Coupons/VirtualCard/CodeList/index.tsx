import RedirectLink from '@/components/TableRowLink/RedirectLink';
import {
  fetchCouponCardAbandon,
  fetchCouponCardCodeList,
} from '@/models/card/fetch';
import {
  CouponCardCode,
  CouponCardCodeListParam,
  ExchangeStatusEnum,
} from '@/models/card/interface';
import {
  exchangeStatusNameObj,
  initCouponCardListParam,
} from '@/models/card/util';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  SearchTypeEnum,
  SuitableDeviceTypeEnum,
  WifiSearchTypeEnum,
} from '@/models/device/interface';
import { deviceTypeEnum } from '@/models/device/util';
import { cardApiOption } from '@/services/api/card';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import global from '@/utils/global';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Modal, Popconfirm, Space, message } from 'antd';
import dayjs from 'dayjs';
import { forIn } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import {
  VirtualCardCodeTableForm,
  VirtualCardCodeUrlParam,
} from '../interface';
import { transferCodeUrlParamToCodeListParam } from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<VirtualCardCodeUrlParam>({});
  const [dataList, setDataList] = useState<CouponCardCode[]>([]);
  const [listParam, setListParam] = useState<CouponCardCodeListParam>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const tableFormRef = useRef<ProFormInstance | undefined>();
  const [selectedCodeInfoList, setSelectedCodeInfoList] = useState<
    CouponCardCode[]
  >([]);

  const onTableRowChanged = (
    _: React.Key[],
    selectedRecords: CouponCardCode[],
  ) => {
    setSelectedCodeInfoList(selectedRecords);
  };

  // 获取列表数据
  const requestVirtualCardCodeList = async (param: CouponCardCodeListParam) => {
    setSelectedCodeInfoList([]);
    const { items, ...rest } = await fetchCouponCardCodeList(param);
    setPagination(rest);
    setDataList(items);
  };

  const requestCouponCardAbandon = async (cardCodes: string[]) => {
    const result = await fetchCouponCardAbandon(cardCodes);
    if (result !== true) {
      message.error('操作失败，请重新尝试!');
      return;
    }
    message.success('操作成功');
    if (!listParam) return;
    setListParam({ ...listParam });
  };

  // 废弃
  const dropCodeList = async (recordList: CouponCardCode[]) => {
    if (!recordList || !recordList.length) {
      message.warning(
        '请先选择至少一个未分配或者未兑换的卡号，才可以进行废弃操作。',
      );
      return;
    }
    // console.log(recordList);
    const hasNotUnExchangedOrUnAssignedCodeList = !!recordList.every(
      (record) =>
        record.exchangeStatus !== ExchangeStatusEnum.UNEXCHANGE &&
        record.exchangeStatus !== ExchangeStatusEnum.UNASSIGNED,
    );
    if (hasNotUnExchangedOrUnAssignedCodeList) {
      message.warning(
        '存在非未兑换或者非未分配的卡号信息，请检查后重新进行批量废弃操作',
      );
      return;
    }

    Modal.confirm({
      content: '确定要废弃所选择的卡号么？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        requestCouponCardAbandon(recordList.map((record) => record.cardCode));
      },
    });
  };

  const exportByCodeList = () => {
    const urlSearch = `${location.search}${
      location.search ? '&' : '?'
    }X-Admin-Session=${localStorage.getItem('sessionToken')}`;
    const url = `${location.origin}${getCurrentPrefix()}${
      cardApiOption.couponCardExportByListParam.url
    }${urlSearch}`;
    window.open(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: VirtualCardCodeTableForm = form?.getFieldsValue();
          const param: VirtualCardCodeUrlParam = {};
          // console.log('searchOptionRender', formData);
          forIn(formData, (value, key) => {
            if (
              value !== undefined &&
              value !== null &&
              value !== '' &&
              JSON.stringify(value) !== '[]' &&
              JSON.stringify(value) !== '{}'
            ) {
              if (key === 'exchangedDate' && formData.exchangedDate?.length) {
                param.exchangeStartTime = formData.exchangedDate[0].valueOf();
                param.exchangeEndTime = formData.exchangedDate[1].valueOf();
              } else if (key === 'createDate' && formData.createDate?.length) {
                param.startTime = formData.createDate[0].valueOf();
                param.endTime = formData.createDate[1].valueOf();
              } else {
                (param as any)[key] = value;
              }
            }
          });
          // console.log(param);
          // setUrlParam(param);

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/virtualCard/codeList`,
              param: param as Record<string, any>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();

          // const _urlParam = {};
          // forIn(urlParam, (_, key) => {
          //   _urlParam[key] = undefined;
          // });
          // setUrlParam(_urlParam);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/virtualCard/codeList`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<CouponCardCode>> = [
    {
      title: '卡号',
      dataIndex: 'cardCode',
      width: 180,
    },
    {
      title: '手机号码',
      dataIndex: 'mobile',
      width: 150,
      render: (_, record) => (
        <RedirectLink
          text={record.mobile}
          linkUrl="/user/users"
          params={{ username: record.mobile }}
        />
      ),
    },
    {
      title: '设备SN',
      dataIndex: 'snCode',
      width: 150,
      render: (_, record) =>
        record.snCode ? (
          <RedirectLink
            text={record.snCode}
            linkUrl={`/${record.activatedDeviceType.toLowerCase()}/devices`}
            params={{
              type: global.wifiDeviceList.includes(
                record.activatedDeviceType.toLowerCase(),
              )
                ? WifiSearchTypeEnum.SN
                : SearchTypeEnum.Sn,
              s: record.snCode,
            }}
          />
        ) : (
          '-'
        ),
    },
    {
      title: '激活设备',
      dataIndex: 'activatedDeviceType',
      width: 100,
      valueType: 'select',
      valueEnum: deviceTypeEnum,
    },
    {
      title: '适用设备',
      width: 120,
      dataIndex: 'deviceTypes',
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: deviceTypeEnum,
      render: (_, record) => {
        return (
          <>
            {record.deviceTypes.split(',').map((deviceType) => (
              <div key={deviceType}>{deviceType}</div>
            ))}
          </>
        );
      },
    },
    {
      title: '卡券状态',
      width: 120,
      dataIndex: 'exchangeStatus',
      valueType: 'select',
      valueEnum: exchangeStatusNameObj,
      render: (_, record) => exchangeStatusNameObj[record.exchangeStatus],
    },
    {
      title: '生成方式',
      dataIndex: 'sendWay',
      width: 100,
      search: false,
    },
    {
      title: '兑换时间',
      dataIndex: 'exchangedDate',
      width: 160,
      valueType: 'dateRange',
      render: (_, record) =>
        record.exchangedDate
          ? dayjs(record.exchangedDate).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '生成批次',
      dataIndex: 'batchId',
      width: 160,
    },
    {
      title: '生成时间',
      dataIndex: 'createDate',
      width: 160,
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.createDate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      fixed: 'right',
      title: '操作',
      width: 100,
      search: false,
      dataIndex: 'action',
      render: (_, record) =>
        record.exchangeStatus === ExchangeStatusEnum.UNASSIGNED ||
        record.exchangeStatus === ExchangeStatusEnum.UNEXCHANGE ? (
          <Popconfirm
            title="确定要废弃所选择的卡号么？"
            onConfirm={() => dropCodeList([record])}
          >
            <a>废弃</a>
          </Popconfirm>
        ) : (
          '-'
        ),
    },
  ];

  useEffect(() => {
    if (listParam) requestVirtualCardCodeList(listParam);
  }, [listParam]);

  useEffect(() => {
    if (JSON.stringify(urlParam) === '{}') {
      setListParam({ ...initCouponCardListParam });
      return;
    }

    const tableForm: VirtualCardCodeTableForm = {};
    const form = tableFormRef.current;
    if (!form) return;
    forIn(urlParam, (value, key) => {
      if (key === 'startTime') {
        tableForm.createDate = tableForm.createDate || [];
        tableForm.createDate.unshift(dayjs(+value));
      } else if (key === 'endTime') {
        tableForm.createDate = tableForm.createDate || [];
        tableForm.createDate.push(dayjs(+value));
      } else if (key === 'exchangeStartTime') {
        tableForm.exchangedDate = tableForm.exchangedDate || [];
        tableForm.exchangedDate.unshift(dayjs(+value));
      } else if (key === 'exchangeEndTime') {
        tableForm.exchangedDate = tableForm.exchangedDate || [];
        tableForm.exchangedDate.push(dayjs(+value));
      } else if (key === 'deviceTypes') {
        tableForm.deviceTypes = decodeURIComponent(urlParam.deviceTypes).split(
          ',',
        ) as SuitableDeviceTypeEnum[];
      } else {
        tableForm[key] = value;
      }
    });
    form.setFieldsValue(tableForm);

    const param: CouponCardCodeListParam = {
      ...transferCodeUrlParamToCodeListParam(urlParam),
    };
    setListParam(param);
  }, [urlParam]);

  return (
    <ProTable<CouponCardCode>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="cardCode"
      formRef={tableFormRef}
      rowSelection={{
        fixed: true,
        selectedRowKeys: selectedCodeInfoList.map(
          (codeInfo) => codeInfo.cardCode,
        ),
        onChange: onTableRowChanged,
        getCheckboxProps: (record) => {
          return {
            disabled:
              record.exchangeStatus !== ExchangeStatusEnum.UNASSIGNED &&
              record.exchangeStatus !== ExchangeStatusEnum.UNEXCHANGE,
          };
        },
      }}
      tableAlertOptionRender={() => {
        return (
          <Space size={16}>
            <a onClick={() => setSelectedCodeInfoList([])}>取消选择</a>
          </Space>
        );
      }}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{
        reload: () => {
          if (listParam) setListParam({ ...listParam });
        },
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            onClick={() => exportByCodeList()}
          >
            导出
          </Button>,
          <Button
            key="batch-drop"
            onClick={() => dropCodeList(selectedCodeInfoList)}
          >
            批量废弃
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
