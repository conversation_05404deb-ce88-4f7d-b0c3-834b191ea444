import { DomainTypeEnum } from '@/utils/targetDomain';

export const getSourceDomainToken = (domainType: DomainTypeEnum) => {
  let tokenKey = 'sessionToken';
  if (domainType !== DomainTypeEnum.CURRENT) {
    // 将tokenKey类型首字母大写
    const baseToken = tokenKey.charAt(0).toUpperCase() + tokenKey.slice(1);
    tokenKey = `${domainType}${baseToken}`;
  }

  const sessionToken = localStorage.getItem(tokenKey);
  return sessionToken;
};

export const setSourceDomainToken = (
  domainType: DomainTypeEnum,
  session: string,
) => {
  let tokenKey = 'sessionToken';
  if (domainType !== DomainTypeEnum.CURRENT) {
    // 将tokenKey类型首字母大写
    const baseToken = tokenKey.charAt(0).toUpperCase() + tokenKey.slice(1);
    tokenKey = `${domainType}${baseToken}`;
  }

  console.log('before set session', tokenKey, session);

  const sessionToken = localStorage.setItem(tokenKey, session);
  return sessionToken;
};
