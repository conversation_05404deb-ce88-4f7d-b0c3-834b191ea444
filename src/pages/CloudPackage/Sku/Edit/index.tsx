import CurrencyPriceForm from '@/components/CurrencyPriceForm';
import SkuSelector from '@/components/SkuSelector';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';

import { fetchBenefitList } from '@/api/benefit/fetch';
import { BenefitAttribute, BenefitInfo } from '@/api/benefit/types';
import Uploader from '@/components/Uploader';
import {
  ApiSuccessEnum,
  ArrElement,
  StatusEnum,
} from '@/models/common.interface';
import { getCycleTimeOptionList } from '@/models/common.util';
import { fetchDeviceCapacityList } from '@/models/device/fetch';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  deviceTypeOptions,
  initDeviceCapacityListParam,
} from '@/models/device/util';
import { getSourceDomainToken } from '@/models/login/util';
import {
  fetchForwardSyncSku,
  fetchProductSkuCreation,
  fetchProductSkuDetail,
  fetchProductSkuUpdating,
} from '@/models/product/fetch';
import {
  ProductSku,
  ProductSkuBenefit,
  ReNewEnum,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { initRelationProductSkuListParam } from '@/models/product/util';
import Login from '@/pages/Login/Login';
import { initPaginatorParam } from '@/utils/request';
import { DomainTypeEnum, domainTypeInfo } from '@/utils/targetDomain';
import useUrlState from '@ahooksjs/use-url-state';
import { InfoCircleFilled, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

import { BooleanEnum, ResultCodeEnum } from '@/models/common.enum';
import { history, useParams } from '@umijs/max';
import {
  Alert,
  Button,
  Checkbox,
  Col,
  Empty,
  Form,
  FormProps,
  Image,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Typography,
  message,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { CSSProperties, useEffect, useMemo, useState } from 'react';
import { ProductSkuForm, UrlParam } from '../interface';
import {
  initialProductSkuForm,
  serviceTimeUnitObj,
  transferDetailToFormData,
  transferFormDataToParam,
  transferFormDataToSyncSkuParam,
} from '../util';

const Edit: React.FC = () => {
  const param = useParams<{ id: string }>();
  const [urlParam] = useUrlState<UrlParam>();
  const [form] = Form.useForm<ProductSkuForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 20 },
  };
  const proCardStyle: CSSProperties = {
    marginBlockEnd: 16,
  };

  const levelList = [0, 1, 2, 3, 4];
  const [detail, setDetail] = useState<ProductSku>();
  const [showSkuSelector, setShowSkuSelector] = useState(false);
  const [associateSkuList, setAssociateSkuList] = useState<ProductSku[]>([]);
  const [associateSkuListParam, setAssociateSkuListParam] =
    useState<RelationProductSkuListParam>(initRelationProductSkuListParam);
  const [benefitList, setBenefitList] = useState<ProductSkuBenefit[]>([]);

  const [showLoginModal, setShowLoginModal] = useState(false);

  const isAutoRenew = Form.useWatch('isAutoRenew', form);
  const isFirstPreferential = Form.useWatch('isFirstPreferential', form);
  const serviceTimeUnit = Form.useWatch('serviceTimeUnit', form);
  // const price = Form.useWatch('price', form);
  const deviceType = Form.useWatch('deviceType', form);
  const skuCapacities = Form.useWatch('skuCapacities', form);

  const isEditing = useMemo(() => {
    return !!(param.id && +param.id);
  }, [param]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    if (!id) return;
    const detail = await fetchProductSkuDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);

    // 处理权益数据，确保包含属性和选中值
    const processedBenefits = detail.benefits.map((benefit) => {
      // 获取默认属性
      const defaultAttr = benefit.attributes?.find(
        (attr) => attr.isDefaultAttribute === BooleanEnum.TRUE,
      );

      // 确保每个权益都有独立的selectedValue
      let selectedValue = benefit.selectedValue;
      if (!selectedValue && defaultAttr) {
        selectedValue = defaultAttr.attributeText;
      }

      return {
        ...benefit,
        selectedValue: selectedValue,
      };
    });
    setBenefitList(processedBenefits);
    setDetail(detail);

    if (urlParam.source !== DomainTypeEnum.CURRENT && !!+urlParam.isCopy)
      return;

    if (!detail.relationSkuId) return;

    const relationDetail = await fetchProductSkuDetail(detail.relationSkuId);
    setAssociateSkuList([relationDetail]);
  };

  // 根据设备型号获取对应的权益列表
  const requestDeviceBenefitList = async (
    _deviceType: SuitableDeviceTypeEnum,
  ) => {
    try {
      // 获取设备对应的权益列表
      const benefitListData = await fetchBenefitList({
        limit: 1000,
        offset: 0,
        deviceType: _deviceType,
      });

      if (!benefitListData || !benefitListData.length) {
        setBenefitList([]);
        return;
      }

      // 转换权益数据为SKU权益格式
      const productSkuBenefitList: ProductSkuBenefit[] = benefitListData.map(
        (benefit: BenefitInfo) => {
          // 查找是否已存在的权益，保持原有配置
          const existingBenefit = benefitList.find(
            (existing) => existing.id === parseInt(benefit.groupId),
          );

          // 获取默认属性
          const defaultAttr = benefit.attributes?.find(
            (attr: BenefitAttribute) =>
              attr.isDefaultAttribute === StatusEnum.ENABLE,
          );

          // 确保每个权益都有独立的selectedValue
          let selectedValue = existingBenefit?.selectedValue;
          if (!selectedValue && defaultAttr) {
            selectedValue = defaultAttr.attributeText;
          }

          return {
            id: parseInt(benefit.groupId),
            name: benefit.name,
            icon: benefit.icon,
            description: benefit.description,
            image: benefit.image,
            handpick: existingBenefit?.handpick || false,
            isCoreBenefit: benefit.isCoreBenefit,
            attributes: benefit.attributes.map((attr: BenefitAttribute) => ({
              benefitId: attr.benefitId,
              isDefaultAttribute:
                attr.isDefaultAttribute === StatusEnum.ENABLE
                  ? BooleanEnum.TRUE
                  : BooleanEnum.FALSE,
              attributeType: attr.attributeType,
              attributeText: attr.attributeText,
              attributeSelectedText: attr.attributeSelectedText,
            })),
            selectedValue: selectedValue,
          } as ProductSkuBenefit;
        },
      );

      setBenefitList(productSkuBenefitList);
    } catch (error) {
      console.error('获取设备权益列表失败:', error);
      setBenefitList([]);
    }
  };

  // 根据设备型号获取对应SKU能力和权益
  const requestDeviceCapacityList = async (
    _deviceType: SuitableDeviceTypeEnum,
  ) => {
    // 调用此接口时，则说明formData中的deviceType已经被更新了
    const { items } = await fetchDeviceCapacityList({
      ...initDeviceCapacityListParam,
      deviceType: _deviceType,
    });
    if (!form) return;
    if (!items || !items.length) {
      form.setFieldValue('skuCapacities', []);
      return;
    }
    const _skuCapacities: ProductSkuForm['skuCapacities'] =
      items[0].allCapacities.map((item) => {
        const initCapacity = items[0].initialCapacities.find(
          (cap) => cap.type === item.type,
        );
        const currentSkuCapacity = (detail?.capacities || []).find(
          (capacity) => capacity.type === item.type,
        );
        const skuCapacityForm: ArrElement<ProductSkuForm['skuCapacities']> = {
          type: item.type,
          name: item.name,
          minCycleTime: initCapacity?.cycleTime || 0,
          cycleTime: currentSkuCapacity?.cycleTime || null,
          enable: !!currentSkuCapacity,
        };
        return skuCapacityForm;
      });
    // console.log(detail?.capacities, _skuCapacities);
    form.setFieldValue('skuCapacities', _skuCapacities || []);

    // 自动获取该设备下的所有权益数据
    await requestDeviceBenefitList(_deviceType);
  };

  const onSubmitFail: FormProps['onFinishFailed'] = ({ errorFields }) => {
    if (
      errorFields[0].name[0] === 'skuCapacities' &&
      errorFields[0].name.length === 1
    ) {
      form.scrollToField(['skuCapacities', 0, 'enable'], {
        behavior: 'smooth',
      });
      return;
    }
    form.scrollToField(errorFields[0].name, { behavior: 'smooth' });
  };

  // 当前环境的创建和更新SKU
  const requestSaveForCurrent = async () => {
    const formData = form.getFieldsValue();
    let _param = transferFormDataToParam(
      formData,
      associateSkuList,
      benefitList,
      +(param?.id || 0) || 0,
    );
    console.log('submit', formData, _param, benefitList);
    return;

    setLoading(true);
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchProductSkuUpdating(_param);
      } else {
        state = '创建';
        result = await fetchProductSkuCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const requestSyncSave = async () => {
    const formData = form.getFieldsValue();
    const _param = transferFormDataToParam(
      formData,
      associateSkuList,
      benefitList,
    );
    const syncSkuParam = transferFormDataToSyncSkuParam(_param, benefitList);

    setLoading(true);
    try {
      const result = await fetchForwardSyncSku(syncSkuParam, urlParam.source);

      if (result === ApiSuccessEnum.success) {
        message.success(
          `已成功创建到${domainTypeInfo[urlParam.source as DomainTypeEnum]}！`,
        );
        history.back();
      }
    } catch (error: any) {
      console.log(error);
      const {
        response: { code },
      } = error;
      if (code === ResultCodeEnum.SESSION_EXPIRED) {
        // 登录过期重新进行登录目标服务器
        setShowLoginModal(true);
      }
    } finally {
      setLoading(false);
    }
  };

  // 提交form表单
  const submit = async (formData: ProductSkuForm) => {
    if (!formData.serviceTimeUnit) {
      message.warning('请选择服务时长的单位');
      return;
    }

    // 编辑ID不为0，或者source是当前域名时，走正常的保存流程
    if (
      (param && +(param.id || 0)) ||
      urlParam.source === DomainTypeEnum.CURRENT
    ) {
      requestSaveForCurrent();
      return;
    }

    if (!getSourceDomainToken(urlParam.source)) {
      setShowLoginModal(true);
      return;
    }

    requestSyncSave();
  };

  const closeLoginModal = (isLogged?: boolean) => {
    setShowLoginModal(false);
    if (!isLogged) {
      message.warning('用户取消了同步SKU');
      return;
    }

    requestSyncSave();
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const showProductSelector = (formData: ProductSkuForm) => {
    const {
      serviceTimeUnit: _serviceTimeUnit,
      deviceType,
      skuCapacities,
      level,
    } = formData;
    if (
      !skuCapacities.filter(
        (skuCapacity) => skuCapacity.enable && skuCapacity.cycleTime,
      ).length
    ) {
      message.warning('存在未配置循环周期的能力！');
      return;
    }
    if (!deviceType || !deviceType.length) {
      message.warning('请先选择适用设备！');
      return;
    }
    if (level === undefined || level === null) {
      message.warning('请先选择权益优先级！');
      return;
    }
    const _param: RelationProductSkuParam = {
      isReNew: ReNewEnum.RENEW,
      uniteDeviceTypes: [deviceType],
      uniteCapacities: skuCapacities
        .filter((capacity) => capacity.enable && capacity.cycleTime)
        .map((capacity) => ({
          type: capacity.type,
          cycleTime: capacity.cycleTime || 0,
        })),
      serviceTime: 1,
      serviceTimeUnit: _serviceTimeUnit,
      level,
    };
    setAssociateSkuListParam({
      ...initPaginatorParam,
      payload: _param,
    });
    setShowSkuSelector(true);
  };

  // 精选权益操作
  const handpickProductSkuBenefit = (
    handpick: boolean,
    benefit: ProductSkuBenefit,
  ) => {
    benefit.handpick = handpick;
    setBenefitList([...benefitList]);
  };

  // 删除关联权益
  const removeProductSkuBenefit = (benefit: ProductSkuBenefit) => {
    const index = benefitList.findIndex((bf) => bf.id === benefit.id);
    benefitList.splice(index, 1);
    setBenefitList([...benefitList]);
  };

  const associateBenefitTableColumns: ColumnsType<ProductSkuBenefit> = [
    {
      title: '权益名称',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '是否核心权益',
      dataIndex: 'isCoreBenefit',
      width: 120,
      render: (_, row) => <span>{row.isCoreBenefit === 1 ? '是' : '否'}</span>,
    },
    {
      title: '权益描述',
      dataIndex: 'description',
      width: 150,
    },
    {
      title: '权益配图',
      dataIndex: 'image',
      width: 120,
      render: (_, row) => <Image width={100} alt="权益配图" src={row.image} />,
    },
    {
      title: '精选',
      dataIndex: 'handpick',
      width: 80,
      render: (_, row) => (
        <Switch
          unCheckedChildren="否"
          checkedChildren="是"
          checked={row.handpick}
          onChange={(ev) => handpickProductSkuBenefit(ev, row)}
        ></Switch>
      ),
    },
    {
      title: '值',
      width: 150,
      dataIndex: 'selectedValue',
      render: (_, row) => {
        const defaultAttr = row.attributes?.find(
          (attr) => attr.isDefaultAttribute === 1,
        );
        // 确保每行都有独立的选中值
        const currentValue = row.selectedValue || defaultAttr?.attributeText;

        return (
          <Select
            key={`benefit-select-${row.id}`}
            style={{ width: '100%' }}
            value={currentValue}
            placeholder="请选择值"
            onChange={(value) => {
              // 更新权益的选中值，确保每行独立操作
              const updatedBenefitList = benefitList.map((benefit) =>
                benefit.id === row.id
                  ? { ...benefit, selectedValue: value }
                  : benefit,
              );
              setBenefitList(updatedBenefitList);
            }}
            options={
              row.attributes?.map((attr) => ({
                label: attr.attributeText,
                value: attr.attributeText,
              })) || []
            }
          />
        );
      },
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'action',
      render: (_, row) => (
        <Space>
          <Popconfirm
            title="确定删除该行所关联的权益么？"
            onConfirm={() => removeProductSkuBenefit(row)}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const onServiceTimeUnitChange = (value: ServiceTimeUnitEnum) => {
    // 如果时长单位为日时，则需要禁用自动续费的操作
    if (value === ServiceTimeUnitEnum.DAY) {
      form.setFieldValue('isAutoRenew', false);
    }
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    if (urlParam.skuId) {
      requestDetailById(urlParam.skuId);
    }
  }, [urlParam]);

  useEffect(() => {
    if (deviceType) requestDeviceCapacityList(deviceType);
  }, [deviceType]);

  return (
    <>
      <Form
        {...layout}
        form={form}
        layout="vertical"
        onFinish={submit}
        onFinishFailed={onSubmitFail}
        initialValues={initialProductSkuForm}
      >
        <ProCard title="SKU内容" style={proCardStyle}>
          <Row>
            <Col span={8}>
              <Form.Item
                name="name"
                label="SKU名称"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input showCount maxLength={15} placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="simpleName"
                label="SKU简称"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入SKU简称' }]}
              >
                <Input showCount maxLength={6} placeholder="请输入SKU简称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="aliasName"
                label="SKU别名"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入SKU别名' }]}
              >
                <Input maxLength={50} showCount placeholder="请输入SKU别名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="level"
                label="权益优先级"
                rules={[{ required: true, message: '请选择权益优先级' }]}
              >
                <Select
                  placeholder="请选择权益优先级"
                  // disabled={isEditing}
                  options={levelList.map((level) => ({
                    value: level,
                    label: level,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sort"
                label="优先级"
                rules={[{ required: true, message: '请输入优先级' }]}
                extra={
                  <Alert message="数字越大，优先级越高" type="info" showIcon />
                }
              >
                <InputNumber
                  min={0}
                  max={99999}
                  step={1}
                  // 转为整数
                  formatter={(value: number | undefined) => {
                    return value || value === 0
                      ? `${parseInt(`${value || ''}`)}`
                      : '';
                  }}
                  placeholder="请输入优先级"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="serviceTimeUnit"
                label="服务时长"
                rules={[{ required: true }]}
              >
                <Radio.Group
                  onChange={(ev) => onServiceTimeUnitChange(ev.target.value)}
                >
                  {Object.keys(serviceTimeUnitObj).map((index) => (
                    <Radio key={index} value={index} disabled={isEditing}>
                      {serviceTimeUnitObj[index as ServiceTimeUnitEnum]}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="deviceType"
                label="适用设备"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请选择适用设备' }]}
              >
                <Select
                  disabled={isEditing}
                  options={deviceTypeOptions}
                  placeholder="请选择适用设备"
                />
              </Form.Item>
            </Col>
          </Row>
        </ProCard>
        <ProCard
          title={
            <>
              <span style={{ marginInlineEnd: 4, color: '#ff4d4f' }}>*</span>
              SKU能力
            </>
          }
          style={proCardStyle}
        >
          <Form.List
            name="skuCapacities"
            rules={[
              {
                validator: async (
                  _,
                  fields: ProductSkuForm['skuCapacities'],
                ) => {
                  if (
                    !fields ||
                    !fields.length ||
                    fields.every((field) => !field.enable)
                  ) {
                    return Promise.reject(
                      new Error('请选择至少一种能力和其循环周期'),
                    );
                  }
                },
              },
            ]}
          >
            {(fields, _, { errors }) =>
              fields && fields.length ? (
                <>
                  {fields.map((field, index) => (
                    <Form.Item key={field.key}>
                      <Row justify="space-between" align="bottom">
                        <Col span={12}>
                          <Form.Item
                            name={[field.name, 'enable']}
                            valuePropName="checked"
                          >
                            <Checkbox
                              disabled={isEditing}
                              onChange={() =>
                                form.validateFields(['skuCapacities'])
                              }
                            >
                              {skuCapacities[index].name}
                            </Checkbox>
                          </Form.Item>{' '}
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name={[field.name, 'cycleTime']}
                            label="循环周期（单位：日）"
                            rules={[
                              {
                                required: skuCapacities[index].enable,
                                message: '请选择循环周期',
                              },
                            ]}
                          >
                            <Select
                              disabled={isEditing}
                              placeholder="请选择循环周期"
                              options={getCycleTimeOptionList(
                                skuCapacities[index].minCycleTime,
                              )}
                              onChange={() =>
                                form.validateFields(['skuCapacities'])
                              }
                            ></Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form.Item>
                  ))}
                  <Form.Item>
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="请先选择适用设备后再配置SKU能力"
                />
              )
            }
          </Form.List>
        </ProCard>
        <ProCard
          title={
            <>
              <span style={{ marginInlineEnd: 4, color: '#ff4d4f' }}>*</span>
              SKU权益
              {!deviceType && (
                <span
                  style={{ color: '#999', fontSize: '12px', marginLeft: 8 }}
                >
                  (请先选择适用设备)
                </span>
              )}
            </>
          }
          style={proCardStyle}
        >
          {benefitList && benefitList.length ? (
            <Table<ProductSkuBenefit>
              rowKey="id"
              columns={associateBenefitTableColumns}
              scroll={{
                x: associateSkuTableColumns
                  .filter((col) => col.dataIndex === 'action')
                  .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
              }}
              dataSource={benefitList}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无权益信息"
            />
          )}
        </ProCard>
        <ProCard title="SKU价格" style={proCardStyle}>
          <Form.Item
            label=""
            name="currencyPrice"
            wrapperCol={{ span: 24 }}
            rules={[
              {
                validator: (_, value) => {
                  const { price, linePrice } = value;
                  if (price === undefined || price === null) {
                    return Promise.reject(new Error('价格不能为空'));
                  }
                  if (linePrice && price > linePrice) {
                    return Promise.reject(
                      new Error('划线价格必须大于或者等于价格'),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <CurrencyPriceForm />
          </Form.Item>
          <Form.Item
            name="isAutoRenew"
            label="自动续费"
            valuePropName="checked"
            required
          >
            <Switch
              disabled={
                isEditing || serviceTimeUnit === ServiceTimeUnitEnum.DAY
              }
              unCheckedChildren="关闭"
              checkedChildren="开启"
            />
          </Form.Item>
          {isAutoRenew && (
            <Form.Item
              name="isFirstPreferential"
              label="首期特惠"
              required
              valuePropName="checked"
              extra={
                <Typography.Text type="secondary">
                  如连续包月原价 40 元，首月 35
                  元，后续按原价续费；或连续包年原价 299 元，首年 135
                  元，后续按原价续费；
                </Typography.Text>
              }
            >
              <Switch unCheckedChildren="关闭" checkedChildren="开启" />
            </Form.Item>
          )}
          {isAutoRenew && isFirstPreferential && (
            <Form.Item
              name="preferentialPrice"
              label="优惠价格(元)"
              required
              rules={
                [
                  // { required: true, message: '请输入首期特惠价格' },
                  // {
                  //   max: price,
                  //   type: 'number',
                  //   message: `首期价格不能高于${(price || 0).toFixed(2)}`,
                  // },
                  // {
                  //   min: 0,
                  //   type: 'number',
                  //   message: '请输入大于0的数，最小值为0.01',
                  // },
                ]
              }
            >
              {/* <InputNumber
                style={{ width: '100%' }}
                prefix="￥"
                precision={2}
                min={0.01}
                placeholder="请输入优惠价格"
              /> */}
              <CurrencyPriceForm formItems={['price']} />
            </Form.Item>
          )}
        </ProCard>
        <ProCard title="营销设置" style={proCardStyle}>
          <Form.Item
            name="cornerIcons"
            label="角标"
            shouldUpdate
            getValueFromEvent={normFile}
            valuePropName="fileList"
            extra={
              <Typography.Text type="secondary">
                <InfoCircleFilled
                  style={{ color: '#1890ff', marginRight: 8 }}
                />
                SKU引流图标，高度为30px或其倍数
              </Typography.Text>
            }
          >
            <Uploader maxCount={3} />
          </Form.Item>
          {/* <Form.Item name="description" label="SKU说明">
            <Input maxLength={500} showCount placeholder="请输入SKU说明" />
          </Form.Item> */}
        </ProCard>
        {/* 非自动续费套餐 不需要关联SKU */}
        {!isAutoRenew ? (
          <ProCard title="其他设置" style={proCardStyle}>
            <Form.Item
              label="关联SKU"
              extra={
                <Typography.Text type="secondary">
                  <InfoCircleFilled
                    style={{ color: '#1890ff', marginRight: 8 }}
                  />
                  在续费时自动关联对应的自动续费SKU
                </Typography.Text>
              }
            >
              <Button
                type="link"
                icon={<PlusOutlined />}
                onClick={() => showProductSelector(form?.getFieldsValue())}
              >
                去关联SKU
              </Button>
            </Form.Item>
            {associateSkuList && associateSkuList.length ? (
              <Table<ProductSku>
                caption="已关联的SKU"
                rowKey="id"
                columns={(
                  associateSkuTableColumns as ColumnsType<ProductSku>
                ).concat([
                  {
                    title: '状态',
                    dataIndex: 'saleStatus',
                    fixed: 'right',
                    width: 100,
                    render: (_, row) => (row.saleStatus ? '上架' : '下架'),
                  },
                  {
                    title: '操作',
                    dataIndex: 'saleStatus',
                    fixed: 'right',
                    width: 100,
                    render: () => (
                      <Popconfirm
                        title="确定要删除么？"
                        onConfirm={() => setAssociateSkuList([])}
                      >
                        <Button type="link">删除</Button>
                      </Popconfirm>
                    ),
                  },
                ])}
                scroll={{
                  x: associateSkuTableColumns
                    .filter((col) => col.dataIndex === 'action')
                    .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
                }}
                dataSource={associateSkuList}
              />
            ) : (
              ''
            )}
          </ProCard>
        ) : null}
        <ProCard style={proCardStyle}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            style={{ textAlign: 'center', marginBottom: 0 }}
          >
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              {urlParam.source === DomainTypeEnum.CURRENT || !urlParam.source
                ? '提交'
                : `创建到${domainTypeInfo[urlParam.source as DomainTypeEnum]}`}
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </ProCard>
      </Form>
      {showSkuSelector ? (
        <SkuSelector
          param={associateSkuListParam}
          open
          onOk={(ev) => setAssociateSkuList([ev])}
          onCancel={() => setShowSkuSelector(false)}
        />
      ) : null}

      {showLoginModal ? (
        <Modal
          title={`登录${domainTypeInfo[urlParam.source as DomainTypeEnum]}`}
          open
          width={600}
          footer={null}
          onCancel={() => setShowLoginModal(false)}
        >
          <Login
            domainType={urlParam.source as DomainTypeEnum}
            onClose={closeLoginModal}
          />
        </Modal>
      ) : null}
    </>
  );
};

export default Edit;
