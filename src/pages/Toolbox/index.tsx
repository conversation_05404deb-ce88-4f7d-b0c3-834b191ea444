import { BreadcrumbInfo } from '@/models/common.interface';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import CompleteOrder from './CompleteOrder';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Toolbox: React.FC = () => {
  const location = useLocation();
  const [title] = useState(`工具箱`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    // const _breadcrumbInfoList = global.getBreadcrumbInfo(breadcrumbInfo, location);
    const _breadcrumbInfoList = [
      { ...breadcrumbInfo, breadcrumbName: '工具箱' },
    ];

    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.breadcrumbName}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <CompleteOrder />
    </PageContainer>
  );
};

export default Toolbox;
