import { StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';
import { BooleanEnum } from '../common.enum';
import { BasicCurrency } from '../currency/interface';
import { SuitableDeviceTypeEnum } from '../device/interface';

export enum ReNewEnum {
  RENEW = 1,
  NOT_RENEW = 0,
}

export enum SaleStatusEnum {
  ON_SALE = 1,
  OFF_SALVE = 0,
}

export enum ProductSkuCapacityTypeEnum {
  CLOUD_STORAGE = 'CLOUD_STORAGE',
  SPLENDID_MOMENT = 'SPLENDID_MOMENT',
  TIME_LAPSE = 'TIME_LAPSE',
}

export enum SkuTypeEnum {
  EVENT = 'EVENT',
  CVR = 'CVR',
  BASIC = 'BASIC',
}

export enum RecordTypeEnum {
  EVENT = 'EVENT',
  CVR = 'CVR',
}

export enum ServiceTimeUnitEnum {
  YEAR = 'YEAR',
  MONTH = 'MONTH',
  DAY = 'DAY',
}

// 列表参数数据
export interface ProductSkuListParam extends PaginatorParam {
  skuId?: number;
  skuName?: string;
  skuShortName?: string;
  skuAlias?: string;
  capacity?: string;
  isReNew?: ReNewEnum;
  saleStatus?: SaleStatusEnum;
  deviceType?: string;
  // 优先级筛选条件
  sort?: number;
  level?: number;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
}

// 列表数据
export interface ProductSku {
  id: number;
  name: string;
  shortName: string;
  aliasName: string;
  saleStatus: boolean;
  capacities: Array<{
    type: string;
    cycleTime: number;
    name: string;
  }>;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceTypes: SuitableDeviceTypeEnum[];
  price: {
    price: number;
    linePrice: number;
    firstPhasePrice: number;
    isReNew: boolean;
    priceList: OtherCurrencySkuPrice[];
    firstPhasePriceList: OtherCurrencySkuPrice[];
  };
  cornerMarkIcon: string;
  description: string;
  createTime: number;
  relationSkuId: number;
  skuType: SkuTypeEnum;
  // sku等级
  level: number;
  // 优先级
  sort: number;
  benefits: ProductSkuBenefit[];
  // SKU关联关系
  skuRelations?: Array<{
    skuId: number;
    skuType: string;
  }>;
}

export interface ProductSkuBenefit
  extends Omit<Benefit, 'status' | 'createTime' | 'updateTime'> {
  handpick: boolean;
  isCoreBenefit?: number;
  attributes?: Array<{
    benefitId: number;
    isDefaultAttribute: BooleanEnum;
    attributeType: number;
    attributeText: string;
    attributeSelectedText: string;
  }>;
  selectedValue?: string;
}

// 创建/编辑提交参数
export interface ProductSkuParam {
  id?: number;
  name: string;
  shortName: string;
  aliasName?: string;
  level: number;
  sort: number;
  benefits: ProductSkuBenefitParam[];
  capacities: ProductSkuCapacityParam[];
  serviceTime: number;
  serviceTimeUnit: string;
  deviceTypes?: SuitableDeviceTypeEnum[];
  price: ProductSkuPriceParam;
  cornerMarkIcon: string;
  // description: string;
  relationSkuId?: number;
  skuRelations?: Array<{
    skuId: number;
    skuType: string;
  }>;
}

export interface ProductSkuCapacityParam {
  type: string;
  cycleTime?: number;
}

export interface ProductSkuPriceParam {
  price: number;
  linePrice?: number;
  firstPhasePrice?: number;
  isReNew: ReNewEnum;
  priceList: OtherCurrencySkuPriceListParam[];
  firstPhasePriceList: OtherCurrencySkuPriceListParam[];
}

export interface ProductSkuBenefitParam {
  id: number;
  sort: number;
  handpick: boolean;
  selectedValue?: string;
}

// 详情数据
export type ProductSkuDetail = ProductSku;

// 修改SKU日志数据结构
export interface ProductSkuUpdatingLog {
  id: number;
  skuId: number;
  operateType: string;
  modifiedTime: number;
  field: string;
  before: string;
  after: string;
}

export interface RelationProductSkuParam {
  uniteDeviceTypes: SuitableDeviceTypeEnum[];
  uniteCapacities: ProductSkuCapacityParam[];
  isReNew?: ReNewEnum;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
  level?: number | null;
  skuAlias?: string;
  skuId?: number;
  skuName?: string;
  saleStatus?: SaleStatusEnum;
  price?: number;
}

export interface RelationProductSkuListParam extends PaginatorParam {
  payload: RelationProductSkuParam;
}

// 变更历史相关接口参数和返回值
export interface ProductSkuUpdatingLogsParam extends PaginatorParam {
  skuId: number;
}

export interface ProductSkuOperationHistory {
  id: number;
  skuId: number;
  operatorName: string;
  modifyTime: string;
  field: string;
  before: string;
  after: string;
  operateType: string;
}

/**
 * =====================
 * 权益相关接口参数
 */
export interface BenefitListParam extends PaginatorParam {
  name?: string;
  status?: StatusEnum;
}
export interface Benefit {
  id: number;
  name: string;
  icon: string;
  description: string;
  image: string;
  status: StatusEnum;
  createTime: number;
  updateTime: number;
  distributedUniqueId?: string | null;
}

export type BenefitDetail = Benefit;

export interface BenefitParam {
  id?: number;
  name: string;
  icon: string;
  description: string;
  image: string;
}
/**
 * =====================
 */

/**
 * =====================
 * SKU能力相关接口参数
 */
export interface Capacity {
  id: number;
  name: string;
  benefits: Array<{ id: number; name: string }>;
  createTime: number;
  updateTime: number;
}

export type CapacityDetail = Capacity;

export interface CapacityParam {
  id: number;
  benefits: string;
}
/**
 * =====================
 */

/**
 * ************************************************************************
 * 特权对比相关接口
 */
export interface BenefitCompareListParam extends PaginatorParam {
  deviceType?: string;
  status?: boolean;
}

export interface BenefitCompare {
  id: number;
  deviceType: string;
  image: string;
  status: boolean;
  createTime: number;
  updateTime: number;
}

export interface BenefitCompareParam {
  id?: number;
  deviceType: string;
  image: string;
}

export interface BenefitCompareUpdateStatusParam {
  id: number;
  status: boolean;
}
/**
 * ************************************************************************
 */

/**
 * ************************************************************************
 * sku价格列表
 */
export interface OtherCurrencySkuPriceListParam {
  price: number;
  linePrice?: number;
  areaCode?: string[];
}

export type OtherCurrencySkuPrice = BasicCurrency & {
  price: number;
  linePrice?: number;
};
/**
 * ************************************************************************
 */

export interface SyncSkuParam extends ProductSkuParam {
  benefitList: Omit<Benefit, 'status' | 'createTime' | 'updateTime'>[];
}
