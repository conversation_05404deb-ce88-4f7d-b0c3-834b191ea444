import { PaginatorParam } from '@/utils/request';
import { StatusEnum } from '../common.interface';
import { Currency } from '../currency/interface';
import { SuitableDeviceTypeEnum } from '../device/interface';
import {
  OtherCurrencySkuPrice,
  ServiceTimeUnitEnum,
} from '../product/interface';

export enum ActTimeEnum {
  CUSTOM = 'CUSTOM',
  FOREVER = 'FOREVER',
}

export enum ActTypeEnum {
  /**
   * 60天早鸟活动
   */
  EARLY_BIRD_SISTY_DAY = 'EARLY_BIRD_SISTY_DAY',
  /**
   * 新设备7天优惠
   */
  // NEW_DEVICE_SEVEN_DAY = 'NEW_DEVICE_SEVEN_DAY',
  /**
   * 免费活动
   */
  FREE_ACTIVITY = 'FREE_ACTIVITY',
  /**
   * 推广活动
   */
  PROMOTION_ACTIVITY = 'PROMOTION_ACTIVITY',
}

// 列表参数数据
export interface OperationPackageListParam extends PaginatorParam {
  saleStatus?: StatusEnum;
  deviceType?: SuitableDeviceTypeEnum;
  actName?: string;
}

export interface OperationPackage {
  id: number;
  deviceType: SuitableDeviceTypeEnum;
  actContent: string;
  actName: string;
  actProp: string;
  priority: number;
  actTimeEnum: ActTimeEnum;
  effectDate: string | null;
  inEffectDate: string | null;
  saleStatus: StatusEnum;
  relationSkuId: number;
  actNum: number;
}

export interface OperationPackagePayloadParam {
  id?: number;
  deviceType: SuitableDeviceTypeEnum;
  actProp: string;
  actPropId: number;
  actContent: string;
  priority: number;
  actName: string;
  relationSkuId: number;
  actTime: ActTimeEnum;
  effectTime?: number | null;
  inEffectTime?: number | null;
  imageParam: {
    popChineseImage: string;
    actChineseImage: string;
    popEnglishImage: string;
    actEnglishImage: string;
  };
  // 是否更改sku
  changeSku: boolean;
  // 活动SKU名称
  actSkuName?: string;
  // 活动SKU服务时长
  serviceTime?: number;
  // 活动SKU服务时长单位
  serviceTimeUnit?: string;
  // 活动价格
  actPrice?: {
    price: number;
    actPriceList: OtherCurrencySkuPrice[];
  };
  // 活动角标
  cornerMarkIcon?: string;
}

export interface OperationPackageParam {
  payload: OperationPackagePayloadParam;
}

// 详情数据
export interface OperationPackageDetail extends OperationPackage {
  relationSkuId: number;
  actSkuName: string;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  actPrice: number;
  cornerMarkIcon: string;
  actTimeEnum: ActTimeEnum;
  actPropId: number;
  changeSku: StatusEnum;
  imageRet: {
    id: number;
    actChineseImage: string;
    actEnglishImage: string;
    popChineseImage: string;
    popEnglishImage: string;
  };
  price: {
    amount: number;
    currency: Currency;
    priceList: OtherCurrencySkuPrice[];
  };
}

// 验证优先级是否可用的参数
export interface ActivityPriorityValidatorParam {
  priority: number;
  deviceType: SuitableDeviceTypeEnum;
}
