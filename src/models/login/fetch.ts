import { getCurrentPrefix } from '@/utils/currentPrefix';
import request from '@/utils/request';
import { DomainTypeEnum, getTargetDomain } from '@/utils/targetDomain';
import { loginApiOption } from './api';
import { LoginParam } from './interface';

// 获取Login列表
export const fetchLogin = (
  param: LoginParam,
  domainType: DomainTypeEnum,
): Promise<void> => {
  const config = loginApiOption.login;
  // config.url =
  switch (domainType) {
    case DomainTypeEnum.EU:
      config.url = `${getTargetDomain(domainType)}${getCurrentPrefix(
        domainType,
      )}/adm/admin/login`;
      break;
    case DomainTypeEnum.SG:
      config.url = `${getTargetDomain(domainType)}${getCurrentPrefix(
        domainType,
      )}/adm/admin/login`;
      break;
    default:
      break;
  }
  config.option.data = param;
  return request(config.url, config.option);
};
