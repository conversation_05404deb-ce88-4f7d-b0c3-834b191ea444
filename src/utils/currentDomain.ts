export const getCurrentDomain = () => {
  const { origin, port } = location;
  const cnOnline = 'api.petkit.com/latest';
  const cnSandbox = 'api-sandbox.petkit.com/6';
  const cnSandbox2 = 'api-sandbox2.petkit.com/6';
  // const cnLocal = 'bs-local.devapi.petkit.com';
  // let currentDomain = cnSandbox;
  let currentDomain = '';

  if (port === '9997') {
    return cnSandbox2;
  }

  if (origin.includes('sandbox2')) currentDomain = cnSandbox2;
  else if (origin.includes('sandbox')) currentDomain = cnSandbox;
  else if (origin.includes('petkit.com') || origin.includes('petkit.cn'))
    currentDomain = cnOnline;
  // else currentDomain = '';

  return currentDomain;
};
