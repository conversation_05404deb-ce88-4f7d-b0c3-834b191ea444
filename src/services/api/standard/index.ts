import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const standardApiOption: Record<
  | 'foodBrandList'
  | 'foodBrandCreation'
  | 'foodBrandUpdating'
  | 'foodBrandStatusSwitch'
  | 'foodBrandDetailById'
  | 'foodListByBrandId'
  | 'foodCreation'
  | 'foodUpdating'
  | 'foodStatusSwitch'
  | 'foodDetailById'
  | 'specList'
  | 'specCreation'
  | 'specUpdating'
  | 'specStatusSwitch'
  | 'specDetailById'
  | 'specAttrList'
  | 'specAttrCreation'
  | 'specAttrUpdating'
  | 'specAttrStatusSwitch'
  | 'specAttrDetailById'
  | 'petTypeList',
  RequestOption
> = {
  /**
   * ************
   * 主粮品牌接口
   */
  // 主粮品牌列表
  foodBrandList: {
    url: '/adm/pet/food/brand/listBrands',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建主粮品牌
  foodBrandCreation: {
    url: '/adm/pet/food/brand/addBrand',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新主粮品牌
  foodBrandUpdating: {
    url: '/adm/pet/food/brand/updateBrand',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 切换主粮品牌状态
  foodBrandStatusSwitch: {
    url: '/adm/pet/food/brand/updateBrandStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据主粮品牌id获取详情
  foodBrandDetailById: {
    url: '/adm/pet/food/brand/getBrandByBrandId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * ************
   */
  /**
   * ************
   * 主粮接口
   */
  // 根据品牌id获取主粮列表
  foodListByBrandId: {
    url: '/adm/pet/food/listFoodsByBrandId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建主粮
  foodCreation: {
    url: '/adm/pet/food/addFood',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新主粮
  foodUpdating: {
    url: '/adm/pet/food/updateFood',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 切换主粮状态
  foodStatusSwitch: {
    url: '/adm/pet/food/updateFoodStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据主粮id获取详情
  foodDetailById: {
    url: '/adm/pet/food/getFoodById',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * ************
   */
  /**
   * ************
   * 规格相关接口
   */
  // 规格列表
  specList: {
    url: '/adm/pet/food/spec/getSpecAll',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建规格
  specCreation: {
    url: '/adm/pet/food/spec/addSpec',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新规格
  specUpdating: {
    url: '/adm/pet/food/spec/updateSpec',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 切换规格状态
  specStatusSwitch: {
    url: '/adm/pet/food/spec/updateSpecStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据规格id获取详情
  specDetailById: {
    url: '/adm/pet/food/spec/getSpecById',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * ************
   */
  /**
   * ************
   * 规格值相关接口
   */
  // 规格值列表
  specAttrList: {
    url: '/adm/pet/food/spec/getByAttrSpecId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建规格值
  specAttrCreation: {
    url: '/adm/pet/food/spec/createSpecAttr',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新规格值
  specAttrUpdating: {
    url: '/adm/pet/food/spec/updateSpecAttr',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 切换规格值状态
  specAttrStatusSwitch: {
    url: '/adm/pet/food/spec/updateSpecAttrStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据规格值id获取详情
  specAttrDetailById: {
    url: '/adm/pet/food/spec/getSpecAttr',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * ************
   */
  /**
   * ************
   * 其它接口
   */
  petTypeList: {
    url: '/adm/pet/food/listPetTypes',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  /**
   * ************
   */
};
