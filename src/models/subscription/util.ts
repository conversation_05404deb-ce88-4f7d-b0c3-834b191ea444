import { initPaginatorParam } from '@/utils/request';
import {
  SubscriptionListParam,
  SubscriptionPeriodTypeEnum,
  SubscriptionStateEnum,
} from './interface';

export const subscriptionPeriodTypeName: {
  [key in SubscriptionPeriodTypeEnum]: string;
} = {
  [SubscriptionPeriodTypeEnum.DAY]: '天',
  [SubscriptionPeriodTypeEnum.MONTH]: '月',
  [SubscriptionPeriodTypeEnum.YEAR]: '年',
};

export const subscriptionStateName: { [key in SubscriptionStateEnum]: string } =
  {
    [SubscriptionStateEnum.SIGNING]: '签约中',
    [SubscriptionStateEnum.ACTIVITY]: '生效中',
    [SubscriptionStateEnum.DISABLED]: '已解约',
    [SubscriptionStateEnum.CANCELLED]: '已取消',
  };

export const initSubscriptionListParam: SubscriptionListParam = {
  ...initPaginatorParam,
};
