import uploadTemplate from '@/assets/templates/指定用户上传模板.xlsx';
import SkuSelector from '@/components/SkuSelector';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';
import Uploader from '@/components/Uploader';
import {
  fetchCouponCardCreation,
  fetchCouponCardDetail,
  fetchCouponCardMobileFileValidtor,
  fetchCouponCardMobileUpload,
} from '@/models/card/fetch';
import {
  CouponCardUploadResult,
  CouponCardUploadValidResult,
  CouponCardUsageModeEnum,
} from '@/models/card/interface';
import { ApiSuccessEnum } from '@/models/common.interface';
import { normFile } from '@/models/common.util';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { deviceTypeOptions } from '@/models/device/util';
import { fetchProductSkuDetail } from '@/models/product/fetch';
import {
  ProductSku,
  ReNewEnum,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { initRelationProductSkuListParam } from '@/models/product/util';
import { PageActionType } from '@/pages/enum';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  UpOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Divider,
  Form,
  FormListOperation,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Typography,
  Upload,
  message,
} from 'antd';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import styles from '../index.less';
import { VirtualCardForm } from '../interface';
import {
  calculateEstimatedPrice,
  initDeviceSkuForm,
  initVirtualCardForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

interface SelectedDeviceInfo {
  deviceType: SuitableDeviceTypeEnum;
  index: number;
}

const Edit: React.FC = () => {
  const urlParam = useParams<{ id: string; type: PageActionType }>();
  const [form] = Form.useForm<VirtualCardForm>();
  const [loading, setLoading] = useState(false);
  const [couponCardUploadResult, setCouponCardUploadResult] =
    useState<CouponCardUploadResult>();
  const [couponCardUploadValidResult, setCouponCardUploadValidResult] =
    useState<CouponCardUploadValidResult>();
  const [showProductSkuList, setShowProductSkuList] = useState(false);
  const [associateSkuListGroup, setAssociateSkuListGroup] = useState<
    ProductSku[][]
  >([]);

  const userType = Form.useWatch('userType', form);
  const deviceSkuList = Form.useWatch('deviceSkuList', form);
  const [associateSkuListParam, setAssociateSkuListParam] =
    useState<RelationProductSkuListParam>(initRelationProductSkuListParam);
  const [currentSelectedDeviceInfo, setCurrentSelectedDeviceInfo] =
    useState<SelectedDeviceInfo>();
  const [showMoreMobiles, setShowMoreMobiles] = useState(false);

  const back = () => {
    history.back();
  };

  // 校验手机号
  const validMobileList = async (mobileKey?: string) => {
    if (!mobileKey) {
      message.warning('当前未获取到上传内容，请重新上传');
      return;
    }
    setCouponCardUploadValidResult(undefined);
    if (!mobileKey) return;
    const result = await fetchCouponCardMobileFileValidtor(mobileKey);
    setCouponCardUploadValidResult(result);
  };

  // 增加适用设备
  const addDevice = (add: FormListOperation['add']) => {
    add(initDeviceSkuForm);
  };
  // 删除适用设备
  const removeDevice = (remove: FormListOperation['remove'], index: number) => {
    remove(index);
    associateSkuListGroup.splice(index, 1);
    setAssociateSkuListGroup([...associateSkuListGroup]);
  };

  const showProductSkuListModal = (selectedDeviceInfo: SelectedDeviceInfo) => {
    setShowProductSkuList(true);
    setCurrentSelectedDeviceInfo(selectedDeviceInfo);
    const payloadParam: RelationProductSkuParam = {
      uniteDeviceTypes: [selectedDeviceInfo.deviceType],
      uniteCapacities: [],
      isReNew: ReNewEnum.RENEW,
    };
    const facePrice = form.getFieldValue('facePrice');
    if (facePrice) {
      payloadParam.price = facePrice;
    }
    setAssociateSkuListParam({
      ...initRelationProductSkuListParam,
      payload: payloadParam,
    });
  };

  const requestDetailById = async (id: number) => {
    const result = await fetchCouponCardDetail(id);
    form.setFieldsValue(transferDetailToFormData(result, urlParam.type));

    const _associateSkuListGroup: ProductSku[][] = [];
    const skuPromises: Promise<ProductSku>[] = result.skus.map(async (sku) => {
      const relateiveSku = await fetchProductSkuDetail(sku.productSkuId);
      return relateiveSku;
    });

    Promise.all(skuPromises).then((skuList: ProductSku[]) => {
      skuList.forEach((sku, index) => {
        _associateSkuListGroup[index] = [sku];
        setAssociateSkuListGroup(_associateSkuListGroup);
      });
    });
  };

  const createVirtualCard = async (
    formData: VirtualCardForm,
    associateSkuListGroup: ProductSku[][],
    couponCardUploadResult?: CouponCardUploadResult,
  ) => {
    setLoading(true);
    try {
      const _param = transferFormDataToParam(
        formData,
        associateSkuListGroup,
        couponCardUploadResult,
      );
      // console.log('submit', couponCardUploadResult, formData, _param);
      // return;
      const state = '创建';
      const result = await fetchCouponCardCreation(_param);
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 提交form表单
  const submit = async (formData: VirtualCardForm) => {
    // console.log(
    //   associateSkuListGroup,
    //   !associateSkuListGroup.every((group) => (group || []).length),
    // );
    if (
      associateSkuListGroup &&
      !associateSkuListGroup.every((group) => (group || []).length)
    ) {
      message.warning('请先配置设备所关联的Sku后，再尝试提交');
      return;
    }
    if (
      formData.userType === CouponCardUsageModeEnum.USER &&
      !couponCardUploadResult
    ) {
      message.warning('请先上传指定用户的数据！');
      return;
    }

    // console.log(couponCardUploadResult, couponCardUploadValidResult);
    if (
      formData.userType === CouponCardUsageModeEnum.USER &&
      (!couponCardUploadResult ||
        !couponCardUploadValidResult ||
        couponCardUploadValidResult.invalidNum)
    ) {
      message.warning('请先校验并调整指定用户的信息！');
      return;
    }

    const estimatedPrice = calculateEstimatedPrice(
      associateSkuListGroup,
      formData,
      couponCardUploadValidResult,
    );

    Modal.confirm({
      width: 300,
      icon: null,
      content: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
          }}
        >
          <ExclamationCircleOutlined
            style={{ fontSize: 50, marginBottom: 16 }}
          />
          <Typography.Text type="danger">
            共计： {couponCardUploadValidResult?.total || formData.userNum || 0}{' '}
            张
          </Typography.Text>
          <Typography.Text type="danger">
            预计金额： {estimatedPrice}元
          </Typography.Text>
          <Typography.Text type="danger">确定生成卡券么？</Typography.Text>
        </div>
      ),
      onOk: () =>
        createVirtualCard(
          formData,
          associateSkuListGroup,
          couponCardUploadResult,
        ),
    });
  };

  useEffect(() => {
    if (!urlParam || !urlParam.id || !+urlParam.id) return;
    requestDetailById(+urlParam.id);
  }, [urlParam]);

  useEffect(() => {
    if (!form) return;
    const activeAssociateSkuListGroup = associateSkuListGroup.filter(
      (group) => group && !!group.length,
    );
    if (activeAssociateSkuListGroup.length) return;

    form.setFieldValue('facePrice', null);
  }, [associateSkuListGroup, form]);

  return (
    <>
      <Form
        layout="vertical"
        form={form}
        onFinish={submit}
        initialValues={initVirtualCardForm}
        scrollToFirstError
      >
        <ProCard title="基本信息" style={{ marginBottom: 16 }}>
          <Form.Item
            name="name"
            label="卡券名称"
            rules={[{ required: true, message: '请输入卡券名称' }]}
          >
            <Input placeholder="请输入卡券名称" maxLength={15} showCount />
          </Form.Item>
          <Form.Item
            name="aliasName"
            label="卡券Plan名称"
            rules={[{ required: true, message: '请输入卡券Plan名称' }]}
          >
            <Input placeholder="请输入卡券Plan名称" maxLength={50} showCount />
          </Form.Item>
          <Form.List name="deviceSkuList">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => (
                  <React.Fragment key={field.key}>
                    <Row>
                      <Col span={10}>
                        <Form.Item
                          name={[field.name, 'deviceType']}
                          label="适用设备"
                        >
                          <Select
                            options={deviceTypeOptions.filter(
                              (option) =>
                                !(deviceSkuList || []).find(
                                  (deviceSku) =>
                                    deviceSku.deviceType === option.value,
                                ),
                            )}
                            onChange={() => {
                              associateSkuListGroup[index] = [];
                              setAssociateSkuListGroup([
                                ...associateSkuListGroup,
                              ]);
                            }}
                            placeholder="请选择适用设备"
                          />
                        </Form.Item>
                      </Col>
                      <Col
                        span={10}
                        style={{ display: 'flex', alignItems: 'center' }}
                      >
                        <Button
                          type="link"
                          icon={<PlusOutlined />}
                          onClick={() => {
                            const deviceType = deviceSkuList[index]
                              .deviceType as SuitableDeviceTypeEnum;
                            if (!deviceType) {
                              message.warning('请先选择适用设备！');
                              return;
                            }
                            showProductSkuListModal({
                              deviceType,
                              index,
                            });
                          }}
                        >
                          去关联Sku
                        </Button>
                      </Col>
                      <Col
                        span={4}
                        style={{ display: 'flex', alignItems: 'center' }}
                      >
                        {(deviceTypeOptions || []).length >
                        (deviceSkuList || []).length ? (
                          <PlusCircleOutlined
                            className={styles.formListIcon}
                            onClick={() => {
                              addDevice(add);
                            }}
                          />
                        ) : null}
                        {(deviceSkuList || []).length > 1 ? (
                          <MinusCircleOutlined
                            className={styles.formListIcon}
                            onClick={() => removeDevice(remove, index)}
                          />
                        ) : null}
                      </Col>
                    </Row>
                    {associateSkuListGroup[index] &&
                    associateSkuListGroup[index].length ? (
                      <Row>
                        <Divider dashed>
                          {deviceSkuList[index].deviceType} 关联的Sku
                        </Divider>
                        <Table<ProductSku>
                          rowKey="id"
                          columns={(
                            associateSkuTableColumns as ColumnsType<ProductSku>
                          ).concat([
                            {
                              title: '操作',
                              dataIndex: 'action',
                              fixed: 'right',
                              width: 100,
                              render: () => (
                                <Popconfirm
                                  title="确定要删除么？"
                                  onConfirm={() => {
                                    const _associateSkuListGroup = [
                                      ...associateSkuListGroup,
                                    ];
                                    _associateSkuListGroup[index].splice(0, 1);
                                    setAssociateSkuListGroup([
                                      ..._associateSkuListGroup,
                                    ]);
                                    // console.log(
                                    //   deviceSkuList,
                                    //   index,
                                    //   _associateSkuListGroup,
                                    // );
                                    form.setFieldValue(
                                      ['deviceSkuList', index],
                                      { ...deviceSkuList[index], skuId: 0 },
                                    );
                                  }}
                                >
                                  <Button type="link">删除</Button>
                                </Popconfirm>
                              ),
                            },
                          ])}
                          scroll={{
                            x: associateSkuTableColumns
                              .filter((col) => col.dataIndex === 'action')
                              .reduce(
                                (prev, curr) => Number(curr.width || 0) + prev,
                                0,
                              ),
                          }}
                          dataSource={associateSkuListGroup[+`${index}`]}
                        />
                      </Row>
                    ) : (
                      ''
                    )}
                  </React.Fragment>
                ))}
              </>
            )}
          </Form.List>
          <Row gutter={16}>
            <Col span={12}>
              <Space>
                <Form.Item
                  name="serviceTime"
                  label="服务时长"
                  rules={[{ required: true, message: '请输入大于零的整数' }]}
                >
                  <InputNumber
                    style={{ width: 200 }}
                    placeholder="请输入大于零的整数"
                  />
                </Form.Item>
                <Form.Item
                  name="serviceTimeUnit"
                  rules={[{ required: true }]}
                  noStyle
                >
                  <Radio.Group>
                    <Radio
                      key={ServiceTimeUnitEnum.MONTH}
                      value={ServiceTimeUnitEnum.MONTH}
                    >
                      月
                    </Radio>
                    <Radio
                      key={ServiceTimeUnitEnum.DAY}
                      value={ServiceTimeUnitEnum.DAY}
                    >
                      天
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              </Space>
            </Col>
            <Col span={12} style={{ display: 'flex', alignItems: 'flex-end' }}>
              <Form.Item
                name="exchangeDate"
                label="截止兑换日期"
                rules={[{ required: true, message: '请选择截止兑换日期' }]}
                style={{ width: '80%' }}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="请选择卡券可兑换的截止日期"
                  disabledDate={(current) =>
                    current && current < dayjs().endOf('d')
                  }
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="coverImage"
            label="卡券封面"
            getValueFromEvent={normFile}
            shouldUpdate
            valuePropName="fileList"
            rules={[{ required: true, message: '请上传卡券封面' }]}
            extra={<Alert message="尺寸为72px*60px" type="info" showIcon />}
          >
            <Uploader />
          </Form.Item>
        </ProCard>

        <ProCard title="价格" style={{ marginBottom: 16 }}>
          <Form.Item
            label="实际售价（元）"
            name="realPrice"
            rules={[{ required: true, message: '请输入实际售价（元）' }]}
          >
            <InputNumber min={0} placeholder="请输入实际售价（元）" />
          </Form.Item>
          <Form.Item
            label="卡券面额（元）"
            name="facePrice"
            rules={[
              { required: true, message: '请选择关联的Sku来获取卡券面额' },
            ]}
          >
            <InputNumber
              disabled
              min={0.01}
              placeholder="请选择关联的Sku来获取卡券面额"
            />
          </Form.Item>
        </ProCard>

        <ProCard title="使用对象" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="userType"
                label="使用方"
                rules={[{ required: true }]}
                extra={
                  userType === CouponCardUsageModeEnum.USER ? (
                    <div style={{ marginTop: 8 }}>
                      <Form.Item
                        extra={
                          <Space>
                            <span
                              style={{
                                fontSize: 10,
                                color: 'rgba(127, 127, 127, 0.65)',
                                verticalAlign: 'sub',
                              }}
                            >
                              仅支持上传excel文件
                            </span>
                            <a
                              href={uploadTemplate}
                              download
                              style={{ fontSize: 12 }}
                            >
                              下载模板
                            </a>
                          </Space>
                        }
                      >
                        <Upload
                          accept=".xlsx,.xls"
                          listType="text"
                          maxCount={1}
                          showUploadList={{
                            showDownloadIcon: false,
                          }}
                          itemRender={(node, file) => (
                            <div style={{ display: 'flex' }}>
                              {node}{' '}
                              {file.status === 'error' ? null : (
                                <Button
                                  type="link"
                                  onClick={() =>
                                    validMobileList(
                                      couponCardUploadResult?.mobileKey ||
                                        couponCardUploadResult?.emailKey,
                                    )
                                  }
                                >
                                  校验
                                </Button>
                              )}
                            </div>
                          )}
                          beforeUpload={(file) => {
                            const LIMIT_SIZE = 5 * 1024 * 1024;
                            if (file.size > LIMIT_SIZE) {
                              message.error(
                                '文件过大，不能超过5MB！请重新上传',
                              );
                              return Upload.LIST_IGNORE;
                            }
                            setCouponCardUploadResult(undefined);
                            setCouponCardUploadValidResult(undefined);
                            return true;
                          }}
                          customRequest={async (options) => {
                            const { file, onSuccess, onProgress, onError } =
                              options;
                            try {
                              const result = await fetchCouponCardMobileUpload(
                                file as File,
                              );
                              setCouponCardUploadResult(result);
                              onProgress?.({
                                percent: 100,
                              });
                              onSuccess?.({});
                            } catch (error: any) {
                              onError?.({ ...error });
                            }
                          }}
                        >
                          <Button icon={<UploadOutlined />}>上传文件</Button>
                        </Upload>
                      </Form.Item>
                      {couponCardUploadValidResult ? (
                        <div>
                          <Space>
                            <div>共计：{couponCardUploadValidResult.total}</div>
                            <div>
                              有效数量：
                              {couponCardUploadValidResult.total -
                                couponCardUploadValidResult.invalidNum}
                            </div>
                          </Space>

                          <div>
                            <div
                              style={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                maxHeight: 200,
                                overflow: 'auto',
                              }}
                            >
                              {couponCardUploadValidResult.failValidMobiles
                                .length > 10 && !showMoreMobiles
                                ? couponCardUploadValidResult.failValidMobiles
                                    .slice(0, 10)
                                    .map((mobile, index) => (
                                      <span
                                        key={String(index)}
                                        style={{
                                          color: '#9d9d9d',
                                          marginRight: 16,
                                        }}
                                      >
                                        {mobile}
                                      </span>
                                    ))
                                : couponCardUploadValidResult.failValidMobiles.map(
                                    (mobile, index) => (
                                      <span
                                        key={String(index)}
                                        style={{
                                          color: '#9d9d9d',
                                          marginRight: 16,
                                        }}
                                      >
                                        {mobile}
                                      </span>
                                    ),
                                  )}
                            </div>

                            {couponCardUploadValidResult.failValidMobiles
                              .length > 10 ? (
                              <Button
                                type="link"
                                className={styles.readMoreMobiles}
                                onClick={() =>
                                  setShowMoreMobiles(!showMoreMobiles)
                                }
                              >
                                {showMoreMobiles ? (
                                  <>
                                    收起
                                    <UpOutlined style={{ marginLeft: 4 }} />
                                  </>
                                ) : (
                                  <>
                                    查看更多
                                    <DownOutlined style={{ marginLeft: 4 }} />
                                  </>
                                )}
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      ) : null}
                    </div>
                  ) : (
                    <Form.Item
                      name="userNum"
                      rules={[
                        { required: true, message: '请输入大于零的整数' },
                      ]}
                    >
                      <InputNumber min={1} placeholder="请输入大于零的整数" />
                    </Form.Item>
                  )
                }
              >
                <Radio.Group>
                  <Radio
                    key={CouponCardUsageModeEnum.USER}
                    value={CouponCardUsageModeEnum.USER}
                  >
                    指定用户
                  </Radio>
                  <Radio
                    key={CouponCardUsageModeEnum.NUM}
                    value={CouponCardUsageModeEnum.NUM}
                  >
                    指定数量
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="demandSide"
                label="需求方"
                rules={[{ required: true, message: '请输入需求方信息' }]}
              >
                <Input placeholder="请输入需求方信息" />
              </Form.Item>
            </Col>
          </Row>
        </ProCard>

        <ProCard>
          <Form.Item style={{ textAlign: 'center', marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button type="default" onClick={back}>
              取消
            </Button>
          </Form.Item>
        </ProCard>
      </Form>

      {showProductSkuList ? (
        <SkuSelector
          param={associateSkuListParam}
          open
          onOk={(ev) => {
            if (!currentSelectedDeviceInfo) return;
            const deviceSku = deviceSkuList.find(
              (deviceSku) =>
                deviceSku.deviceType === currentSelectedDeviceInfo.deviceType,
            );
            if (!deviceSku) return;
            deviceSku.skuId = ev.id;
            associateSkuListGroup[currentSelectedDeviceInfo.index] = [ev];
            setAssociateSkuListGroup([...associateSkuListGroup]);
            form.setFieldValue('deviceSkuList', [...deviceSkuList]);
            form.setFieldValue('facePrice', ev.price.price);
            form.validateFields();
          }}
          onCancel={() => setShowProductSkuList(false)}
        />
      ) : null}
    </>
  );
};

export default Edit;
