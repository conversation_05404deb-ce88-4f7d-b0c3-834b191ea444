import type { User, UserQueryParams } from '@/api/user/types';
import { userService } from '@/services/user';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useCallback, useMemo } from 'react';

interface UserTableProps {
  onUserSelect?: (user: User) => void;
  onUserEdit?: (user: User) => void;
}

/**
 * 用户表格组件 - 遵循PascalCase命名规范
 */
const UserTable: React.FC<UserTableProps> = ({ onUserSelect, onUserEdit }) => {
  // 方法名使用camelCase
  const handleRowClick = useCallback(
    (record: User) => {
      onUserSelect?.(record);
    },
    [onUserSelect],
  );

  const handleEditClick = useCallback(
    (record: User) => {
      onUserEdit?.(record);
    },
    [onUserEdit],
  );

  const handleDeleteClick = useCallback(async (record: User) => {
    try {
      await userService.deleteUser(record.id);
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  }, []);

  // 使用useMemo优化columns定义
  const columns: ProColumns<User>[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username',
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        valueEnum: {
          active: { text: '激活', status: 'Success' },
          inactive: { text: '未激活', status: 'Default' },
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        render: (_, record) => [
          <Button
            key="edit"
            type="link"
            onClick={() => handleEditClick(record)}
          >
            编辑
          </Button>,
          <Button
            key="delete"
            type="link"
            danger
            onClick={() => handleDeleteClick(record)}
          >
            删除
          </Button>,
        ],
      },
    ],
    [handleEditClick, handleDeleteClick],
  );

  return (
    <ProTable<User>
      request={async (params) => {
        // 使用新的userService
        const response = await userService.getUserList(
          params as UserQueryParams,
        );
        return {
          data: response.items,
          success: true,
          total: response.total,
        };
      }}
      columns={columns}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      pagination={{
        pageSize: 10,
      }}
      onRow={(record) => ({
        onClick: () => handleRowClick(record),
      })}
    />
  );
};

export default UserTable;
