import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction } from '@/models/common.util';
import { SearchTypeEnum, WifiSearchTypeEnum } from '@/models/device/interface';
import { fetchDevicepackageUnlink } from '@/models/devicepackage/fetch';
import { DevicepackageUnlinkParam } from '@/models/devicepackage/interface';
import { fetchServiceListV2 } from '@/models/service/fetch';
import {
  ServiceListV2Param,
  ServiceStatusV2Enum,
  ServiceV2,
} from '@/models/service/interface';
import {
  initServiceListV2Param,
  serviceStatusNameObj,
} from '@/models/service/util';
import { antdUtils } from '@/utils/antd.util';
import global from '@/utils/global';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Modal, Select, Space, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import OperationHistoryV2 from '../OperationHistoryV2';
import RelativeDevice from '../RelativeDevice';
import ServiceSkuListV2 from '../ServiceSkuListV2';
import ServiceTransferringV2 from '../ServiceTransferringV2';
import { ServiceListForm } from '../interface';
// import { postMessageFunction } from '@/models/common.util';

const ListV2: React.FC = () => {
  // const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<ServiceListForm>();
  const [dataList, setDataList] = useState<ServiceV2[]>([]);
  const [listParam, setListParam] = useState<ServiceListV2Param>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const [showOperationHistory, setShowOperationHistory] = useState(false);
  const [showServiceTransferring, setShowServiceTransferring] = useState(false);
  const [showServiceSkuList, setShowServiceSkuList] = useState(false);
  const [showRelativeDevice, setShowRelativeDevice] = useState(false);
  const [selectedService, setSelectedService] = useState<ServiceV2>();
  const [deviceType, setDeviceType] = useState('');
  const formRef = useRef<ProFormInstance<ServiceListForm>>();

  // 获取列表数据
  const requestServiceList = async (param: ServiceListV2Param) => {
    console.log('requestServiceList', param);
    let result = await fetchServiceListV2({
      ...param,
    });
    if (!result) return;
    const { items, ...rest } = result;
    setPagination(rest);
    setDataList(items);
  };

  const showHistory = (row: ServiceV2) => {
    setSelectedService(row);
    setShowOperationHistory(true);
  };

  const transferService = (row: ServiceV2) => {
    setSelectedService(row);
    setShowServiceTransferring(true);
  };

  const showSkuList = (row: ServiceV2) => {
    setSelectedService(row);
    setShowServiceSkuList(true);
  };

  const relateDevice = (row: ServiceV2) => {
    setSelectedService(row);
    setShowRelativeDevice(true);
  };

  // 解除关联
  const unrelateDevice = (row: ServiceV2) => {
    Modal.confirm({
      title: '解除关联提醒',
      content: `确定要解除当前服务与sn为${row.sn}的设备的关联状态么？`,
      okText: '确定',
      onOk: async () => {
        const param: DevicepackageUnlinkParam = {
          userId: row.userId,
          deviceId: row.deviceId,
          deviceType: row.deviceType,
        };
        try {
          await fetchDevicepackageUnlink(param);
          message.success('解除关联成功！');
          setListParam({ ...initServiceListV2Param, ...listParam });
        } catch (error) {
          console.log(error);
        }
      },
    });
  };

  const onModelClosed = (shouldRefresh?: boolean) => {
    setShowOperationHistory(false);
    setShowServiceTransferring(false);
    setShowServiceSkuList(false);
    setShowRelativeDevice(false);
    setSelectedService(undefined);
    if (shouldRefresh) {
      setListParam({ ...initServiceListV2Param, ...listParam });
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: ServiceListForm = form?.getFieldsValue();
          let key: keyof ServiceListForm;
          for (key in formData) {
            if (formData[key] === undefined || !formData[key]) {
              delete formData[key];
            }
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/cloudServiceV2`,
              param: { ...formData } as unknown as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          // form?.resetFields();
          // setUrlParam(form?.getFieldsValue());
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/cloudServiceV2`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;

    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  // 当SKU详情中存在成功退款的时候，需要刷新当页页面的数据
  const onRefundSuccess = () => {
    setListParam({ ...initServiceListV2Param, ...listParam });
  };

  // 当关联设备被成功关联时，调用该方法
  const onRelateDeviceSuccess = () => {
    setListParam({ ...initServiceListV2Param, ...listParam });
  };

  const columns: Array<ProColumns<ServiceV2>> = [
    {
      title: '服务ID',
      width: 80,
      dataIndex: 'serviceId',
    },
    {
      title: '用户ID',
      width: 100,
      dataIndex: 'userId',
      render: (text, row) =>
        row.userId ? (
          <RedirectLink
            text={text}
            linkUrl="/user/users"
            params={{
              username: row.userId,
            }}
          />
        ) : (
          '-'
        ),
    },
    // {
    //   title: '设备ID',
    //   width: 100,
    //   dataIndex: 'deviceId',
    //   render: (_, row) =>
    //     deviceType && row.deviceId ? (
    //       <RedirectLink
    //         text={row.deviceId}
    //         linkUrl={`/${urlParam.deviceType}/devices`}
    //         params={{
    //           type: global.bluetoothDeviceList.includes(deviceType)
    //             ? SearchTypeEnum.ID
    //             : WifiSearchTypeEnum.ID,
    //           s: row.deviceId,
    //         }}
    //       />
    //     ) : (
    //       row.deviceId || '-'
    //     ),
    // },
    {
      title: '设备SN',
      dataIndex: 'sn',
      width: 150,
      render: (_, row) =>
        deviceType && row.sn ? (
          <RedirectLink
            text={row.sn}
            linkUrl={`/${deviceType}/devices`}
            params={{
              type: global.bluetoothDeviceList.includes(deviceType)
                ? SearchTypeEnum.Sn
                : WifiSearchTypeEnum.SN,
              s: row.sn,
            }}
          />
        ) : (
          row.sn || '-'
        ),
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      width: 100,
      search: false,
      render: (_, row) => (row.deviceType || '-').toUpperCase(),
    },
    {
      title: '服务状态',
      width: 150,
      dataIndex: 'serviceStatus',
      valueEnum: {
        [ServiceStatusV2Enum.OUT_DATE]: '已过期',
        [ServiceStatusV2Enum.ON_SERVICE]: '生效中',
        [ServiceStatusV2Enum.DELETED]: '已删除',
      },
      // render: (_, row) => {
      //   const planWorkIndateList = row.servicePlans
      //     .filter((plan) => plan.status !== ServicePlanStatusEnum.DELETED)
      //     .map((plan) => plan.workIndate)
      //     .sort()
      //     .reverse();
      //   const maxPlanWorkIndate = planWorkIndateList[0] * 1000;
      //   const now = dayjs().valueOf();
      //   if (now > maxPlanWorkIndate) {
      //     return '已过期';
      //   }
      //   return     '服务中';
      // },
    },
    {
      title: '服务Plan',
      width: 150,
      dataIndex: 'servicePlans',
      search: false,
      // render: (_, row) =>
      //   (row.servicePlans || []).map((cap, index) => (
      //     <div key={String(index)}>
      //       {cap.type} {cap.cycleTime}天循环
      //     </div>
      //   )),
      render: (_, row) => {
        return (row.servicePlans || []).map((plan, index) => (
          <div key={String(index)}>{plan.skuShortName}</div>
        ));
      },
    },
    {
      title: '服务Plan到期时间',
      width: 180,
      dataIndex: 'serviceStatus',
      valueType: 'select',
      search: false,
      valueEnum: serviceStatusNameObj,
      render: (_, row) => {
        return (row.servicePlans || []).map((plan, index) => (
          <div key={String(index)}>
            {/* {dayjs(plan.workIndate)
              .tz(plan.zoneId)
              .format('YYYY-MM-DD HH:mm:ss')} */}
            {plan.workIndateStr || '-'}
          </div>
        ));
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 300,
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <Space size="large">
          <a key={'history'} onClick={() => showHistory(row)}>
            变更历史
          </a>
          {row.serviceStatus !== ServiceStatusV2Enum.DELETED ? (
            <a key={'transfer'} onClick={() => transferService(row)}>
              转移服务
            </a>
          ) : null}
          <a key={'planList'} onClick={() => showSkuList(row)}>
            服务Plan
          </a>
          {!row.sn ? (
            <a key={'relative'} onClick={() => relateDevice(row)}>
              关联设备
            </a>
          ) : null}
          {row.sn && row.serviceStatus !== ServiceStatusV2Enum.DELETED ? (
            <a key={'relative'} onClick={() => unrelateDevice(row)}>
              解除关联
            </a>
          ) : null}
        </Space>
      ),
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: { d4sh: 'D4sh', d4h: 'D4H', t5: 'T5', t6: 'T6', t7: 'T7' },
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} allowClear />
      ),
    },
  ];

  useEffect(() => {
    if (!urlParam.deviceType) return;

    const _listParam: ServiceListV2Param = {
      ...initServiceListV2Param,
      // deviceType: urlParam.deviceType || 'd4sh',
      ...urlParam,
    };
    setListParam({ ..._listParam });
    setDeviceType(urlParam.deviceType);

    if (JSON.stringify(urlParam) === '{}') return;

    const form = formRef.current;
    form?.setFieldsValue(urlParam);
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestServiceList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<ServiceV2>
        dataSource={dataList}
        formRef={formRef}
        columns={columns}
        defaultSize="small"
        rowKey="serviceId"
        search={{
          defaultCollapsed: false,
          span: 6,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        // toolbar={{
        //   actions: [
        //     <Button
        //       key="button"
        //       type="primary"
        //       icon={<PlusOutlined />}
        //       onClick={() => editService(0)}
        //     >
        //       新增
        //     </Button>,
        //   ],
        // }}
        pagination={{
          ...antdUtils.transferPaginatorToTablePagination(pagination),
          onChange: onPaginationChanged,
        }}
      />
      {showOperationHistory && selectedService ? (
        <OperationHistoryV2
          deviceType={deviceType}
          showModal
          serviceId={selectedService.serviceId}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
      {showServiceTransferring && selectedService ? (
        <ServiceTransferringV2
          deviceType={deviceType}
          showModal
          currentUserId={selectedService.userId}
          serviceId={selectedService.serviceId}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
      {showServiceSkuList && selectedService ? (
        <ServiceSkuListV2
          deviceType={deviceType}
          showModal
          serviceId={selectedService.serviceId}
          onExpirationChangeSuccess={() =>
            setListParam({ ...initServiceListV2Param, ...listParam })
          }
          onRefundSuccess={onRefundSuccess}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
      {showRelativeDevice && selectedService ? (
        <RelativeDevice
          userId={selectedService.userId}
          deviceType={deviceType}
          showModal
          serviceId={selectedService.serviceId}
          onRelateDeviceSuccess={onRelateDeviceSuccess}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default ListV2;
