import { convertImageToBase64 } from '@/utils/util';
import { RcFile } from 'antd/lib/upload';
import { SizeEnum, UploadTokenTypeEnum } from './interface';

export const getByteSize = (byte: number, type: SizeEnum): number => {
  let result = 0;
  switch (type) {
    case SizeEnum.MB:
      result = byte * 1024 * 1024;
      break;
    case SizeEnum.KB:
      result = byte * 1024;
      break;
    case SizeEnum.B:
      result = byte;
      break;
    default:
      result = byte;
      break;
  }
  return result;
};

export const uploadTypeUrl: { [key in UploadTokenTypeEnum]: string } = {
  [UploadTokenTypeEnum.FILE]: `/adm/app/upload_file_token`,
  [UploadTokenTypeEnum.IMAGE]: `/adm/app/upload_image_token`,
  [UploadTokenTypeEnum.VIDEO]: `/adm/app/upload_video_token`,
  [UploadTokenTypeEnum.FIRMWARE]: `/adm/app/upload_firmware_token`,
  [UploadTokenTypeEnum.CUSTOMER]: '',
};

export const getImageSize = async (
  file: RcFile,
): Promise<{ width: number; height: number }> => {
  const imageData = await convertImageToBase64(file);
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = imageData as string;
    try {
      image.onload = () => {
        resolve({ width: image.width, height: image.height });
      };
    } catch (error) {
      reject(error);
    }
  });
};
