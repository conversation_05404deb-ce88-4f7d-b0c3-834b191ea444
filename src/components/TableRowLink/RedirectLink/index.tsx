import { postMessageFunction } from '@/models/common.util';
import React, { useEffect, useState } from 'react';

interface Props {
  linkUrl: string;
  params?: { [key: string]: string | number };
  text?: string | React.ReactNode;
}

const RedirectLink: React.FC<Props> = ({
  linkUrl,
  params = {},
  text = '',
}: Props) => {
  const [url, setUrl] = useState<string>('');

  useEffect(() => {
    const urlParam = new URLSearchParams(params as Record<string, string>);
    setUrl(`${linkUrl}${urlParam.toString() ? '?' : ''}${urlParam.toString()}`);
  }, [linkUrl, params]);

  if (!text) return <>-</>;

  return (
    <a
      onClick={() =>
        postMessageFunction({
          type: 'redirect',
          content: { redirectUrl: url },
        })
      }
    >
      {text}
    </a>
  );
};

export default RedirectLink;
