import { refundApiOption } from '@/services/api/refund';
import request from '@/utils/request';
import {
  AvailableRefundInfo,
  RefundDetail,
  RefundInfo,
  RefundParam,
  RefundResult,
} from './interface';

// 获取订单余额信息
export const fetchRefundRetrieveBalanceDetail = (
  orderId: string,
): Promise<AvailableRefundInfo> => {
  const config = { ...refundApiOption.refundPaymentInfo };
  config.option.restApi = { orderId };
  return request(config.url, config.option);
};

// 退款
export const fetchRefund = (param: RefundParam): Promise<RefundResult> => {
  const config = refundApiOption.refund;
  config.option.data = param;
  return request(config.url, config.option);
};

// 根据退款ID查询退款
export const fetchRefundInfoById = (refundId: string): Promise<RefundInfo> => {
  const config = refundApiOption.refundInfoById;
  config.option.restApi = { refundId };
  return request(config.url, config.option);
};

// 获取退款详情
export const fetchRefundDetail = (orderId: string): Promise<RefundDetail> => {
  const config = refundApiOption.refundDetail;
  config.option.restApi = { orderId };
  return request(config.url, config.option);
};

// 获取退款详情
export const fetchCancelSubscription = (orderId?: string, subNo?: string): Promise<void> => {
  const config = refundApiOption.cancelSubscription;
  config.option.data = { orderId, subNo };
  return request(config.url, config.option);
};
