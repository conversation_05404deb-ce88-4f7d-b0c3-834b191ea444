import { initPaginatorParam } from '@/utils/request';
import {
  ChangeExpirationRecordListParam,
  RefundOrderRecordListParam,
  RefundTypeEnum,
  VirtualCardDistributionListParam,
} from './interface';

export const refundTypeMap: { [key in RefundTypeEnum]: string } = {
  [RefundTypeEnum.FULL]: '全额',
  [RefundTypeEnum.PARTIAL]: '部分',
};

export const initVirtualCardDistributionListParam: VirtualCardDistributionListParam =
  {
    ...initPaginatorParam,
  };

export const initChangeExpirationRecordListParam: ChangeExpirationRecordListParam =
  { ...initPaginatorParam };

export const initRefundOrderRecordListParam: RefundOrderRecordListParam = {
  ...initPaginatorParam,
};
