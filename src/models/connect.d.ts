import { AppState } from './app/model';
import { CurrencyState } from './currency/model';
import { MallState } from './mall/model';
import { PackageState } from './package/model';
import { ProductState } from './product/model';
import { UtilState } from './util/model';

export interface Route {
  /**
   * Any valid URL path
   */
  path?: string;
  /**
   * A React component to render only when the location matches.
   */
  component?: string | (() => any);
  wrappers?: string[];
  /**
   * navigate to a new location
   */
  redirect?: string;
  /**
   * When true, the active class/style will only be applied if the location is matched exactly.
   */
  exact?: boolean;
  routes?: Route[];
  [k: string]: any;
}

export interface Loading {
  global: boolean;
  effects: { [key: string]: boolean | undefined };
  models: {
    global?: boolean;
    menu?: boolean;
    setting?: boolean;
    user?: boolean;
    register?: boolean;
    login?: boolean;
  };
}

export interface ConnectState {
  loading: Loading;
  product: ProductState;
  package: PackageState;
  util: UtilState;
  mall: MallState;
  app: AppState;
  currency: CurrencyState;
}
