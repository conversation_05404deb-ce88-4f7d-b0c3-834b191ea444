import {
  fetchBundleSkuCreate,
  fetchBundleSkuDetail,
  fetchBundleSkuUpdate,
} from '@/api/sku/bundle/fetch';
import { ServiceTimeUnitEnum } from '@/api/sku/bundle/types';
import CurrencyPriceForm from '@/components/CurrencyPriceForm';
import Uploader from '@/components/Uploader';
import { ApiSuccessEnum } from '@/models/common.interface';
import { InfoCircleFilled } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import {
  Alert,
  Button,
  Col,
  Form,
  FormProps,
  Input,
  InputNumber,
  Radio,
  Row,
  Switch,
  Typography,
  message,
} from 'antd';
import React, { CSSProperties, useEffect, useMemo, useState } from 'react';
import { ProductSkuForm } from '../interface';
import {
  initialProductSkuForm,
  serviceTimeUnitObj,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<ProductSkuForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 20 },
  };
  const proCardStyle: CSSProperties = {
    marginBlockEnd: 16,
  };

  const [detail, setDetail] = useState<any>();

  const isAutoRenew = Form.useWatch('isAutoRenew', form);
  const isFirstPreferential = Form.useWatch('isFirstPreferential', form);
  const serviceTimeUnit = Form.useWatch('serviceTimeUnit', form);

  const isEditing = useMemo(() => {
    return !!(param.id && +param.id);
  }, [param]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchBundleSkuDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    setDetail(detail);
  };

  const onSubmitFail: FormProps['onFinishFailed'] = ({ errorFields }) => {
    form.scrollToField(errorFields[0].name, { behavior: 'smooth' });
  };

  // 提交form表单
  const submit = async (formData: ProductSkuForm) => {
    if (!formData.serviceTimeUnit) {
      message.warning('请选择服务时长的单位');
      return;
    }

    const _param = transferFormDataToParam(
      formData,
      [], // 如果需要relatedSkus状态，这里需要传入实际的状态变量
      +(param?.id || 0) || 0,
    );
    // console.log('submit', formData, _param);
    // return;

    setLoading(true);
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchBundleSkuUpdate(_param);
      } else {
        state = '创建';
        result = await fetchBundleSkuCreate(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const onServiceTimeUnitChange = (value: ServiceTimeUnitEnum) => {
    // 如果时长单位为日时，则需要禁用自动续费的操作
    if (value === ServiceTimeUnitEnum.DAY) {
      form.setFieldValue('isAutoRenew', false);
    }
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <>
      <Form
        {...layout}
        form={form}
        layout="vertical"
        onFinish={submit}
        onFinishFailed={onSubmitFail}
        initialValues={initialProductSkuForm}
      >
        <ProCard title="SKU内容" style={proCardStyle}>
          <Row>
            <Col span={8}>
              <Form.Item
                name="name"
                label="SKU名称"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input showCount maxLength={15} placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="simpleName"
                label="SKU简称"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入SKU简称' }]}
              >
                <Input showCount maxLength={6} placeholder="请输入SKU简称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="aliasName"
                label="SKU别名"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入SKU别名' }]}
              >
                <Input maxLength={50} showCount placeholder="请输入SKU别名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sort"
                label="优先级"
                rules={[{ required: true, message: '请输入优先级' }]}
                extra={
                  <Alert message="数字越大，优先级越高" type="info" showIcon />
                }
              >
                <InputNumber
                  min={0}
                  max={99999}
                  step={1}
                  // 转为整数
                  formatter={(value: number | undefined) => {
                    return value || value === 0
                      ? `${parseInt(`${value || ''}`)}`
                      : '';
                  }}
                  placeholder="请输入优先级"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="serviceTimeUnit"
                label="服务时长"
                rules={[{ required: true }]}
              >
                <Radio.Group
                  onChange={(ev) => onServiceTimeUnitChange(ev.target.value)}
                >
                  {Object.keys(serviceTimeUnitObj).map((index) => (
                    <Radio key={index} value={index} disabled={isEditing}>
                      {serviceTimeUnitObj[index as ServiceTimeUnitEnum]}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="deviceNumber"
                label="支持设备数量"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入支持设备数量' }]}
              >
                <InputNumber
                  min={1}
                  max={999999}
                  step={1}
                  placeholder="请输入设备数量"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="promotionName"
                label="优惠名称"
                labelCol={{ span: 8 }}
                rules={[{ required: true, message: '请输入优惠名称' }]}
              >
                <Input maxLength={50} showCount placeholder="请输入优惠名称" />
              </Form.Item>
            </Col>
          </Row>
        </ProCard>
        <ProCard title="SKU价格" style={proCardStyle}>
          <Form.Item
            label=""
            name="currencyPrice"
            wrapperCol={{ span: 24 }}
            rules={[
              {
                validator: (_, value) => {
                  const { price, linePrice } = value;
                  if (price === undefined || price === null) {
                    return Promise.reject(new Error('价格不能为空'));
                  }
                  if (linePrice && price > linePrice) {
                    return Promise.reject(
                      new Error('划线价格必须大于或者等于价格'),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <CurrencyPriceForm />
          </Form.Item>
          <Form.Item
            name="isAutoRenew"
            label="自动续费"
            valuePropName="checked"
            required
          >
            <Switch
              disabled={
                isEditing || serviceTimeUnit === ServiceTimeUnitEnum.DAY
              }
              unCheckedChildren="关闭"
              checkedChildren="开启"
            />
          </Form.Item>
          {isAutoRenew && (
            <Form.Item
              name="isFirstPreferential"
              label="首期特惠"
              required
              valuePropName="checked"
              extra={
                <Typography.Text type="secondary">
                  如连续包月原价 40 元，首月 35
                  元，后续按原价续费；或连续包年原价 299 元，首年 135
                  元，后续按原价续费；
                </Typography.Text>
              }
            >
              <Switch unCheckedChildren="关闭" checkedChildren="开启" />
            </Form.Item>
          )}
          {isAutoRenew && isFirstPreferential && (
            <Form.Item
              name="preferentialPrice"
              label="优惠价格(元)"
              required
              rules={
                [
                  // { required: true, message: '请输入首期特惠价格' },
                  // {
                  //   max: price,
                  //   type: 'number',
                  //   message: `首期价格不能高于${(price || 0).toFixed(2)}`,
                  // },
                  // {
                  //   min: 0,
                  //   type: 'number',
                  //   message: '请输入大于0的数，最小值为0.01',
                  // },
                ]
              }
            >
              {/* <InputNumber
                style={{ width: '100%' }}
                prefix="￥"
                precision={2}
                min={0.01}
                placeholder="请输入优惠价格"
              /> */}
              <CurrencyPriceForm formItems={['price']} />
            </Form.Item>
          )}
        </ProCard>
        <ProCard title="营销设置" style={proCardStyle}>
          <Form.Item
            name="cornerIcons"
            label="角标"
            shouldUpdate
            getValueFromEvent={normFile}
            valuePropName="fileList"
            extra={
              <Typography.Text type="secondary">
                <InfoCircleFilled
                  style={{ color: '#1890ff', marginRight: 8 }}
                />
                SKU引流图标，高度为30px或其倍数
              </Typography.Text>
            }
          >
            <Uploader maxCount={3} />
          </Form.Item>
          {/* <Form.Item name="description" label="SKU说明">
            <Input maxLength={500} showCount placeholder="请输入SKU说明" />
          </Form.Item> */}
        </ProCard>
        <ProCard style={proCardStyle}>
          <Form.Item
            wrapperCol={{ span: 24 }}
            style={{ textAlign: 'center', marginBottom: 0 }}
          >
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </ProCard>
      </Form>
    </>
  );
};

export default Edit;
