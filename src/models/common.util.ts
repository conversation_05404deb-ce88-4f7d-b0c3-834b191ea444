import { BaseQueryFilterProps } from '@ant-design/pro-components';
import Decimal from 'decimal.js';
import forIn from 'lodash/forIn';
import { PMData, SelectOption, StatusEnum, TreeNode } from './common.interface';

// saleStatusName 已迁移到适配器文件中，使用新的枚举映射
// 为了向后兼容，这里保持原有的数组格式
export const saleStatusName = ['下架', '上架'];

export const deviceList = ['D4sh', 'D4h', 'T5', 'T6', 'T7'];

export const statusName = ['启用', '禁用'];

export const DEFAULT_PAGE_SIZE = 20;

export const getStatusMap = (): Map<
  'ALL' | StatusEnum,
  { text: string; status: string }
> => {
  const _statusMap = new Map<
    'ALL' | StatusEnum,
    { text: string; status: string }
  >();
  _statusMap.set(StatusEnum.ENABLE, { text: '启用', status: 'Success' });
  _statusMap.set(StatusEnum.DISABLE, { text: '禁用', status: 'Error' });
  return _statusMap;
};

// 组装form表达的提交参数数据 用于表格搜索form
type CopyPartialInterface<T> = Partial<Pick<T, keyof T>>;
export const transferFormDataToParam = <FormData, Param>(
  formData: FormData,
): CopyPartialInterface<Param> => {
  const param: CopyPartialInterface<Param> = {};
  forIn(formData, (value, key) => {
    if (value !== undefined) {
      (param as any)[key] = value;
    }
  });
  return param;
};

// 用户主人的跳转功能
export const postMessageFunction = (pmData: PMData) => {
  if (window.parent) {
    let redirectUrl = pmData.content?.redirectUrl || '';
    const searchStr = new URLSearchParams(pmData.content?.param);
    if (searchStr.toString()) {
      if (redirectUrl.includes('?')) {
        redirectUrl = `${redirectUrl}&${searchStr.toString()}`;
      } else {
        redirectUrl = `${redirectUrl}?${searchStr.toString()}`;
      }
    }
    window.parent.postMessage({
      type: pmData.type,
      content: {
        redirectUrl,
      },
    });
    // window.open(redirectUrl);
  }
};

// 获取国际化的值
export const getL10nValue = (localeObject: {
  [key: string]: string;
}): string => {
  const language = window.navigator.language
    .replace('-', '_')
    .toLocaleLowerCase();
  for (const locale in localeObject) {
    if (language === locale.toLowerCase()) {
      return localeObject[locale];
    }
  }

  for (const locale in localeObject) {
    if (localeObject[locale]) {
      return localeObject[locale];
    }
  }
  return '';
};

// 扁平化TreeNode树形结构,可传入回调函数自定义操作
export const flatTreeNode = (
  tree: TreeNode[],
  cb: (node: TreeNode) => void,
) => {
  if (!tree || !tree.length) return;
  tree.forEach((node) => {
    cb(node);
    flatTreeNode(node.children || [], cb);
  });
};

export const spanConfig: BaseQueryFilterProps['span'] = {
  xs: 24,
  sm: 24,
  md: 12,
  lg: 12,
  xl: 8,
  xxl: 8,
};

// 处理上传文件格式
export const normFile = (e: any) => {
  // console.log('Upload event:', e);
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

// 未完工
export function transferMapToSelelctOptionList<KEY, VALUE>(
  map: Map<KEY, VALUE>,
): SelectOption<KEY>[] {
  const options: SelectOption<KEY>[] = [];
  for (const [key, val] of map.entries()) {
    options.push({ value: key, label: `${val}` });
  }
  return options;
}

export function transferObjectToSelelctOptionList<KEY>(
  obj: any,
): SelectOption<KEY>[] {
  const options: SelectOption<KEY>[] = [];
  for (const key in obj) {
    if (obj[key]) {
      options.push({ value: key as KEY, label: `${obj[key]}` });
    }
  }
  return options;
}

export const getCycleTimeOptionList = (
  minValue: number = 0,
): SelectOption[] => {
  const durations = [0, 1, 7, 30];
  const options: SelectOption[] = durations.map((duration) => ({
    value: duration,
    label: `${duration}`,
    disabled: minValue > duration,
  }));
  return options;
};

/**
 * @description 将数字or字符串数组转换为SelectOption数组
 */
export const transferArrayToSelectOption = (
  list: (string | number)[] = [],
): SelectOption[] => {
  return list.map((item) => ({
    value: item,
    label: `${item}`,
  }));
};

/**
 * @description 将数字or字符串数组转换为Object
 */
export const transferArrayToObject = (
  list: (string | number)[] = [],
): { [key: string]: string | number } => {
  const obj: { [key: string]: string | number } = {};
  list.forEach((item) => {
    obj[item] = item;
  });
  return obj;
};

/**
 * 处理除法，避免精度丢失
 * @param dividend 被除数
 * @param divisor 除数
 * @returns 商
 */
export const divideWithPrecision = (
  dividend: number,
  divisor: number,
): number => {
  if (divisor === 0) return 0;
  return new Decimal(dividend).dividedBy(divisor).toNumber();
};

/**
 * 处理乘法，避免精度丢失
 * @param multipliers 乘数数组
 * @returns 乘积
 */
export const multiplyWithPrecision = (...multipliers: number[]): number => {
  return multipliers.reduce((result, current) => {
    return new Decimal(result).times(current).toNumber();
  }, 1);
};

/**
 * 格式化数字为固定两位小数
 * @param value 需要格式化的数值
 * @returns 格式化后的字符串
 */
export const formatToTwoDecimals = (value?: number): number => {
  if (!value) return 0;
  try {
    return Number(new Decimal(value).toFixed(2));
  } catch {
    return value;
  }
};
