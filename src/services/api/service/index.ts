import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const serviceApiOption: Record<
  | 'serviceList'
  | 'serviceListV2'
  | 'serviceUpdatingHistoryList'
  | 'serviceUpdatingHistoryListV2'
  | 'serviceTransferring'
  | 'serviceTransferringV2',
  RequestOption
> = {
  /**
   * 获取Service列表
   */
  serviceList: {
    url: '/adm/:device/service/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取Service列表 - 服务解耦 - v2.0.0
  serviceListV2: {
    url: '/adm/bs/devicepackage/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  serviceUpdatingHistoryList: {
    url: '/adm/:device/service/change/history',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  serviceUpdatingHistoryListV2: {
    url: '/adm/bs/devicepackage/service/change/history',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  serviceTransferring: {
    url: '/adm/:device/service/transfer',
    option: {
      ...defaultOptions,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: RequestMethod.Post,
    },
  },
  serviceTransferringV2: {
    url: '/adm/bs/devicepackage/transfer',
    option: {
      ...defaultOptions,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: RequestMethod.Post,
    },
  },
};
