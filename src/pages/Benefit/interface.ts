/**
 * 权益表管理页面数据结构定义
 */

import { BenefitValueTypeEnum } from '@/api/benefit/utils';
import { SuitableDeviceTypeEnum } from '@/api/device/types';
import { LocaleContentForm } from '@/components/LocaleContentModal/interface';
import { UploadFile } from 'antd';

export interface UrlParam {
  deviceType: string;
  name?: string;
}

// 权益预设值数据结构
export interface BenefitAttributeFormData {
  isDefault: boolean;
  benefitId?: number;
  normalValue?: string;
  specialValue?: string;
}

// 本地化权益配置数据结构
export interface LocalizationFormData extends LocaleContentForm {
  name: string;
  alias: string;
  description: string;
  introduction: string;
  configImageList: UploadFile[];
  valueType: BenefitValueTypeEnum;
  defaultValueList: BenefitAttributeFormData[];
  booleanDefaultValueList: BenefitAttributeFormData[];
}

// 权益表创建/更新参数
export type BenefitFormData = LocalizationFormData & {
  isCore: boolean;
  deviceTypeList: SuitableDeviceTypeEnum[];
};

// 权益表翻译数据
export interface BenefitTableTranslation {
  locale: string;
  name: string;
  description: string;
  introduction: string;
}

// 权益属性翻译数据
export interface BenefitAttributeTranslation {
  locale: string;
  attributeText: string;
}

// 权益表搜索表单数据
export interface BenefitTableForm {
  name?: string;
}
