import { fetchDeviceCapacityList } from '@/models/device/fetch';
import {
  DeviceCapacity,
  DeviceCapacityListParam,
} from '@/models/device/interface';
import { initDeviceCapacityListParam } from '@/models/device/util';
import { antdUtils } from '@/utils/antd.util';
import { Paginator, initPaginator } from '@/utils/request';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<DeviceCapacity[]>([]);
  const [listParam, setListParam] = useState<DeviceCapacityListParam>(
    initDeviceCapacityListParam,
  );
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);

  // 获取列表数据
  const requestDeviceCapacityList = async (
    _listParam: DeviceCapacityListParam,
  ) => {
    const { items, ...rest } = await fetchDeviceCapacityList(_listParam);
    setDataList(items);
    setPaginator(rest);
  };

  // 编辑
  // const editDeviceCapacity = (id: number = 0) => {
  //   history.push(`/service/capacity/edit/${id}`);
  // };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;
    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<DeviceCapacity>> = [
    {
      title: '设备型号',
      dataIndex: 'deviceType',
      width: 220,
    },
    {
      title: '全部能力',
      dataIndex: 'allCapacties',
      // width: 300,
      render: (_, row) =>
        (row.allCapacities || []).map((item) => (
          <div key={item.type}>{item.name}</div>
        )),
    },
    {
      title: '初始化能力',
      dataIndex: 'initialCapacities',
      // width: 300,
      render: (_, row) =>
        (row.initialCapacities || []).map((item) => (
          <div key={item.type}>
            {item.name} {item.cycleTime}天循环
          </div>
        )),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 170,
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '修改时间',
      width: 170,
      dataIndex: 'updateTime',
      render: (_, row) =>
        row.updateTime
          ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   width: 80,
    //   render: (_, row) => (
    //     <Button type="link" onClick={() => editDeviceCapacity(row.id)}>
    //       查看
    //     </Button>
    //   ),
    // },
  ];

  useEffect(() => {
    requestDeviceCapacityList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<DeviceCapacity>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={false}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          ...antdUtils.transferPaginatorToTablePagination(paginator),
          onChange: onPaginationChanged,
        }}
        // toolbar={{
        //   actions: [
        //     <Button
        //       key="add"
        //       type="primary"
        //       onClick={() => editDeviceCapacity()}
        //     >
        //       新增
        //     </Button>,
        //   ],
        // }}
      />
      {/* {selectedCapacity ? (
        <Edit
          id={selectedCapacity.id}
          onOk={onEditOk}
          selectedBenefitList={selectedCapacity.allCapacities}
          onCancel={onEditCancel}
        />
      ) : (
        ''
      )} */}
    </>
  );
};

export default List;
