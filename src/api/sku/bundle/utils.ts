/**
 * 组合SKU管理相关的工具函数和常量
 */

import { SelectOption } from '@/models/common.interface';
import {
  BundleSkuListParam,
  OperateTypeEnum,
  SaleStatusEnum,
  ServiceTimeUnitEnum,
} from './types';

// 组合SKU列表初始查询参数
export const initBundleSkuListParam: BundleSkuListParam = {
  offset: 0,
  limit: 20,
};

// 服务时长单位名称映射
export const serviceTimeUnitNameMap = {
  [ServiceTimeUnitEnum.YEAR]: '年',
  [ServiceTimeUnitEnum.MONTH]: '月',
  [ServiceTimeUnitEnum.DAY]: '天',
};

// 服务时长单位选项
export const serviceTimeUnitOptions: SelectOption[] = [
  { label: '天', value: ServiceTimeUnitEnum.DAY },
  { label: '月', value: ServiceTimeUnitEnum.MONTH },
  { label: '年', value: ServiceTimeUnitEnum.YEAR },
];

// 销售状态名称映射
export const saleStatusNameMap = {
  [SaleStatusEnum.OFFLINE]: '下架',
  [SaleStatusEnum.ONLINE]: '上架',
};

// 销售状态选项
export const saleStatusOptions: SelectOption[] = [
  { label: '下架', value: SaleStatusEnum.OFFLINE },
  { label: '上架', value: SaleStatusEnum.ONLINE },
];

// 操作类型名称映射
export const operateTypeNameMap = {
  [OperateTypeEnum.CREATE]: '创建',
  [OperateTypeEnum.UPDATE]: '更新',
  [OperateTypeEnum.UPDATE_STATUS]: '状态更新',
};

// 是否自动续费选项
export const isRenewOptions: SelectOption[] = [
  { label: '否', value: 0 },
  { label: '是', value: 1 },
];

/**
 * 获取服务时长单位文本
 * @param unit 服务时长单位
 * @returns 单位文本
 */
export const getServiceTimeUnitText = (unit: ServiceTimeUnitEnum): string => {
  return serviceTimeUnitNameMap[unit] || '未知';
};

/**
 * 获取销售状态文本
 * @param status 销售状态
 * @returns 状态文本
 */
export const getSaleStatusText = (status: SaleStatusEnum): string => {
  return saleStatusNameMap[status] || '未知';
};

/**
 * 获取操作类型文本
 * @param type 操作类型
 * @returns 操作类型文本
 */
export const getOperateTypeText = (type: OperateTypeEnum): string => {
  return operateTypeNameMap[type] || '未知';
};

/**
 * 格式化服务时长显示文本
 * @param time 时长数值
 * @param unit 时长单位
 * @returns 格式化后的文本
 */
export const formatServiceTimeText = (
  time: number,
  unit: ServiceTimeUnitEnum,
): string => {
  const unitText = getServiceTimeUnitText(unit);
  return `${time}${unitText}`;
};

/**
 * 判断是否为自动续费的文本
 * @param isRenew 是否自动续费标识
 * @returns 是否自动续费的文本
 */
export const isRenewText = (isRenew: number): string => {
  return isRenew === 1 ? '是' : '否';
};
