import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const historyApiOption: Record<
  | 'virtualCardDistributionList'
  | 'virtualCardDistributionExport'
  | 'changeExpirationList'
  | 'changeExpirationListExport'
  | 'refundOrderList'
  | 'refundOrderListExport',
  RequestOption
> = {
  // 获取History列表
  virtualCardDistributionList: {
    url: '/adm/bs/history/cardCouponHistory',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  virtualCardDistributionExport: {
    url: '/adm/bs/history/exportCardList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 修改效期记录列表
  changeExpirationList: {
    url: '/adm/bs/history/planExpirationChangeHistory',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  changeExpirationListExport: {
    url: '/adm/bs/history/exportPlanExpirationChangeList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 订单退款记录列表
  refundOrderList: {
    url: '/adm/bs/history/refundHistory',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  refundOrderListExport: {
    url: '/adm/bs/history/exportRefundList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
