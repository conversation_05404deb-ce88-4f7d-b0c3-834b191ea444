import {
  fetchDevicepackageDeviceLink,
  fetchDevicepackageDevices,
} from '@/models/devicepackage/fetch';
import {
  DevicepackageDevice,
  DevicepackageDeviceLinkParam,
  DevicepackageDevicesParam,
} from '@/models/devicepackage/interface';
import { ServicePlanStatusEnum } from '@/models/service/interface';
import { Button, Form, Modal, Radio, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';

type FormData = {
  deviceId: number;
};

type Props = {
  deviceType: string;
  userId: number;
  serviceId: number;
  showModal: boolean;
  onRelateDeviceSuccess: () => void;
  onClose?: (shouldRefresh?: boolean) => void;
};

const initFormData: FormData = {
  deviceId: 0,
};

const seviceInfoStatusMap: { [key in ServicePlanStatusEnum] } = {
  [ServicePlanStatusEnum.ACTIVE]: '服务中',
  [ServicePlanStatusEnum.DELETED]: '未开通',
  [ServicePlanStatusEnum.EXPIRED]: '已过期',
  [ServicePlanStatusEnum.NOT_YET_ACTIVE]: '未开通',
  [ServicePlanStatusEnum.PENDING]: '服务中',
};

const RelativeDevice: React.FC<Props> = ({
  userId,
  serviceId,
  showModal,
  deviceType,
  onRelateDeviceSuccess,
  onClose,
}: Props) => {
  const [form] = Form.useForm<FormData>();
  const [devicepackageDeviceList, setDevicepackageDeviceList] = useState<
    DevicepackageDevice[]
  >([]);

  const requestUserDeviceList = async () => {
    const param: DevicepackageDevicesParam = {
      userId,
      deviceType,
    };
    const list = await fetchDevicepackageDevices(param);
    setDevicepackageDeviceList(list);
  };

  const requestLinkDevice = async (formData: FormData) => {
    const param: DevicepackageDeviceLinkParam = {
      serviceId,
      userId,
      deviceId: formData.deviceId,
      deviceType,
    };
    await fetchDevicepackageDeviceLink(param);
    message.success('关联成功');
    onRelateDeviceSuccess();
    onClose?.(true);
  };

  const submit = (formData: FormData) => {
    if (!formData.deviceId) {
      message.error('请先选择至少一台设备');
      return;
    }
    requestLinkDevice(formData);
  };

  useEffect(() => {
    requestUserDeviceList();
  }, []);

  return (
    <>
      <Modal
        open={showModal}
        width={700}
        title="关联设备"
        footer={null}
        destroyOnClose
        onCancel={() => onClose?.(false)}
      >
        <Form form={form} initialValues={initFormData} onFinish={submit}>
          <Form.Item
            label="选择设备"
            name="deviceId"
            rules={[{ required: true, message: '请选择设备' }]}
          >
            <Radio.Group>
              {devicepackageDeviceList.map((item, index) => (
                <div key={String(index)}>
                  <Radio
                    value={item.device.deviceId}
                    disabled={
                      item.serviceStatus !==
                      ServicePlanStatusEnum.NOT_YET_ACTIVE
                    }
                    style={{ marginBottom: 16 }}
                  >
                    <Space size={16} align="center">
                      <div>设备名称：{item.device.deviceName}</div>
                      <div>SN：{item.device.sn}</div>
                      <div>{seviceInfoStatusMap[item.serviceStatus]}</div>
                    </Space>
                  </Radio>
                </div>
              ))}
            </Radio.Group>
          </Form.Item>
          <Space style={{ textAlign: 'center' }}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
            <Button
              type="primary"
              htmlType="button"
              onClick={() => onClose?.(false)}
            >
              取消
            </Button>
          </Space>
        </Form>
      </Modal>
    </>
  );
};

export default RelativeDevice;
