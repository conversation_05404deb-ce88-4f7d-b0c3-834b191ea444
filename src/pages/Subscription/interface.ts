import { SubscriptionStateEnum } from '@/models/subscription/interface';
import { Dayjs } from 'dayjs';

export interface SubscriptionUrlParam {
  subscriptionNo?: string;
  userId?: string;
  deviceId?: number;
  state?: SubscriptionStateEnum;
  deviceType?: string;
  startTime?: number;
  endTime?: number;
}

export interface SubscriptionTableForm {
  subscriptionNo?: string;
  userId?: string;
  deviceId?: number;
  state?: SubscriptionStateEnum;
  deviceType?: string;
  subscribedTime?: Dayjs[];
}
