import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const cardApiOption: Record<
  | 'couponCardList'
  | 'couponCardCreation'
  | 'couponCardDetail'
  | 'couponCardCodeList'
  | 'couponCardExportByBatchId'
  | 'couponCardExportByListParam'
  | 'couponCardAbandon'
  | 'couponCardMobileUpload'
  | 'couponCardMobileFileValidtor',
  RequestOption
> = {
  // 获取Card列表
  couponCardList: {
    url: '/adm/bs/card/coupon/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 创建Card
  couponCardCreation: {
    url: '/adm/bs/card/coupon',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据Card的id获取详情数据
  couponCardDetail: {
    url: '/adm/bs/card/coupon/:id',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  couponCardCodeList: {
    url: '/adm/bs/card/coupon/code/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  couponCardExportByBatchId: {
    url: '/adm/bs/card/coupon/export',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  couponCardExportByListParam: {
    url: '/adm/bs/card/coupon/batch/export',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  couponCardAbandon: {
    url: '/adm/bs/card/coupon/code/abandon',
    option: {
      ...defaultOptions,
      // headers: {
      //   ...defaultOptions.headers,
      //   'Content-Type': 'application/x-www-form-urlencoded',
      // },
      requestType: 'form',
      method: RequestMethod.Put,
    },
  },
  couponCardMobileUpload: {
    url: '/adm/bs/card/coupon/upload',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // 上传必须设置headers为空
      headers: {},
    },
  },
  couponCardMobileFileValidtor: {
    url: '/adm/bs/card/coupon/upload/valid',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
