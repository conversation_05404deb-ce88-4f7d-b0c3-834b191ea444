/**
 * 增强的SKU权益选择器组件 v2.7.0
 * 支持设备类型过滤和权益值配置
 */

import {
  BenefitGroup,
  EnhancedProductSkuBenefit,
  fetchBenefitGroupDetail,
  fetchSkuBenefitsByDevice,
  SkuBenefitItem,
} from '@/api/benefit';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  Alert,
  Card,
  Checkbox,
  Col,
  Divider,
  Image,
  message,
  Row,
  Select,
  Space,
  Typography,
} from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
// 设备类型选项
const deviceTypeOptions = [
  { label: 'T6', value: 'T6' },
  { label: 'T5', value: 'T5' },
  { label: 'D4SH', value: 'D4SH' },
  { label: 'D4H', value: 'D4H' },
  { label: 'W5', value: 'W5' },
  { label: 'D3', value: 'D3' },
  { label: 'T4', value: 'T4' },
  { label: 'T3', value: 'T3' },
];

const { Text, Title } = Typography;
const { Option } = Select;

interface EnhancedBenefitSelectorProps {
  value?: SkuBenefitItem[];
  onChange?: (value: SkuBenefitItem[]) => void;
  deviceType?: string; // 当前选择的设备类型
  onDeviceTypeChange?: (deviceType: string) => void;
  disabled?: boolean;
}

const EnhancedBenefitSelector: React.FC<EnhancedBenefitSelectorProps> = ({
  value = [],
  onChange,
  deviceType,
  onDeviceTypeChange,
  disabled = false,
}) => {
  const [selectedBenefits, setSelectedBenefits] =
    useState<SkuBenefitItem[]>(value);
  const [availableBenefits, setAvailableBenefits] = useState<
    EnhancedProductSkuBenefit[]
  >([]);
  const [benefitDetails, setBenefitDetails] = useState<BenefitGroup[]>([]);
  const [loading, setLoading] = useState(false);

  // 同步外部值变化
  useEffect(() => {
    setSelectedBenefits(value);
  }, [value]);

  // 根据设备类型加载可用权益
  const loadAvailableBenefits = useCallback(
    async (selectedDeviceType: string) => {
      if (!selectedDeviceType) {
        setAvailableBenefits([]);
        setBenefitDetails([]);
        return;
      }

      try {
        setLoading(true);

        // 获取设备类型对应的权益列表
        const benefits = await fetchSkuBenefitsByDevice(selectedDeviceType);
        setAvailableBenefits(benefits);

        // 获取权益详细信息（包含预设值）
        if (benefits.length > 0) {
          const benefitIds = benefits.map((b) => b.id);
          const details = await fetchBenefitGroupDetail(benefitIds);
          setBenefitDetails(details);
        }
      } catch (error) {
        message.error('加载权益列表失败');
        setAvailableBenefits([]);
        setBenefitDetails([]);
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 设备类型变化时重新加载权益
  useEffect(() => {
    if (deviceType) {
      loadAvailableBenefits(deviceType);
    }
  }, [deviceType, loadAvailableBenefits]);

  // 获取权益的预设值选项
  const getBenefitValueOptions = useCallback(
    (benefitId: number) => {
      const detail = benefitDetails.find(
        (d) => Number(d.groupId) === benefitId,
      );
      if (!detail?.attributes) return [];

      return detail.attributes.map((attr) => ({
        label: attr.attributeText,
        value: attr.attributeText,
        isDefault: attr.isDefaultAttribute === 1,
      }));
    },
    [benefitDetails],
  );

  // 处理权益选择变化
  const handleBenefitSelection = useCallback(
    (checkedBenefitIds: number[]) => {
      const newSelectedBenefits: SkuBenefitItem[] = checkedBenefitIds.map(
        (benefitId) => {
          // 查找已存在的配置
          const existing = selectedBenefits.find(
            (item) => item.benefitId === benefitId,
          );
          if (existing) {
            return existing;
          }

          // 创建新的权益配置，使用默认值
          const valueOptions = getBenefitValueOptions(benefitId);
          const defaultOption = valueOptions.find((opt) => opt.isDefault);

          return {
            benefitId,
            benefitValue:
              defaultOption?.value || valueOptions[0]?.value || '支持',
            sort: selectedBenefits.length + 1,
            handpick: false,
          };
        },
      );

      setSelectedBenefits(newSelectedBenefits);
      onChange?.(newSelectedBenefits);
    },
    [selectedBenefits, onChange, getBenefitValueOptions],
  );

  // 处理权益值变化
  const handleBenefitValueChange = useCallback(
    (benefitId: number, benefitValue: string) => {
      const newSelectedBenefits = selectedBenefits.map((item) =>
        item.benefitId === benefitId ? { ...item, benefitValue } : item,
      );

      setSelectedBenefits(newSelectedBenefits);
      onChange?.(newSelectedBenefits);
    },
    [selectedBenefits, onChange],
  );

  // 处理精选状态变化
  const handleHandpickChange = useCallback(
    (benefitId: number, handpick: boolean) => {
      const newSelectedBenefits = selectedBenefits.map((item) =>
        item.benefitId === benefitId ? { ...item, handpick } : item,
      );

      setSelectedBenefits(newSelectedBenefits);
      onChange?.(newSelectedBenefits);
    },
    [selectedBenefits, onChange],
  );

  // 获取已选择的权益ID列表
  const selectedBenefitIds = useMemo(
    () => selectedBenefits.map((item) => item.benefitId),
    [selectedBenefits],
  );

  return (
    <Card title="SKU权益配置" loading={loading}>
      {/* 设备类型选择 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Text strong>设备类型：</Text>
          <Select
            value={deviceType}
            onChange={onDeviceTypeChange}
            placeholder="请选择设备类型"
            disabled={disabled}
            style={{ width: '100%', marginTop: 8 }}
            options={deviceTypeOptions}
          />
        </Col>
        <Col span={16}>
          <Alert
            message="权益过滤说明"
            description="选择设备类型后，系统将自动过滤出该设备类型在权益管理中配置的所有权益选项"
            type="info"
            icon={<InfoCircleOutlined />}
            showIcon
          />
        </Col>
      </Row>

      <Divider />

      {/* 权益选择区域 */}
      {!deviceType ? (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <InfoCircleOutlined style={{ fontSize: 24, marginBottom: 8 }} />
          <div>请先选择设备类型以加载对应的权益列表</div>
        </div>
      ) : availableBenefits.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <div>该设备类型暂无可用权益</div>
        </div>
      ) : (
        <div>
          <Title level={5}>可选权益列表</Title>

          {/* 权益选择列表 */}
          <Checkbox.Group
            value={selectedBenefitIds}
            onChange={handleBenefitSelection}
            disabled={disabled}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {availableBenefits.map((benefit) => {
                const isSelected = selectedBenefitIds.includes(benefit.id);
                const selectedItem = selectedBenefits.find(
                  (item) => item.benefitId === benefit.id,
                );
                const valueOptions = getBenefitValueOptions(benefit.id);

                return (
                  <Card
                    key={benefit.id}
                    size="small"
                    style={{
                      border: isSelected
                        ? '1px solid #1890ff'
                        : '1px solid #d9d9d9',
                      backgroundColor: isSelected ? '#f6ffed' : 'transparent',
                    }}
                  >
                    <Row gutter={16} align="middle">
                      <Col span={1}>
                        <Checkbox value={benefit.id} />
                      </Col>
                      <Col span={6}>
                        <Space>
                          {benefit.icon && (
                            <Image
                              src={benefit.icon}
                              alt={benefit.name}
                              width={24}
                              height={24}
                              preview={false}
                            />
                          )}
                          <Text strong>{benefit.name}</Text>
                        </Space>
                        <div style={{ color: '#666', fontSize: 12 }}>
                          {benefit.description}
                        </div>
                      </Col>
                      <Col span={8}>
                        {isSelected && (
                          <div>
                            <Text>权益值：</Text>
                            <Select
                              value={selectedItem?.benefitValue}
                              onChange={(value) =>
                                handleBenefitValueChange(benefit.id, value)
                              }
                              style={{ width: 120, marginLeft: 8 }}
                              disabled={disabled}
                            >
                              {valueOptions.map((option) => (
                                <Option key={option.value} value={option.value}>
                                  {option.label}
                                  {option.isDefault && (
                                    <Text type="secondary"> (默认)</Text>
                                  )}
                                </Option>
                              ))}
                            </Select>
                          </div>
                        )}
                      </Col>
                      <Col span={4}>
                        {isSelected && (
                          <Checkbox
                            checked={selectedItem?.handpick}
                            onChange={(e) =>
                              handleHandpickChange(benefit.id, e.target.checked)
                            }
                            disabled={disabled}
                          >
                            精选推荐
                          </Checkbox>
                        )}
                      </Col>
                      <Col span={5}>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          适用设备: {benefit.deviceTypes?.join(', ')}
                        </Text>
                      </Col>
                    </Row>
                  </Card>
                );
              })}
            </Space>
          </Checkbox.Group>

          {/* 选择统计 */}
          {selectedBenefits.length > 0 && (
            <div
              style={{
                marginTop: 16,
                padding: 12,
                backgroundColor: '#f0f2f5',
                borderRadius: 6,
              }}
            >
              <Text>
                已选择 <Text strong>{selectedBenefits.length}</Text> 个权益，
                其中{' '}
                <Text strong>
                  {selectedBenefits.filter((item) => item.handpick).length}
                </Text>{' '}
                个设为精选推荐
              </Text>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  注意：权益值为"不支持"的项目在对比表中将显示为空
                </Text>
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default EnhancedBenefitSelector;
