import { fetchProductSkuUpdatingLogs } from '@/models/product/fetch';
import {
  ProductSkuUpdatingLog,
  ProductSkuUpdatingLogsParam,
} from '@/models/product/interface';
import { Paginator, initPaginator } from '@/utils/request';
import { Modal, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';
import { initProductSkuUpdatingLogsParam } from '../util';

interface Props {
  skuId: number;
  showModal: boolean;
  onClose?: () => void;
}

const OperationHistory: React.FC<Props> = ({
  showModal,
  skuId,
  onClose,
}: Props) => {
  const [listParam, setListParam] = useState<ProductSkuUpdatingLogsParam>({
    ...initProductSkuUpdatingLogsParam,
    skuId,
  });
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [list, setList] = useState<ProductSkuUpdatingLog[]>([]);

  const requestHistoryList = async (
    _listParam: ProductSkuUpdatingLogsParam,
  ) => {
    const { items, ...rest } = await fetchProductSkuUpdatingLogs(_listParam);
    setPaginator(rest);
    setList(items);
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let current = page;
    if (pageSize !== listParam.limit) {
      current = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (current - 1) * pageSize,
    });
  };

  const columns: ColumnsType<ProductSkuUpdatingLog> = [
    {
      title: '操作',
      dataIndex: 'operateType',
      width: 80,
      render: (_, row) => row.operateType || '--',
    },
    {
      title: '操作字段',
      dataIndex: 'field',
      width: 100,
      render: (_, row) => row.field || '--',
    },
    {
      title: '变更前',
      dataIndex: 'before',
      width: 100,
      render: (_, row) => row.before || '--',
    },
    {
      title: '变更后',
      dataIndex: 'after',
      width: 500,
      render: (_, row) =>
        row.after ? (
          <div>
            {row.after.split('\n').map((item) => (
              <React.Fragment key={item}>{item}</React.Fragment>
            ))}
          </div>
        ) : (
          '--'
        ),
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'modifiedTime',
      width: 180,
      render: (_, row) => dayjs(row.modifiedTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  useEffect(() => {
    if (listParam && listParam.skuId) {
      requestHistoryList(listParam);
    }
  }, [listParam]);

  return (
    <Modal
      width={950}
      destroyOnClose
      open={showModal}
      footer={null}
      onCancel={() => isFunction(onClose) && onClose()}
    >
      <Table<ProductSkuUpdatingLog>
        dataSource={list}
        columns={columns}
        size="small"
        rowKey="id"
        scroll={{
          x: columns.reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          total: paginator.total,
          pageSize: paginator.limit,
          showSizeChanger: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default OperationHistory;
