/**
 * 权益表相关的工具函数和常量
 */

import { SelectOption } from '@/models/common.interface';
import { BenefitListParam } from './types';

// 权益表列表初始查询参数
export const initBenefitListParam: BenefitListParam = {
  offset: 0,
  limit: 20,
};

// 权益类型枚举
export enum BenefitValueTypeEnum {
  BOOLEAN = 0,
  ENUM = 1,
}

// 权益类型名称映射
export const benefitValueTypeNameMap = {
  [BenefitValueTypeEnum.BOOLEAN]: '布尔类型',
  [BenefitValueTypeEnum.ENUM]: '枚举类型',
};

// 权益适用设备类型选项
export const benefitSuitableDeviceTypeOptions: SelectOption[] = [
  { label: 'T5', value: 't5' },
  { label: 'T6', value: 't6' },
  { label: 'D4SH', value: 'd4sh' },
  { label: 'D4H', value: 'd4h' },
];

// 权益状态枚举
export enum BenefitStatusEnum {
  DISABLED = 0,
  ENABLED = 1,
}

// 权益状态名称映射
export const benefitStatusNameMap = {
  [BenefitStatusEnum.DISABLED]: '禁用',
  [BenefitStatusEnum.ENABLED]: '启用',
};

// 权益状态选项
export const benefitStatusOptions: SelectOption[] = [
  { label: '禁用', value: BenefitStatusEnum.DISABLED },
  { label: '启用', value: BenefitStatusEnum.ENABLED },
];

// 核心权益选项
export const coreBenefitOptions: SelectOption[] = [
  { label: '否', value: 0 },
  { label: '是', value: 1 },
];

/**
 * 获取权益状态文本
 * @param status 权益状态
 * @returns 状态文本
 */
export const getBenefitStatusText = (status: number): string => {
  return benefitStatusNameMap[status as BenefitStatusEnum] || '未知';
};

/**
 * 获取权益类型文本
 * @param type 权益类型
 * @returns 类型文本
 */
export const getBenefitTypeText = (type: string): string => {
  return (
    benefitValueTypeNameMap[type as unknown as BenefitValueTypeEnum] || '未知'
  );
};

/**
 * 判断是否为核心权益
 * @param isCoreBenefit 核心权益标识
 * @returns 是否为核心权益的文本
 */
export const isCoreBenefitText = (isCoreBenefit: number): string => {
  return isCoreBenefit === 1 ? '是' : '否';
};
