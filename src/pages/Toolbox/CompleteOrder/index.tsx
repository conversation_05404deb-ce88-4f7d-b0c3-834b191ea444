import { ApiSuccessEnum } from '@/models/common.interface';
import { fetchCompleteOrder } from '@/models/order/fetch';
import { ProCard } from '@ant-design/pro-components';
import { Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';

const CompleteOrder: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [orderId, setOrderId] = useState('');
  const submit = async () => {
    if (!orderId) {
      message.warning('请先提供订单号');
      return;
    }
    try {
      const result = await fetchCompleteOrder(orderId);
      if (!result || result === ApiSuccessEnum.success) {
        message.success('订单激活成功');
      } else if (result) {
        message.success('服务补偿成功');
      }
      setOrderId('');
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <ProCard title="设备服务套餐补偿">
      <Form.Item
        label="订单号"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Input
          placeholder="请输入订单号"
          value={orderId}
          onChange={(ev) => setOrderId(ev.target.value)}
        />
      </Form.Item>
      <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
        <Button
          loading={loading}
          type="primary"
          htmlType="button"
          onClick={submit}
        >
          确认并提交
        </Button>
      </Form.Item>
    </ProCard>
  );
};

export default CompleteOrder;
