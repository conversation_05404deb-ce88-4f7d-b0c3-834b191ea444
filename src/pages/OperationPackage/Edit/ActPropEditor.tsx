import { actPropApiOption } from '@/services/api/actProp';
import { requestService } from '@/utils/request';
import { Form, Input, InputNumber, Modal } from 'antd';
import React, { useState } from 'react';
import { ActPropForm } from '../interface';

interface Props {
  onCancel: () => void;
  onConfirm: (formData: ActPropForm) => void;
}

const ActPropEditor: React.FC<Props> = ({ onConfirm, onCancel }: Props) => {
  const [form] = Form.useForm<ActPropForm>();
  const [isNameRepeat, setIsNameRepeat] = useState(false);

  const cancel = () => {
    onCancel();
  };

  const confirm = async () => {
    const formData = form.getFieldsValue();
    await onConfirm(formData);

    // 这里需要根据请求返回的错误信息来判断活动名称是否重复的问题
    const errorResult =
      requestService.errorInfoObj[actPropApiOption.actPropCreation.url];
    if (errorResult && errorResult.code === 616003) {
      setIsNameRepeat(true);
    }
  };

  return (
    <Modal open title="新建活动属性" onOk={confirm} onCancel={cancel}>
      <Form form={form}>
        <Form.Item
          name="name"
          label="活动属性"
          rules={[{ required: true, message: '请输入活动属性名称' }]}
          validateStatus={isNameRepeat ? 'error' : ''}
          help={isNameRepeat ? '活动名称重复，请重新输入' : ''}
        >
          <Input
            placeholder="请输入活动属性名称"
            onChange={() => setIsNameRepeat(false)}
          />
        </Form.Item>
        <Form.Item
          name="number"
          label="活动次数"
          rules={[{ required: true, message: '请输入活动次数' }]}
        >
          <InputNumber placeholder="请输入活动次数" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActPropEditor;
