import type { Data } from '@/api/common/types';
import type {
  Devi<PERSON>,
  DeviceInfo,
  DeviceInfoParam,
  DeviceListParam,
} from '@/api/device/types';
import { request } from '@/utils/request';
import { RequestMethod, RequestOption } from '@mantas/request';

/**
 * 设备服务 - 按model模块分类封装
 */
export const deviceService = {
  /**
   * 获取设备列表
   */
  getDeviceList: (params: DeviceListParam): Promise<Data<Device>> => {
    return request('/api/devices', {
      method: RequestMethod.GET,
      params,
    } as RequestOption);
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo: (params: DeviceInfoParam): Promise<DeviceInfo> => {
    return request('/api/devices/info', {
      method: RequestMethod.GET,
      params,
    } as RequestOption);
  },

  /**
   * 获取设备详情
   */
  getDeviceDetail: (id: number): Promise<Device> => {
    return request(`/api/devices/${id}`, {
      method: RequestMethod.GET,
    } as RequestOption);
  },

  /**
   * 更新设备信息
   */
  updateDevice: (id: number, data: Partial<Device>): Promise<Device> => {
    return request(`/api/devices/${id}`, {
      method: RequestMethod.PUT,
      data,
    } as RequestOption);
  },
};
