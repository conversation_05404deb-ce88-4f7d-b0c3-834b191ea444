import { initPaginatorParam } from '@/utils/request';
import {
  CouponCardCodeListParam,
  CouponCardListParam,
  ExchangeStatusEnum,
  SendWayEnum,
} from './interface';

export const initCouponCardListParam: CouponCardListParam = {
  ...initPaginatorParam,
};

export const initCouponCardCodeListParam: CouponCardCodeListParam = {
  ...initPaginatorParam,
};

// 卡券状态
export const exchangeStatusNameObj: { [key in ExchangeStatusEnum]: string } = {
  [ExchangeStatusEnum.UNASSIGNED]: '未分配',
  [ExchangeStatusEnum.UNEXCHANGE]: '未兑换',
  [ExchangeStatusEnum.EXCHANGED]: '已兑换',
  [ExchangeStatusEnum.EXPIRED]: '已过期',
  [ExchangeStatusEnum.ABANDONED]: '已废弃',
};

// 发放方式
export const sendWayNameObj: { [key in SendWayEnum]: string } = {
  [SendWayEnum.MANUAL]: '手动发送',
  [SendWayEnum.AUTO]: '自动发送',
};
