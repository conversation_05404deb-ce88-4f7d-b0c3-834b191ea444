import { initPaginatorParam } from '@/utils/request';
import { OrderListParam, OrderStatusEnum, PayTypeEnum } from './interface';

export const initOrderListParam: OrderListParam = {
  ...initPaginatorParam,
};

// export const initOrderParam: OrderParam = {
// };

export const orderStatusObj: { [key in OrderStatusEnum]: string } = {
  [OrderStatusEnum.NONE]: '未知状态',
  [OrderStatusEnum.CREATED]: '已创建',
  [OrderStatusEnum.WAIT_DEDUCT]: '待扣款',
  [OrderStatusEnum.PAYING]: '支付中',
  [OrderStatusEnum.PAY_SUCCEED]: '付款成功',
  [OrderStatusEnum.PAY_FAILED]: '付款失败',
  [OrderStatusEnum.REFUND_SUCCEED]: '退款成功',
  [OrderStatusEnum.REFUND_FAILED]: '退款失败',
  [OrderStatusEnum.REFUNDING]: '退款中',
  [OrderStatusEnum.CANCELLED]: '手动取消',
  [OrderStatusEnum.CANCELLED_SYSTEM]: '系统取消',
  [OrderStatusEnum.FINISHED]: '已完结',
};

export const payTypeObj: { [key in PayTypeEnum]: string } = {
  [PayTypeEnum.ALIPAY]: '支付宝',
  [PayTypeEnum.WEIXIN]: '微信',
  [PayTypeEnum.PAYPAL]: 'Paypal',
  [PayTypeEnum.STRIPE]: 'Stripe',
  [PayTypeEnum.EXPERIENCE]: '免费领取',
  [PayTypeEnum.EXCHANGECOUPON]: '卡券兑换',
};
