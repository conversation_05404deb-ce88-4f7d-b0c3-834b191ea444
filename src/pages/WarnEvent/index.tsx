import { fetchEventList } from '@/models/WarnEvent/fetch';
import { Event, EventListParam } from '@/models/WarnEvent/interface';
import { initEventListParam } from '@/models/WarnEvent/util';
import { antdUtils } from '@/utils/antd.util';
import { Paginator, initPaginator } from '@/utils/request';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import Updater from './updater';

const List: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const [dataList, setDataList] = useState<Event[]>([]);
  // const [dataChanged, setDataChanged] = useState<boolean>(false);
  const [listParam, setListParam] =
    useState<EventListParam>(initEventListParam);
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [selectedRow, setSelectedRow] = useState<Event>();

  // 获取列表数据
  const requestEventList = async (
    param: EventListParam = initEventListParam,
  ) => {
    const { items, ...rest } = await fetchEventList(param);
    setPagination(rest);
    setDataList(items);
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;
    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<Event>> = [
    {
      title: 'ID',
      dataIndex: 'id',
      search: false,
      width: 100,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 150,
    },
    {
      title: '等级',
      dataIndex: 'level',
      width: 150,
    },
    {
      title: '最后发生时间',
      dataIndex: 'lastTime',
      width: 200,
      render: (_, row) => dayjs(row.lastTime).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '首次发生时间',
      dataIndex: 'createTime',
      width: 200,
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      search: false,
      width: 150,
    },
    {
      title: '内容',
      dataIndex: 'payload',
      search: false,
      width: 150,
      ellipsis: true,
    },
  ];

  useEffect(() => {
    if (listParam) requestEventList(listParam);
    // const _listParam: EventListParam = { ...initEventListParam };

    // setListParam(_listParam);
    // setDataChanged(false);
  }, [listParam]);

  return (
    <>
      <ProTable<Event>
        actionRef={tableRef}
        dataSource={dataList}
        columns={columns}
        rowClassName={() => {
          return 'red-1';
        }}
        defaultSize="small"
        rowKey="id"
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          ...antdUtils.transferPaginatorToTablePagination(pagination),
          onChange: onPaginationChanged,
        }}
        onRow={(row) => {
          return {
            onClick: () => {
              setSelectedRow(row);
              setDrawerVisit(true);
            },
          };
        }}
      />

      <Updater
        data={selectedRow}
        visit={drawerVisit}
        onDataChanged={() => {
          // setDataChanged(true);
          requestEventList(listParam);
        }}
        onVisitChanged={(visit: boolean) => {
          setDrawerVisit(visit);
        }}
      />
    </>
  );
};

export default List;
