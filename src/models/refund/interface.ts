import { OrderStatusEnum } from '../order/interface';

// 退款方式
export enum RefundTypeEnum {
  PARTIAL = 0,
  ALL = 1,
}

export enum RefundStatusEnum {
  // 成功
  SUCCEEDED = 'SUCCEEDED',
  // 失败
  FAILED = 'FAILED',
  // 退款处理中
  PENDING = 'PENDING',
}

export interface AvailableRefundInfo {
  availableRefundAmount: number;
  paidAmount: number;
  platform: string;
  tradeNo: string;
  refundable: boolean;
  orderStatus: OrderStatusEnum;
}

export interface RefundParam {
  orderId: string;
  amount: number;
  reason: string;
}

export interface RefundResult {
  orderId: string;
  refundId: string;
  status: RefundStatusEnum;
}

export interface RefundInfo {
  refundId: string;
  orderId: string;
  status: RefundStatusEnum;
  refundAmount: number;
  operator: string;
  createdTime: string;
}

export interface RefundListInfo {
  refundId: string;
  orderId: string;
  status: RefundStatusEnum;
  refundAmount: number;
  operator: string;
  createdTime: string;
  timezone: string;
  refundType: RefundTypeEnum;
  currency: string;
}

export interface RefundDetail {
  refundCount: number;
  totalRefundAmount: number;
  refundList: RefundListInfo[];
}
