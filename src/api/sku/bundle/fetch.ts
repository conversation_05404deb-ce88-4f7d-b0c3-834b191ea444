/**
 * 组合SKU管理相关的API请求函数
 */

import { ApiSuccessEnum } from '@/models/common.interface';
import request, { Data } from '@/utils/request';
import { bundleSkuApi } from './api';
import type {
  BundleSkuChangeLogsParam,
  BundleSkuListParam,
  CloudStorageProductSKUBundleChange,
  ProductSKUBundle,
  ProductSkuBundleCreationParams,
  ProductSKUBundleUpdateParams,
  SaleStatusEnum,
} from './types';

/**
 * 查询组合SKU列表
 * @param params 查询参数
 * @returns 组合SKU列表数据
 */
export const fetchBundleSkuList = (
  params: BundleSkuListParam,
): Promise<Data<ProductSKUBundle>> => {
  const config = bundleSkuApi.list;
  config.option.params = params;
  return request(config.url, config.option);
};

/**
 * 获取组合SKU详情
 * @param id 组合SKU ID
 * @returns 组合SKU详情数据
 */
export const fetchBundleSkuDetail = (id: number): Promise<ProductSKUBundle> => {
  const config = bundleSkuApi.detail;
  config.option.params = { id };
  return request(config.url, config.option);
};

/**
 * 创建组合SKU
 * @param data 创建参数
 * @returns 创建结果
 */
export const fetchBundleSkuCreate = (
  param: ProductSkuBundleCreationParams,
): Promise<ApiSuccessEnum> => {
  const config = bundleSkuApi.create;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * 更新组合SKU
 * @param data 更新参数
 * @returns 更新结果
 */
export const fetchBundleSkuUpdate = (
  param: ProductSKUBundleUpdateParams,
): Promise<ApiSuccessEnum> => {
  const config = bundleSkuApi.update;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * 批量上下架组合SKU
 * @param ids 组合SKU ID列表
 * @param saleStatus 上下架状态 1:上架 0:下架
 * @returns 操作结果
 */
export const fetchBundleSkuUpdateSaleStatus = (
  ids: number[],
  saleStatus: SaleStatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = bundleSkuApi.updateSaleStatus;
  config.option.params = { saleStatus };
  config.option.data = ids;
  return request(config.url, config.option);
};

/**
 * 查询组合SKU变更记录
 * @param params 查询参数
 * @returns 变更记录列表
 */
export const fetchBundleSkuChangeLogs = (
  params: BundleSkuChangeLogsParam,
): Promise<CloudStorageProductSKUBundleChange[]> => {
  const config = bundleSkuApi.changeLogs;
  config.option.params = params;
  return request(config.url, config.option);
};
