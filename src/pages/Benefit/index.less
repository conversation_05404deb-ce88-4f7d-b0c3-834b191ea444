/**
 * 权益表管理页面样式
 */

.benefit-table-container {
  .ant-pro-table-list-toolbar-title {
    font-weight: 600;
  }

  .benefit-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 8px;
  }

  .benefit-image {
    border-radius: 4px;
    border: 1px solid #d9d9d9;
  }

  .attribute-tags {
    .ant-tag {
      margin: 2px;
      border-radius: 4px;
    }

    .default-attribute {
      background-color: #1890ff;
      color: white;
      border-color: #1890ff;
    }

    .normal-attribute {
      background-color: #f5f5f5;
      color: #595959;
      border-color: #d9d9d9;
    }

    .more-count {
      background-color: #f0f0f0;
      color: #8c8c8c;
      border-color: #d9d9d9;
    }
  }

  .device-type-tags {
    .ant-tag {
      margin: 2px;
      background-color: #e6f7ff;
      color: #1890ff;
      border-color: #91d5ff;
    }
  }

  .batch-actions {
    margin-bottom: 16px;
  }
}

.benefit-table-edit {
  .form-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #262626;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }
  }

  .image-upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    transition: border-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }

    .upload-hint {
      color: #8c8c8c;
      margin-top: 8px;
    }
  }

  .attribute-form-item {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: #fafafa;

    .attribute-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .attribute-title {
        font-weight: 600;
        color: #262626;
      }

      .remove-button {
        color: #ff4d4f;
      }
    }

    .attribute-fields {
      .ant-form-item {
        margin-bottom: 12px;
      }
    }
  }

  .add-attribute-button {
    width: 100%;
    height: 60px;
    border: 2px dashed #d9d9d9;
    background-color: #fafafa;
    color: #8c8c8c;
    font-size: 14px;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .device-type-select {
    width: 100%;
  }
}
