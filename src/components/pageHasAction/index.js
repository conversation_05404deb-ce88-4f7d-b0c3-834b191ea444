import { PROJECT_CONFIG } from '@/project.config';
import { isEmpty } from '@/utils/lang';
import { useSelector } from '@umijs/max';

/**
 * 是否包含功能权限
 */
export default (props) => {
  if (PROJECT_CONFIG.ignoreAuth) {
    return <>{props.children}</>;
  }

  const { action = '' } = props;
  if (isEmpty(action)) return null;

  const global = useSelector((state) => state.global);
  const { pageActions } = global || {};
  return <>{pageActions.includes(action) ? props.children : null}</>;
};
