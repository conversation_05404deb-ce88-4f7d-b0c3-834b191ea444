import { PaginatorParam } from '@/utils/request';
import { StatusEnum } from '../common.interface';
import { SuitableDeviceTypeEnum } from '../device/interface';

export enum OrderStatusEnum {
  /**
   * 初始状态
   */
  NONE = 'NONE',
  /**
   * 已创建
   */
  CREATED = 'CREATED',
  /**
   * 待扣款（订阅扣款）
   */
  WAIT_DEDUCT = 'WAIT_DEDUCT',
  /**
   * 付款中
   */
  PAYING = 'PAYING',
  /**
   * 支付成功
   */
  PAY_SUCCEED = 'PAY_SUCCEED',
  /**
   * 支付失败
   */
  PAY_FAILED = 'PAY_FAILED',
  /**
   * 退款成功
   */
  REFUND_SUCCEED = 'REFUND_SUCCEED',
  /**
   * 退款失败
   */
  REFUND_FAILED = 'REFUND_FAILED',
  /**
   * 退款失败
   */
  REFUNDING = 'REFUNDING',
  /**
   * 手动取消
   */
  CANCELLED = 'CANCELLED',
  /**
   * 系统取消
   */
  CANCELLED_SYSTEM = 'CANCELLED_SYSTEM',
  /**
   * 订单已完结, 不可再次操作
   */
  FINISHED = 'FINISHED',
}

export enum PayTypeEnum {
  /**
   * 支付宝
   */
  ALIPAY = 'Alipay',
  /**
   * 微信支付
   */
  WEIXIN = 'Weixin',
  /**
   * Paypal支付
   */
  PAYPAL = 'Paypal',
  /**
   * Stripe支付
   */
  STRIPE = 'Stripe',
  /**
   * 免费领取
   */
  EXPERIENCE = 'Experience',
  /**
   * 卡券兑换
   */
  EXCHANGECOUPON = 'ExchangeCoupon',
}

// 列表参数数据
export interface OrderListParam extends PaginatorParam {
  orderId?: string;
  skuName?: string;
  skuId?: number;
  skuCapacity?: string[];
  userId?: number;
  state?: OrderStatusEnum;
  paymentType?: PayTypeEnum;
  isReNew?: number;
  deviceType?: string;
  createTimeStart?: number;
  createTimeEnd?: number;
  isActivity?: StatusEnum;
  isUpgrade?: StatusEnum;

  tradeNo?: string;
  currency?: string;
}

// 列表数据
export interface Order {
  amount: number;
  currency: string;
  createTime: string;
  device: { deviceId: number; deviceType: SuitableDeviceTypeEnum };
  skuId: number;
  id: number;
  isReNew: boolean;
  orderId: string;
  payExpireTime: string;
  platform: PayTypeEnum;
  skuName: string;
  state: OrderStatusEnum;
  userId: number;
  activityName: string;
  upgradeServiceProductName: string;
  tradeNo: string;
  // 是否补差价/升级
  upgrade: StatusEnum;
  // 支付失败原因
  lsubscriptionDeductErrorMsg: string;
}

// 创建/编辑提交参数
// export interface OrderParam {}

// 详情数据
// export interface OrderDetail {}
