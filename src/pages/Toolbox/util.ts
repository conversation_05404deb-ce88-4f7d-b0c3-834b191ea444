import { ToolboxDetail, ToolboxParam } from '@/models/toolbox/interface';
import { ToolboxForm } from './interface';

export const initialToolboxForm: ToolboxForm = {};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: ToolboxForm,
  id?: number,
): ToolboxParam => {
  const param: ToolboxParam = {};
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: ToolboxDetail,
): ToolboxForm => {
  const formData: ToolboxForm = {};
  return formData;
};
