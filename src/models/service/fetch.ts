import { serviceApiOption } from '@/services/api/service';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  Service,
  ServiceListParam,
  ServiceListV2Param,
  ServiceTransferParam,
  ServiceTransferV2Param,
  ServiceUpdatingHistory,
  ServiceUpdatingHistoryListParam,
  ServiceUpdatingHistoryListV2Param,
  ServiceUpdatingHistoryV2,
  ServiceV2,
} from './interface';

/**
 * 获取Service列表
 */
export const fetchServiceList = (
  param: ServiceListParam,
  device: string,
): Promise<Data<Service>> => {
  const config = serviceApiOption.serviceList;
  config.option.restApi = { device };
  config.option.data = { ...param };
  // config.option.requestType = 'form';
  return request(config.url, config.option);
};

/**
 * 获取Service列表 - 服务解耦 - v2.0.0
 */
export const fetchServiceListV2 = (
  param: ServiceListV2Param,
): Promise<Data<ServiceV2>> => {
  const config = serviceApiOption.serviceListV2;
  config.option.params = { ...param };
  // config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取Service变更历史列表
export const fetchServiceUpdatingHistoryList = (
  param: ServiceUpdatingHistoryListParam,
  device: string,
): Promise<Data<ServiceUpdatingHistory>> => {
  const config = serviceApiOption.serviceUpdatingHistoryList;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.restApi = { device };
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 获取Service变更历史列表 - 服务解耦 - v2.0.0
export const fetchServiceUpdatingHistoryListV2 = (
  param: ServiceUpdatingHistoryListV2Param,
): Promise<Data<ServiceUpdatingHistoryV2>> => {
  const config = serviceApiOption.serviceUpdatingHistoryListV2;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.params = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 转移服务
export const fetchServiceTransferring = (
  param: ServiceTransferParam,
  device: string,
): Promise<ApiSuccessEnum> => {
  const config = serviceApiOption.serviceTransferring;
  config.option.restApi = { device };
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 转移服务 - 服务解耦 - v2.0.0
export const fetchServiceTransferringV2 = (
  param: ServiceTransferV2Param,
): Promise<ApiSuccessEnum> => {
  const config = serviceApiOption.serviceTransferringV2;
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
