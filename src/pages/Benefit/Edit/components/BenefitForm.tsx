/**
 * 权益基本信息表单组件
 */

import { BenefitValueTypeEnum } from '@/api/benefit/utils';
import Uploader from '@/components/Uploader';
import { normFile } from '@/models/common.util';
import {
  InfoCircleFilled,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  Radio,
  Row,
  Typography,
} from 'antd';
import { BenefitFormData } from '../../interface';
const { TextArea } = Input;

interface BenefitFormProps {
  form?: FormInstance<BenefitFormData>; // 可选的modal form实例
}

const BenefitForm: React.FC<BenefitFormProps> = ({ form }) => {
  // modalForm现在可以在需要时使用，例如：
  // const handleSomeAction = () => {
  //   if (modalForm) {
  //     const currentValues = modalForm.getFieldsValue();
  //     modalForm.setFieldsValue({ name: '新值' });
  //   }
  // };

  const valueType = Form.useWatch('valueType', form);
  const isCore = Form.useWatch('isCore', form);
  // const booleanDefaultValueList = Form.useWatch(
  //   'booleanDefaultValueList',
  //   form,
  // );
  // const defaultValueList = Form.useWatch('defaultValueList', form);

  // useEffect(() => {
  //   console.log(
  //     cloneDeep(booleanDefaultValueList),
  //     cloneDeep(defaultValueList),
  //   );
  // }, [booleanDefaultValueList, defaultValueList]);

  const handleDefaultValueChange = (index: number, checked: boolean) => {
    if (!checked || !form) return;

    const currentValues = form.getFieldsValue();
    const listFieldName =
      valueType === BenefitValueTypeEnum.BOOLEAN
        ? 'booleanDefaultValueList'
        : 'defaultValueList';
    const currentList = currentValues[listFieldName] || [];

    const updatedList = currentList.map((item: any, i: number) => ({
      ...item,
      isDefault: i === index,
    }));

    form.setFieldsValue({
      [listFieldName]: updatedList,
    });
  };

  return (
    <>
      {/* 权益名称 */}
      <Form.Item
        name="name"
        label="权益名称"
        rules={[{ required: true, message: '请输入权益名称' }]}
      >
        <Input placeholder="请输入权益名称" />
      </Form.Item>

      {/* 权益别名 */}
      <Form.Item name="alias" label="权益别名">
        <Input placeholder="请输入权益别名" />
      </Form.Item>

      {/* 权益介绍 */}
      <Form.Item name="introduction" label="权益介绍">
        <TextArea rows={3} placeholder="请输入权益介绍" />
      </Form.Item>

      {/* 权益描述 */}
      <Form.Item
        name="description"
        label="权益描述"
        rules={[{ required: true, message: '请输入权益描述' }]}
      >
        <TextArea
          rows={4}
          placeholder="请输入权益描述"
          showCount
          maxLength={500}
        />
      </Form.Item>

      {/* 配置图片 */}
      <Form.Item
        name="configImageList"
        label="权益配图"
        getValueFromEvent={normFile}
        extra={
          <Typography.Text type="secondary">
            <InfoCircleFilled style={{ color: '#1890ff', marginRight: 8 }} />
            权益配图尺寸为375px*360px，支持 .jpg, .gif, .png, .mp4 格式
          </Typography.Text>
        }
        shouldUpdate
        valuePropName="fileList"
        rules={[{ required: true, message: '请选择标贴图片' }]}
      >
        <Uploader />
      </Form.Item>

      {/* 权益类型 */}
      <Form.Item
        name="valueType"
        label="权益类型"
        rules={[{ required: true, message: '请选择权益类型' }]}
      >
        <Radio.Group block optionType="button">
          <Radio value={BenefitValueTypeEnum.BOOLEAN}>布尔型</Radio>
          <Radio value={BenefitValueTypeEnum.ENUM}>枚举型</Radio>
        </Radio.Group>
      </Form.Item>

      {/* 默认值配置 - 根据权益类型动态展示 */}
      <div>
        <Form.Item
          label={
            <div className="flex items-center gap-2">
              <span>预设值配置</span>
              <Typography.Text type="secondary" className="text-sm">
                <InfoCircleFilled
                  style={{ color: '#1890ff', marginRight: 4 }}
                />
                每个权益值支持两种格式，前者展示在精选权益中，后者展示在权益对比中
              </Typography.Text>
            </div>
          }
          className="mb-4"
        >
          <Form.List
            name={
              valueType === BenefitValueTypeEnum.BOOLEAN
                ? 'booleanDefaultValueList'
                : 'defaultValueList'
            }
          >
            {(fields, { add, remove }) => (
              <>
                <div className="space-y-4">
                  {fields.map((field) => (
                    <Row key={field.key} gutter={16} align="bottom">
                      {valueType === BenefitValueTypeEnum.BOOLEAN ? (
                        <>
                          <Col span={18}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'normalValue']}
                              className="mb-0"
                              rules={[
                                { required: true, message: '请输入布尔值' },
                              ]}
                            >
                              <Input
                                placeholder="布尔值（如：支持、不支持）"
                                disabled
                                className="bg-gray-100"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'isDefault']}
                              valuePropName="checked"
                              className="mb-0"
                            >
                              <Radio
                                onChange={(e) =>
                                  handleDefaultValueChange(
                                    field.name,
                                    e.target.checked,
                                  )
                                }
                              >
                                设为默认值
                              </Radio>
                            </Form.Item>
                          </Col>
                        </>
                      ) : (
                        <>
                          <Col span={9}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'specialValue']}
                              rules={[
                                {
                                  required: isCore,
                                  message: '请输入精选权益值',
                                },
                              ]}
                              className="mb-0"
                            >
                              <Input placeholder="在精选权益中显示的值" />
                            </Form.Item>
                          </Col>
                          <Col span={9}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'normalValue']}
                              className="mb-0"
                              rules={[
                                {
                                  required: true,
                                  message: '请输入普通权益值',
                                },
                              ]}
                            >
                              <Input placeholder="在权益对比中显示的值" />
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'isDefault']}
                              valuePropName="checked"
                              className="mb-0"
                            >
                              <Radio
                                onChange={(e) =>
                                  handleDefaultValueChange(
                                    field.name,
                                    e.target.checked,
                                  )
                                }
                              >
                                设为默认值
                              </Radio>
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            {fields.length > 1 && (
                              <Button
                                type="text"
                                danger
                                icon={<MinusCircleOutlined />}
                                onClick={() => remove(field.name)}
                                className="mb-0"
                                title="删除预设值"
                              />
                            )}
                          </Col>
                        </>
                      )}
                    </Row>
                  ))}
                </div>

                {valueType === BenefitValueTypeEnum.ENUM && (
                  <Button
                    type="link"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                    className="mt-2"
                  >
                    新增预设值
                  </Button>
                )}
              </>
            )}
          </Form.List>
        </Form.Item>
      </div>
    </>
  );
};

export default BenefitForm;
