# 新旧接口映射与类型差异清单

## 接口映射关系

基于代码分析，建立 Product 版本与 Bundle 版本的接口映射关系：

### 1. 列表查询接口
**旧接口 (Product):** `fetchProductSkuList`
**新接口 (Bundle):** `fetchBundleSkuList`

**功能:** 查询商品SKU列表 → 查询组合SKU列表

### 2. 详情查询接口
**旧接口 (Product):** `fetchProductSkuDetail`
**新接口 (Bundle):** `fetchBundleSkuDetail`

**功能:** 获取商品SKU详情 → 获取组合SKU详情

### 3. 创建接口
**旧接口 (Product):** `fetchProductSkuCreation`
**新接口 (Bundle):** `fetchBundleSkuCreate`

**功能:** 创建商品SKU → 创建组合SKU

### 4. 更新接口
**旧接口 (Product):** `fetchProductSkuUpdating`
**新接口 (Bundle):** `fetchBundleSkuUpdate`

**功能:** 修改商品SKU → 更新组合SKU

### 5. 批量上下架接口
**旧接口 (Product):** `fetchProductSkuSaleStatusUpdatingByBatch`
**新接口 (Bundle):** `fetchBundleSkuUpdateSaleStatus`

**功能:** 批量商品SKU上/下架 → 批量上下架组合SKU

### 6. 变更记录接口
**旧接口 (Product):** `fetchProductSkuUpdatingLogs`
**新接口 (Bundle):** `fetchBundleSkuChangeLogs`

**功能:** 获取商品SKU的修改记录 → 查询组合SKU变更记录

## 请求参数类型差异

### 1. 列表查询参数差异

#### Product版本 (ProductSkuListParam)
```typescript
interface ProductSkuListParam extends PaginatorParam {
  skuId?: number;
  skuName?: string;
  skuShortName?: string;
  skuAlias?: string;
  capacity?: string;
  isReNew?: ReNewEnum;
  saleStatus?: SaleStatusEnum;
  deviceType?: string;
  sort?: number;
  level?: number;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
}
```

#### Bundle版本 (BundleSkuListParam)
```typescript
interface BundleSkuListParam extends PaginatorParam {
  bundleSkuId?: number;        // 字段名变化: skuId → bundleSkuId
  name?: string;               // 字段名变化: skuName → name
  shortName?: string;          // 字段名变化: skuShortName → shortName
  aliasName?: string;          // 字段名变化: skuAlias → aliasName
  serviceTime?: number;        // 保持一致
  isRenew?: StatusEnum;        // 字段名和类型变化: isReNew → isRenew, ReNewEnum → StatusEnum
  level?: number;              // 保持一致
  saleStatus?: SaleStatusEnum; // 保持一致
  orderBySql?: string;         // 新增字段
}
```

**差异总结:**
- 移除字段: `capacity`, `deviceType`, `sort`, `serviceTimeUnit`
- 新增字段: `orderBySql`
- 字段名变化: `skuId` → `bundleSkuId`, `skuName` → `name`, `skuShortName` → `shortName`, `skuAlias` → `aliasName`, `isReNew` → `isRenew`
- 类型变化: `isReNew` 从 `ReNewEnum` 变为 `StatusEnum`

### 2. 枚举值差异

#### SaleStatusEnum 枚举差异
**Product版本:**
```typescript
enum SaleStatusEnum {
  ON_SALE = 1,      // 上架
  OFF_SALVE = 0,    // 下架 (注意拼写错误)
}
```

**Bundle版本:**
```typescript
enum SaleStatusEnum {
  OFFLINE = 0,  // 下架
  ONLINE = 1,   // 上架
}
```

**差异:** 枚举成员名称完全不同，但值保持一致

#### ReNewEnum vs StatusEnum
**Product版本:**
```typescript
enum ReNewEnum {
  RENEW = 1,
  NOT_RENEW = 0,
}
```

**Bundle版本使用通用的 StatusEnum:**
```typescript
enum StatusEnum {
  DISABLE = 0,
  ENABLE = 1,
}
```

## 响应数据类型差异

### 1. 主要数据结构差异

#### Product版本 (ProductSku)
```typescript
interface ProductSku {
  id: number;
  name: string;
  shortName: string;
  aliasName: string;
  saleStatus: boolean;        // 注意是 boolean 类型
  capacities: Array<{
    type: string;
    cycleTime: number;
    name: string;
  }>;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceTypes: SuitableDeviceTypeEnum[];
  price: {
    price: number;
    linePrice: number;
    firstPhasePrice: number;
    isReNew: boolean;
    priceList: OtherCurrencySkuPrice[];
    firstPhasePriceList: OtherCurrencySkuPrice[];
  };
  cornerMarkIcon: string;
  description: string;
  createTime: number;
  relationSkuId: number;
  skuType: SkuTypeEnum;
  level: number;
  sort: number;
  benefits: ProductSkuBenefit[];
}
```

#### Bundle版本 (ProductSKUBundle)
```typescript
interface ProductSKUBundle {
  id: number;
  name: string;
  shortName: string;
  aliasName?: string;         // 可选字段
  discountName?: string;      // 新增字段
  supportedDeviceNum: number; // 新增字段，替代 deviceTypes
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  price: ProductSkuBundlePrice; // 价格结构发生变化
  cornerMarkIcon?: string;    // 变为可选
  skuRelations: SkuRelation[]; // 新增字段，替代 relationSkuId
  cycleTime?: number;         // 新增字段，替代 capacities.cycleTime
  level: number;
  sort: number;
  saleStatus: SaleStatusEnum; // 类型从 boolean 变为枚举
}
```

### 2. 价格结构差异

#### Product版本价格结构
```typescript
price: {
  price: number;
  linePrice: number;
  firstPhasePrice: number;
  isReNew: boolean;
  priceList: OtherCurrencySkuPrice[];
  firstPhasePriceList: OtherCurrencySkuPrice[];
}
```

#### Bundle版本价格结构
```typescript
interface ProductSkuBundlePrice {
  price: number;
  linePrice?: number;         // 变为可选
  firstPhasePrice?: number;   // 变为可选
  unitPrice?: number;         // 新增字段
  priceList?: SkuBundleCurrencyPrice[]; // 类型变化，变为可选
  firstPhasePriceList?: CurrencyPrice[];  // 类型变化，变为可选
  isReNew?: StatusEnum;       // 类型从 boolean 变为 StatusEnum，变为可选
  currency?: CurrencyInfo;    // 新增字段
}
```

## 创建/更新参数差异

### Product版本 (ProductSkuParam)
```typescript
interface ProductSkuParam {
  id?: number;
  name: string;
  shortName: string;
  aliasName?: string;
  level: number;
  sort: number;
  benefits: ProductSkuBenefitParam[];
  capacities: ProductSkuCapacityParam[];
  serviceTime: number;
  serviceTimeUnit: string;
  deviceTypes: SuitableDeviceTypeEnum[];
  price: ProductSkuPriceParam;
  cornerMarkIcon: string;
  relationSkuId?: number;
}
```

### Bundle版本 (ProductSkuBundleCreationParams)
```typescript
interface ProductSkuBundleCreationParams {
  name: string;
  shortName: string;
  aliasName?: string;
  discountName?: string;      // 新增字段
  supportedDeviceNum: number; // 替代 deviceTypes
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum; // 类型从 string 变为枚举
  price: ProductSkuBundlePrice;
  cornerMarkIcon?: string;    // 变为可选
  skuRelations: SkuRelation[]; // 替代 relationSkuId
  cycleTime?: number;         // 替代 capacities
  level: number;
  sort: number;
}
```

## 变更记录类型差异

### Product版本 (ProductSkuUpdatingLog)
```typescript
interface ProductSkuUpdatingLog {
  id: number;
  skuId: number;
  operateType: string;
  modifiedTime: number;       // 时间戳
  field: string;
  before: string;
  after: string;
}
```

### Bundle版本 (CloudStorageProductSKUBundleChange)
```typescript
interface CloudStorageProductSKUBundleChange {
  id: number;
  bundleSkuId: number;        // 字段名变化
  operatorId: number;         // 新增字段
  operatorName: string;       // 新增字段
  operateType: OperateTypeEnum; // 类型从 string 变为枚举
  field: string;
  beforeContent: string;      // 字段名变化: before → beforeContent
  afterContent: string;       // 字段名变化: after → afterContent
  createTime: string;         // 字段名和类型变化: modifiedTime(number) → createTime(string)
}
```

## 调整建议

基于以上差异分析，在从 Product 版本迁移到 Bundle 版本时需要进行以下调整：

### 1. 字段名映射
- `skuId` → `bundleSkuId`
- `skuName` → `name`
- `skuShortName` → `shortName`
- `skuAlias` → `aliasName`
- `isReNew` → `isRenew`
- `before/after` → `beforeContent/afterContent`
- `modifiedTime` → `createTime`

### 2. 类型转换
- `ReNewEnum` → `StatusEnum`
- `saleStatus` 从 `boolean` 转为 `SaleStatusEnum`
- `operateType` 从 `string` 转为 `OperateTypeEnum`
- `serviceTimeUnit` 从 `string` 转为 `ServiceTimeUnitEnum`
- 时间字段从 `number` 转为 `string`

### 3. 结构调整
- `deviceTypes` 数组 → `supportedDeviceNum` 数字
- `capacities` 数组 → `cycleTime` 数字
- `relationSkuId` 数字 → `skuRelations` 数组
- `benefits` 数组移除，由新的权益管理方式替代

### 4. 新增字段处理
- `discountName`: 优惠名称
- `supportedDeviceNum`: 支持设备数量
- `unitPrice`: 单价
- `currency`: 货币信息
- `operatorId/operatorName`: 操作者信息
