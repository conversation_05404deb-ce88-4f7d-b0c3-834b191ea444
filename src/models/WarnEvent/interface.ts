import { PaginatorParam } from '@/utils/request';

export enum TypeEnum {}

export enum LevelEnum {}

export enum StatusEnum {}

// 列表参数数据
export interface EventUpdateParam {
  id: number;
  status: string;
  remark: string;
}

// 列表参数数据
export interface EventListParam extends PaginatorParam {}

export interface Event {
  id: number;
  type: TypeEnum;
  level: LevelEnum;
  lastTime: number;
  payload: string;
  status: StatusEnum;
  remark: string;
  createTime: string;
}
