/**
 * BundleSku 适配器文件
 * 统一工具和类型文件引用，将旧的映射更新为新 enums
 */

import {
  OperateTypeEnum,
  SaleStatusEnum,
  ServiceTimeUnitEnum,
} from '@/api/sku/bundle/types';
import {
  formatServiceTimeText,
  getOperateTypeText,
  getSaleStatusText,
  getServiceTimeUnitText,
  isRenewOptions,
  isRenewText,
  operateTypeNameMap,
  saleStatusNameMap,
  saleStatusOptions,
  serviceTimeUnitNameMap,
  serviceTimeUnitOptions,
} from '@/api/sku/bundle/utils';
import { SelectOption } from '@/models/common.interface';

// 重新导出新的枚举类型，方便统一引用
export { OperateTypeEnum, SaleStatusEnum, ServiceTimeUnitEnum };

// 为了保持向后兼容，保留旧的映射对象名称但使用新的数据源
export const serviceTimeUnitObj: { [key in ServiceTimeUnitEnum]?: string } = {
  [ServiceTimeUnitEnum.YEAR]: serviceTimeUnitNameMap[ServiceTimeUnitEnum.YEAR],
  [ServiceTimeUnitEnum.MONTH]:
    serviceTimeUnitNameMap[ServiceTimeUnitEnum.MONTH],
  [ServiceTimeUnitEnum.DAY]: serviceTimeUnitNameMap[ServiceTimeUnitEnum.DAY],
};

// 销售状态映射对象
export const saleStatusObj: { [key in SaleStatusEnum]: string } = {
  [SaleStatusEnum.OFFLINE]: saleStatusNameMap[SaleStatusEnum.OFFLINE],
  [SaleStatusEnum.ONLINE]: saleStatusNameMap[SaleStatusEnum.ONLINE],
};

// 重新导出工具函数
export {
  formatServiceTimeText,
  getOperateTypeText,
  getSaleStatusText,
  getServiceTimeUnitText,
  isRenewOptions,
  isRenewText,
  operateTypeNameMap,
  saleStatusNameMap,
  saleStatusOptions,
  serviceTimeUnitNameMap,
  serviceTimeUnitOptions,
};

// 为了向后兼容，创建一些别名函数
export const getSaleStatusName = (status: SaleStatusEnum): string => {
  return getSaleStatusText(status);
};

export const getServiceTimeUnitName = (unit: ServiceTimeUnitEnum): string => {
  return getServiceTimeUnitText(unit);
};

// 销售状态选项（保持旧的命名约定）
export const saleStatusOptions2: SelectOption[] = saleStatusOptions;

// 服务时长单位选项（保持旧的命名约定）
export const serviceTimeUnitOptions2: SelectOption[] = serviceTimeUnitOptions;
