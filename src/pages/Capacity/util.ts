import {
  DeviceCapacity,
  DeviceCapacityParam,
  SuitableDeviceTypeEnum,
} from '@/models/device/interface';
import { Capacity } from '@/models/util/interface';
import { DeviceCapacityForm } from './interface';

export const initialCapacityForm: DeviceCapacityForm = {
  deviceType: null,
  allCapacities: [],
  initialCapacities: [],
};

export const transferFormDataToParam = (
  formData: DeviceCapacityForm,
  capacityList: Capacity[],
  id?: number,
): DeviceCapacityParam => {
  const param: DeviceCapacityParam = {
    deviceType: formData.deviceType || SuitableDeviceTypeEnum.D4sh,
    allCapacities: formData.allCapacities.map((ac) => {
      const capacity = capacityList.find((cap) => cap.type === ac);
      return {
        type: ac,
        name: capacity?.name || '',
      };
    }),
    initialCapacities: formData.initialCapacities
      .filter((ic) => ic.enable)
      .map((ic) => ({
        type: ic.type,
        cycleTime: ic.cycleTime || 0,
        name: ic.name,
      })),
  };
  if (id) {
    param.id = id;
  }
  return param;
};

export const transferDetailToFormData = (
  deviceCapacity: DeviceCapacity,
): DeviceCapacityForm => {
  const formData: DeviceCapacityForm = {
    deviceType: deviceCapacity.deviceType,
    allCapacities: deviceCapacity.allCapacities.map(
      (capacity) => capacity.type,
    ),
    initialCapacities: deviceCapacity.allCapacities.map((capacity) => {
      const initialCapacity = deviceCapacity.initialCapacities.find(
        (ic) => ic.type === capacity.type,
      );
      const initCapacity = {
        type: capacity.type,
        name: capacity.name,
        cycleTime: initialCapacity?.cycleTime || null,
        enable: !!initialCapacity || false,
      };
      return initCapacity;
    }),
  };
  return formData;
};
