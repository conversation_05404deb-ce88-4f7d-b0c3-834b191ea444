import { LoginParam } from '@/models/login/interface';
import { setSourceDomainToken } from '@/models/login/util';
import { fetchForwardLogin } from '@/models/product/fetch';
import { DomainTypeEnum } from '@/utils/targetDomain';
import {
  EyeInvisibleOutlined,
  EyeTwoTone,
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Form, Input, Space } from 'antd';
import React, { useState } from 'react';
import { LoginForm } from './interface';

interface LoginProps {
  domainType: DomainTypeEnum;
  onClose: (success?: boolean) => void;
}

const Login: React.FC<LoginProps> = ({ domainType, onClose }) => {
  const [form] = Form.useForm();
  const [logining, setLogining] = useState(false);

  // 表单提交处理
  const handleSubmit = async (formData: LoginForm) => {
    const param: LoginParam = {
      ...formData,
    };
    try {
      setLogining(true);
      // 这里添加登录逻辑
      const result = await fetchForwardLogin(param, domainType);
      console.log(result);
      setSourceDomainToken(domainType, result.id);
      onClose(true);
    } catch (error) {
      setLogining(false);
      console.log(error);
    }
  };

  return (
    <div style={{ marginTop: 20 }}>
      <Form form={form} name="login" onFinish={handleSubmit} autoComplete="off">
        <Form.Item
          name="username"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入用户名"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            size="large"
            iconRender={(visible) =>
              visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
            }
          />
        </Form.Item>

        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              block
              size="large"
              loading={logining}
              disabled={logining}
            >
              登录
            </Button>
            <Button
              htmlType="button"
              block
              size="large"
              onClick={() => onClose()}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Login;
