import { fetchBundleSkuChangeLogs } from '@/api/sku/bundle/fetch';
import {
  BundleSkuChangeLogsParam,
  CloudStorageProductSKUBundleChange,
} from '@/api/sku/bundle/types';
import { Paginator, initPaginator } from '@/utils/request';
import { Modal, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';
import { initBundleSkuChangeLogsParam } from '../util';

interface Props {
  bundleSkuId: number;
  showModal: boolean;
  onClose?: () => void;
}

const OperationHistory: React.FC<Props> = ({
  showModal,
  bundleSkuId,
  onClose,
}: Props) => {
  const [listParam, setListParam] = useState<BundleSkuChangeLogsParam>({
    ...initBundleSkuChangeLogsParam,
    bundleSkuId,
  });
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [list, setList] = useState<CloudStorageProductSKUBundleChange[]>([]);

  const requestHistoryList = async (_listParam: BundleSkuChangeLogsParam) => {
    const { items, ...rest } = await fetchBundleSkuChangeLogs(_listParam);
    setPaginator(rest);
    setList(items);
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let current = page;
    if (pageSize !== listParam.limit) {
      current = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (current - 1) * pageSize,
    });
  };

  const columns: ColumnsType<CloudStorageProductSKUBundleChange> = [
    {
      title: '操作',
      dataIndex: 'operateType',
      width: 80,
      render: (_, row) => row.operateType || '--',
    },
    {
      title: '操作字段',
      dataIndex: 'field',
      width: 100,
      render: (_, row) => row.field || '--',
    },
    {
      title: '变更前',
      dataIndex: 'beforeContent',
      width: 100,
      render: (_, row) => row.beforeContent || '--',
    },
    {
      title: '变更后',
      dataIndex: 'afterContent',
      width: 500,
      render: (_, row) =>
        row.afterContent ? (
          <div>
            {row.afterContent.split('\n').map((item) => (
              <React.Fragment key={item}>{item}</React.Fragment>
            ))}
          </div>
        ) : (
          '--'
        ),
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      width: 180,
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  useEffect(() => {
    if (listParam && listParam.bundleSkuId) {
      requestHistoryList(listParam);
    }
  }, [listParam]);

  return (
    <Modal
      width={950}
      destroyOnClose
      open={showModal}
      footer={null}
      onCancel={() => isFunction(onClose) && onClose()}
    >
      <Table<CloudStorageProductSKUBundleChange>
        dataSource={list}
        columns={columns}
        size="small"
        rowKey="id"
        scroll={{
          x: columns.reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          total: paginator.total,
          pageSize: paginator.limit,
          showSizeChanger: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default OperationHistory;
