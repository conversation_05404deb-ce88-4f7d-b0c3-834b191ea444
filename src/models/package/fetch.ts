import { packageApiOption } from '@/services/api/package';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum, StatusEnum } from '../common.interface';
import {
  ActivityPriorityValidatorParam,
  OperationPackage,
  OperationPackageDetail,
  OperationPackageListParam,
  OperationPackageParam,
} from './interface';

// 获取Package列表
export const fetchOperationPackageList = (
  param: OperationPackageListParam,
): Promise<Data<OperationPackage>> => {
  const config = packageApiOption.opertionPackageList;
  config.option.params = param;
  return request(config.url, config.option);
};

export const fetchOperationStatusSwtich = (id: number, status: StatusEnum) => {
  const config = packageApiOption.operationPackageStatusSwtich;
  config.option.params = {
    actPackageId: id,
    saleStatus: status,
  };
  return request(config.url, config.option);
};

export const fetchOperationPackageDeletion = (id: number) => {
  const config = packageApiOption.operationPackageDeletion;
  config.option.params = { actPackageId: id };
  return request(config.url, config.option);
};

export const fetchActivityContentInfo = async (): Promise<{
  [key: string]: string;
}> => {
  const config = packageApiOption.activityContentInfo;
  return request(config.url, config.option);
};

export const fetchActivityPackageSave = (
  param: OperationPackageParam,
): Promise<ApiSuccessEnum> => {
  const config = packageApiOption.activityPackageSave;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { ...param, payload: JSON.stringify(param.payload) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取运营套餐详情数据
export const fetchActivityPackageDetail = (
  id: number,
): Promise<OperationPackageDetail> => {
  const config = packageApiOption.activityPackageDetail;
  config.option.restApi = { id: String(id) };
  return request(config.url, config.option);
};

// 校验优先级
export const fetchActivityPackagePriorityValidator = (
  param: ActivityPriorityValidatorParam,
): Promise<ApiSuccessEnum> => {
  const config = packageApiOption.activityPackagePriorityValidator;
  config.option.params = param;
  return request(config.url, config.option);
};
