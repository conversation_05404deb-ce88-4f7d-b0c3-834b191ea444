import { ServicePlanStatusEnum } from '../service/interface';

export type DevicepackageDevicesParam = {
  userId: number;
  deviceType: string;
};

export type DevicepackageDevice = {
  device: {
    id: number;
    deviceId: number;
    deviceType: string;
    deviceName: string;
    sn: string;
  };
  // serviceInfo: {
  //   id: number;
  //   userId: number;
  //   subscribe: ReNewEnum;
  //   status: ServiceStatusEnum;
  // };
  serviceStatus: ServicePlanStatusEnum;
};

export type DevicepackageDeviceLinkParam = {
  serviceId: number;
  userId: number;
  deviceId: number;
  deviceType: string;
};

// 解除关联参数
export type DevicepackageUnlinkParam = {
  userId: number;
  deviceId: number;
  deviceType: string;
};
