import { deviceApiOption } from '@/services/api/device';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  AiVideoFeedback,
  AiVideoFeedbackListParam,
  Device,
  DeviceCapacity,
  DeviceCapacityListParam,
  DeviceCapacityParam,
  DeviceInfo,
  DeviceInfoParam,
  DeviceListParam,
  M3u8InfoParam,
  WifiDeviceListParam,
} from './interface';

export const fetchDeviceInfo = (
  param: DeviceInfoParam,
): Promise<DeviceInfo> => {
  const config = deviceApiOption.deviceInfo;
  config.option.params = param;
  return request(config.url, config.option);
};

// 获取Device列表
export const fetchDeviceList = (
  param: DeviceListParam | WifiDeviceListParam,
  type: string,
): Promise<Data<Device>> => {
  const config = deviceApiOption.deviceList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取设备能力列表
export const fetchDeviceCapacityList = (
  param: DeviceCapacityListParam,
): Promise<Data<DeviceCapacity>> => {
  const config = deviceApiOption.deviceCapacityList;
  config.option.params = param;
  return request(config.url, config.option);
};
// 获取设备能力详情
export const fetchDeviceCapacityDetail = (
  id: number,
): Promise<DeviceCapacity> => {
  const config = { ...deviceApiOption.deviceCapacityDetail };
  config.option.restApi = { id: `${id}` };
  return request(config.url, config.option);
};
// 创建设备能力
export const fetchDeviceCapacityCreation = (
  param: DeviceCapacityParam,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.deviceCapacityCreation;
  config.option.data = param;
  return request(config.url, config.option);
};
// 更新设备能力
export const fetchDeviceCapacityUpdate = (
  param: DeviceCapacityParam,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.deviceCapacityUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 获取ai视频反馈数据
export const fetchAiVideoFeedback = (
  param: AiVideoFeedbackListParam,
  device: string,
): Promise<Data<AiVideoFeedback>> => {
  const config = deviceApiOption.aiVideoFeedback;
  config.option.restApi = { device };
  config.option.params = param;
  return request(config.url, config.option);
};

// 获取m3u8播放地址
export const getM3u8FileUrl = (param: M3u8InfoParam, device: string) => {
  const config = deviceApiOption.m3u8VideoInfo;
  const url = `${getCurrentPrefix()}${config.url.replace(
    ':device',
    device.toLowerCase(),
  )}?X-Admin-Session=${localStorage.getItem('sessionToken')}&eventId=${
    param.eventId
  }&combineKey=${param.combineKey}`;
  return url;
};

export const fetchM3u8VideoInfo = (param: M3u8InfoParam, device: string) => {
  const config = deviceApiOption.m3u8VideoInfo;
  config.option.restApi = { device };
  config.option.params = param;
  return request(config.url, config.option);
};
