import { initPaginatorParam } from '@/utils/request';
import { SelectOption } from '../common.interface';
import { transferObjectToSelelctOptionList } from '../common.util';
import {
  AiVideoFeedbackListParam,
  DeviceCapacityListParam,
  DeviceListParam,
  SearchTypeEnum,
  SuitableDeviceTypeEnum,
} from './interface';

export const initDeviceListParam: DeviceListParam = {
  limit: 10,
  offset: 0,
  type: SearchTypeEnum.ID,
  s: '',
};

export const initDeviceCapacityListParam: DeviceCapacityListParam = {
  ...initPaginatorParam,
};

// export const initDeviceSnListParam: DeviceSnListParam = {
//   ...initPaginatorParam,
//   type: SearchTypeEnum.Sn,
//   s: '',
// };

// export const initWifiDeviceSnListParam: DeviceSnListParam = {
//   ...initPaginatorParam,
//   type: WifiSearchTypeEnum.SN,
//   s: '',
// };

// 初始化ai视频反馈列表接口参数
export const initAiVideoFeedbackListParam: AiVideoFeedbackListParam = {
  ...initPaginatorParam,
};
// 通用设备类型筛选项
export const deviceTypeEnum: {
  [key in SuitableDeviceTypeEnum]: SuitableDeviceTypeEnum;
} = {
  // [SuitableDeviceTypeEnum.D4s]: SuitableDeviceTypeEnum.D4s,
  [SuitableDeviceTypeEnum.D4sh]: SuitableDeviceTypeEnum.D4sh,
  [SuitableDeviceTypeEnum.D4h]: SuitableDeviceTypeEnum.D4h,
  [SuitableDeviceTypeEnum.T5]: SuitableDeviceTypeEnum.T5,
  [SuitableDeviceTypeEnum.T6]: SuitableDeviceTypeEnum.T6,
  [SuitableDeviceTypeEnum.T7]: SuitableDeviceTypeEnum.T7,
};

export const deviceTypeOptions: Array<SelectOption<SuitableDeviceTypeEnum>> =
  transferObjectToSelelctOptionList<SuitableDeviceTypeEnum>(deviceTypeEnum);
