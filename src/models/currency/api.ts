import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const currencyApiOption: Record<
  'currencyList' | 'currencyCodeList',
  RequestOption
> = {
  // 获取Currency列表
  currencyList: {
    url: '/adm/bs/currency/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  currencyCodeList: {
    url: '/adm/bs/currency/getCurrencyCodes',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
