import { fetchQiniuToken } from '@/models/app/fetch';
import { QiniuUploadResult, QiniuUploadToken } from '@/models/app/interface';
import { message } from 'antd';
import { RcFile } from 'antd/lib/upload';
import isFunction from 'lodash/isFunction';
import * as qiniu from 'qiniu-js';
import { ProgressCompose } from 'qiniu-js/esm/upload';
import { CompressOptions } from 'qiniu-js/esm/utils/compress';

export interface QiniuOssOption {
  maxWidth?: number;
  maxHeight?: number;
  file: string | Blob | RcFile;
  onProgress?: (total: ProgressCompose) => void;
  onError?: (
    err: qiniu.QiniuError | qiniu.QiniuRequestError | qiniu.QiniuNetworkError,
  ) => void;
  onSuccess?: (
    body: QiniuUploadResult,
    xhr?: XMLHttpRequest | undefined,
  ) => void;
}

export interface QiniuOssService {
  qiniuTokenKey: QiniuUploadToken;
  getQiniuTokenKey: () => void;
  compressImage: (
    file: File,
    option: CompressOptions,
  ) => Promise<qiniu.CompressResult>;
  run: (options: QiniuOssOption) => void;
}

export const qiniuOssService = {
  qiniuTokenKey: {
    key: '',
    token: '',
  },

  getQiniuTokenKey: async () => {
    try {
      if (
        !qiniuOssService.qiniuTokenKey ||
        !qiniuOssService.qiniuTokenKey.token ||
        !!qiniuOssService.qiniuTokenKey.key
      ) {
        const result = await fetchQiniuToken();
        qiniuOssService.qiniuTokenKey = result;
      }
      return qiniuOssService.qiniuTokenKey;
    } catch (e) {
      console.log(e);
    }
  },

  compressImage: async (
    file: File,
    option: CompressOptions,
  ): Promise<qiniu.CompressResult> => {
    let result: qiniu.CompressResult = { dist: file, width: 0, height: 0 };
    if (!option.maxWidth && !option.maxHeight) return result;

    if (!option.maxHeight) delete option.maxHeight;

    if (!option.maxWidth) delete option.maxWidth;

    result = await qiniu.compressImage(file as File, {
      ...option,
      noCompressIfLarger: true,
    });
    return result;
  },

  run: async (options: QiniuOssOption) => {
    const {
      maxWidth = 0,
      maxHeight = 0,
      file,
      onProgress,
      onError,
      onSuccess,
    } = options;
    const qiniuTokenKey = await qiniuOssService.getQiniuTokenKey();
    console.log(qiniuTokenKey);
    const result = await qiniuOssService.compressImage(file as File, {
      maxWidth,
      maxHeight,
    });
    if (!qiniuTokenKey) {
      message.warning('没有权限，请重新上传');
      return;
    }
    const observable = qiniu.upload(
      result.dist as File,
      qiniuTokenKey.key,
      qiniuTokenKey.token || '',
    );
    // const subscription =
    observable.subscribe({
      next: (item) => {
        if (isFunction(onProgress)) onProgress(item.total);
      },
      error: (err) => {
        if (isFunction(onError)) onError(err);
      },
      complete: onSuccess,
    });
  },
};
