import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { LoginParam, LoginResult } from '@/models/login/interface';
import { productApiOption } from '@/services/api/product';
import request, { Data } from '@/utils/request';
import { DomainTypeEnum } from '@/utils/targetDomain';
import { getSourceDomainToken } from '../login/util';
import {
  Benefit,
  BenefitCompare,
  BenefitCompareListParam,
  BenefitCompareParam,
  BenefitCompareUpdateStatusParam,
  BenefitDetail,
  BenefitListParam,
  BenefitParam,
  Capacity,
  CapacityParam,
  OtherCurrencySkuPrice,
  OtherCurrencySkuPriceListParam,
  ProductSku,
  ProductSkuDetail,
  ProductSkuListParam,
  ProductSkuParam,
  ProductSkuUpdatingLog,
  ProductSkuUpdatingLogsParam,
  RelationProductSkuListParam,
  SyncSkuParam,
} from './interface';

// 获取Product列表
export const fetchProductSkuList = (
  param: ProductSkuListParam,
): Promise<Data<ProductSku>> => {
  const config = productApiOption.productSkuList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 获取ProductSku详情
export const fetchProductSkuDetail = (
  skuId: number,
): Promise<ProductSkuDetail> => {
  const config = productApiOption.productSkuDetail;
  config.option.params = { skuId };
  return request(config.url, config.option);
};

// 获取商品SKU的修改记录
export const fetchProductSkuUpdatingLogs = (
  param: ProductSkuUpdatingLogsParam,
): Promise<Data<ProductSkuUpdatingLog>> => {
  const config = productApiOption.productSkuUpdatingLogs;
  config.option.params = param;
  return request(config.url, config.option);
};

// 商品SKU上/下架
export const fetchProductSkuSaleStatusUpdating = (
  skuId: number,
  status: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.productSkuSaleStatusUpdating;
  config.option.data = { skuId, status };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 创建商品SKU
export const fetchProductSkuCreation = (
  param: ProductSkuParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.productSkuCreation;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { payload: JSON.stringify(param) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 修改商品SKU
export const fetchProductSkuUpdating = (
  param: ProductSkuParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.productSkuUpdating;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { payload: JSON.stringify(param) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 批量商品SKU上/下架
export const fetchProductSkuSaleStatusUpdatingByBatch = (
  skuIdList: number[],
  saleStatus: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.productSkuSaleStatusUpdatingByBatch;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { payload: JSON.stringify({ skuIdList, saleStatus }) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取可关联SKU列表
export const fetchAssociableProductSkuList = (
  param: RelationProductSkuListParam,
): Promise<Data<ProductSku>> => {
  const config = productApiOption.associableProductSkuList;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { ...param, payload: JSON.stringify(param.payload) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取权益列表
export const fetchBenefitList = (
  param: BenefitListParam,
): Promise<Data<Benefit>> => {
  const config = productApiOption.benefitList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 获取权益详情
export const fetchBenefitDetail = (id: number): Promise<BenefitDetail> => {
  const config = productApiOption.benefitDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 创建权益
export const fetchBenefitCreation = (
  param: BenefitParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitCreation;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.requestType = 'form';
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新权益
export const fetchBenefitUpdating = (
  param: BenefitParam,
): Promise<ApiSuccessEnum> => {
  const config = { ...productApiOption.benefitUpdating };
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.requestType = 'form';
  config.option.data = param;
  return request(config.url, config.option);
};

// 切换权益上下架
export const fetchBenefitStatusSwitching = (
  id: number,
  status: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitStatusSwitching;
  config.option.data = { id, status };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除权益
export const fetchBenefitDeletion = (id: number): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitDeletion;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 获取SKU能力列表
export const fetchCapacityList = (): Promise<Capacity[]> => {
  const config = productApiOption.capacityList;
  config.option.params = { limit: 100000, offset: 0 };
  return request(config.url, config.option);
};
// 更新SKU能力
export const fetchCapacityUpdating = (
  param: CapacityParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.capacityUpdating;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

/**
 * ************************************************************************
 * 特权对比相关接口
 */
export const fetchBenefitCompareList = (
  param: BenefitCompareListParam,
): Promise<Data<BenefitCompare>> => {
  const config = productApiOption.benefitCompareList;
  config.option.params = param;
  return request(config.url, config.option);
};

export const fetchBenefitCompareDetail = (
  id: number,
): Promise<BenefitCompare> => {
  const config = productApiOption.benefitCompareDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

export const fetchBenefitCompareAdd = (
  param: BenefitCompareParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitCompareAdd;
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchBenefitCompareUpdate = (
  param: BenefitCompareParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitCompareUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchBenefitCompareStatusUpdate = (
  param: BenefitCompareUpdateStatusParam,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.benefitCompareStatusUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */

/**
 * ************************************************************************
 * SKU价格相关接口
 */
export const fetchOtherCurrencySkuPriceList = (
  param: OtherCurrencySkuPriceListParam,
): Promise<OtherCurrencySkuPrice[]> => {
  const config = productApiOption.otherCurrencySkuPriceList;
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchBasicSkuPriceList = (): Promise<number[]> => {
  const config = productApiOption.basicSkuPriceList;
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */

export const fetchForwardLogin = (
  param: LoginParam,
  area: DomainTypeEnum,
): Promise<LoginResult> => {
  console.log('param', param, area);
  const config = productApiOption.forwardLogin;
  config.option.restApi = { area };
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchSyncSku = (param: SyncSkuParam): Promise<ApiSuccessEnum> => {
  const config = productApiOption.syncSku;
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchForwardSyncSku = (
  param: SyncSkuParam,
  area: DomainTypeEnum,
): Promise<ApiSuccessEnum> => {
  const config = productApiOption.forwardSyncSku;
  config.option.data = param;
  config.option.restApi = { area };
  config.option.headers = {
    ...config.option.headers,
    'X-Admin-Forward-Session': getSourceDomainToken(area) || '',
  };
  return request(config.url, config.option);
};
