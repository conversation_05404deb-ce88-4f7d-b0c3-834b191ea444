import { ApiSuccessEnum } from '@/models/common.interface';
import { fetchEventUpdating } from '@/models/WarnEvent/fetch';
import { Event, EventUpdateParam } from '@/models/WarnEvent/interface';
import {
  DrawerForm,
  ProForm,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';

const Updater: React.FC<{
  data?: Event;
  visit: boolean;
  onVisitChanged: any;
  onDataChanged: any;
}> = (props) => {
  const [form] = Form.useForm();
  const [data, setData] = useState<Event>();

  useEffect(() => {
    setData(props.data);
    form.setFieldsValue({
      payload: props.data?.payload || '',
      remark: props.data?.remark || '',
    });
  }, [props.data]);

  /**
   * 由父组件控制Drawer的显示
   * @param visit
   */
  const changedVisit = (visit: boolean) => {
    props.onVisitChanged(visit);
  };

  const changedData = () => {
    props.onDataChanged();
  };

  const update = (status: string) => {
    let id = props.data?.id;
    if (!id) {
      message.warning('数据错误, 请检查!');
      return;
    }
    let remark = form.getFieldValue('remark');
    if (!remark) {
      message.warning('请填写 处理说明 ');
      return;
    }
    const param: EventUpdateParam = {
      id: id,
      status: status,
      remark: remark,
    };
    fetchEventUpdating(param).then((result) => {
      if (result === ApiSuccessEnum.success) {
        message.success('成功！');
        changedVisit(false);
        changedData();
      }
    });
  };

  return (
    <>
      <DrawerForm
        form={form}
        title={'事件: ' + data?.id || ''}
        open={props.visit}
        onOpenChange={(visit: boolean) => changedVisit(visit)}
        // drawerProps={{maskClosable: false, keyboard: false, destroyOnClose: true}}
        submitter={{
          // 配置按钮的属性
          resetButtonProps: {},
          submitButtonProps: {},

          // 完全自定义整个区域
          render: (props, doms) => {
            return [
              <Button
                type="default"
                key="cancel"
                onClick={() => changedVisit(false)}
              >
                仍未处理
              </Button>,
              <Button
                type="default"
                key="cancel"
                onClick={(e) => {
                  e.preventDefault();
                  update('cancel');
                  changedVisit(false);
                }}
              >
                无需处理
              </Button>,
              <Button
                type="default"
                key="submit"
                onClick={(e) => {
                  e.preventDefault();
                  update('done');
                  changedVisit(false);
                }} // 更新状态????
              >
                已经解决
              </Button>,
            ];
          },
        }}
      >
        <ProForm.Group>
          <ProFormTextArea
            name="payload"
            label="错误信息"
            width={600}
            fieldProps={{ autoSize: { minRows: 2, maxRows: 30 } }}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormTextArea
            name="remark"
            label="处理说明"
            width={600}
            fieldProps={{ autoSize: { minRows: 2, maxRows: 5 } }}
          />
        </ProForm.Group>
      </DrawerForm>
    </>
  );
};

export default Updater;
