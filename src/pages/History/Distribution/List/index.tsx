import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  fetchVirtualCardDistributionExportUrl,
  fetchVirtualCardDistributionList,
} from '@/models/history/fetch';
import {
  VirtualCardDistribution,
  VirtualCardDistributionListParam,
} from '@/models/history/interface';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Select } from 'antd';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { HistoryRecordTypeEnum } from '../../interface';
import { historyRecordTypeMap } from '../../util';
import { DistributionTableForm, UrlParam } from '../interface';
import {
  transferFormDataToUrlParam,
  transferUrlParamToFormData,
  transferUrlParamToListParam,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<VirtualCardDistribution[]>([]);
  const [listParam, setListParam] =
    useState<VirtualCardDistributionListParam>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const formRef = useRef<ProFormInstance<DistributionTableForm>>();

  // 获取列表数据
  const requestDistributionList = async (
    param: VirtualCardDistributionListParam,
  ) => {
    const { items, ...rest } = await fetchVirtualCardDistributionList(
      param as VirtualCardDistributionListParam,
    );
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  // const editDistributionChangeRefund = (id: number) => {
  //   history.push(`/edit/${id}`);
  // };

  const exportData = async () => {
    const url = await fetchVirtualCardDistributionExportUrl(
      omit(listParam, ['offset', 'limit']),
    );
    window.open(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue();
          const param: UrlParam = transferFormDataToUrlParam(formData);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/business/historyRecord/distribution',
              param,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<VirtualCardDistribution>> = [
    {
      title: '操作类型',
      dataIndex: 'operationType',
      search: false,
      render: () => historyRecordTypeMap[HistoryRecordTypeEnum.DISTRIBUTION],
    },
    {
      title: '生成批次',
      dataIndex: 'batchId',
      render: (_, record) => (
        <RedirectLink
          text={record.batchId}
          linkUrl="/business/virtualCard/codeList"
          params={{ batchId: record.batchId }}
        />
      ),
    },
    {
      title: `适用设备`,
      dataIndex: 'deviceType',
      valueType: 'select',
      valueEnum: {
        d4sh: 'D4sh',
        d4h: 'D4h',
        t5: 'T5',
        t6: 'T6',
      },
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} mode="multiple" placeholder="请选择" allowClear />
      ),
      render: (_, record) =>
        record.deviceSkus.map((sku) => sku.deviceType).join(','),
    },
    {
      title: '关联Sku',
      dataIndex: 'deviceSkus',
      search: false,
      render: (_, record) =>
        (record as VirtualCardDistribution).deviceSkus
          .map((sku) => sku.skuName)
          .join(','),
    },
    {
      title: '卡券数量',
      dataIndex: 'sendNum',
      search: false,
    },
    {
      title: '需求方',
      dataIndex: 'demandSide',
      search: false,
    },
    {
      title: '操作时间',
      dataIndex: 'operatorTime',
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.operationTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      search: false,
    },
  ];

  useEffect(() => {
    if (!formRef || !formRef.current) return;
    const formData = transferUrlParamToFormData(urlParam);
    formRef.current.setFieldsValue(formData);
    const _listParam = transferUrlParamToListParam(urlParam);
    console.log(_listParam);
    setListParam(_listParam);
  }, [urlParam, formRef]);

  useEffect(() => {
    if (listParam) {
      requestDistributionList(listParam);
    }
  }, [listParam, urlParam]);

  return (
    <ProTable<VirtualCardDistribution>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      formRef={formRef}
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => listParam && setListParam({ ...listParam }) }}
      toolbar={{
        actions: [
          <Button key="export" type="primary" onClick={() => exportData()}>
            导出报表
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
