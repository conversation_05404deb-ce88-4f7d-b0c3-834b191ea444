import RedirectLink from '@/components/TableRowLink/RedirectLink';
import VideoPlayer, { VideoType } from '@/components/VideoPlayer';
import { StatusEnum } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchAiVideoFeedback, getM3u8FileUrl } from '@/models/device/fetch';
import {
  AiVideoFeedback,
  AiVideoFeedbackListParam,
  PetActionEnum,
  VideoTypeEnum,
  WifiSearchTypeEnum,
} from '@/models/device/interface';
import { initAiVideoFeedbackListParam } from '@/models/device/util';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { PlayCircleFilled } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance, Image, Select, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { forIn, omit } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import {
  AiVideoFeedbackTableForm,
  AiVideoFeedbackUrlParam,
} from '../interface';

const List: React.FC = () => {
  const [urlParam] = useUrlState<AiVideoFeedbackUrlParam>();
  const [dataList, setDataList] = useState<AiVideoFeedback[]>([]);
  const [listParam, setListParam] = useState<AiVideoFeedbackListParam>();
  const [deviceType, setDeviceType] = useState('');
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const formRef = useRef<FormInstance<AiVideoFeedbackTableForm>>();
  const [showM3u8ById, setShowM3u8ById] = useState<{ [key: number]: boolean }>(
    {},
  );

  // 获取列表数据
  const requestAiVideoFeedbackList = async (
    param: AiVideoFeedbackListParam,
    device: string,
  ) => {
    const { items, ...rest } = await fetchAiVideoFeedback(param, device);
    setPagination(rest);
    setDataList(items);
  };

  const playVideo = async (record: AiVideoFeedback) => {
    if (record.upload === StatusEnum.DISABLE) {
      message.warning('当前视频未上传，无法查看');
      return;
    }
    setShowM3u8ById({ [record.id]: true });
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: AiVideoFeedbackTableForm = form?.getFieldsValue();
          const param: Partial<AiVideoFeedbackUrlParam> = {};

          forIn<AiVideoFeedbackTableForm>(formData, (value, key) => {
            if (value) {
              if (key === 'createdAt') {
                param.start = (value as Dayjs[])[0].valueOf();
                param.end = (value as Dayjs[])[1].valueOf();
                // param[key] = (value as Dayjs[]).length
                //   ? [
                //     ]
                //   : [];
              } else {
                param[key] = value as string | number;
              }
            }
          });
          console.log(param);

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/aiVideoFeedback`,
              param: param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          // form?.resetFields();
          // setUrlParam(form?.getFieldsValue());
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/aiVideoFeedback`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<AiVideoFeedback>> = [
    {
      title: '用户ID',
      dataIndex: 'userId',
      width: 80,
      render: (_, row) => (
        <RedirectLink
          text={row.userId}
          linkUrl={`/user/users`}
          params={{
            username: row.userId,
          }}
        />
      ),
    },
    {
      title: '设备ID',
      dataIndex: 'deviceId',
      width: 80,
      render: (_, row) => (
        <RedirectLink
          text={row.deviceId}
          linkUrl={`/${row.deviceType.toLowerCase()}/devices`}
          params={{
            type: WifiSearchTypeEnum.ID,
            s: row.deviceId,
          }}
        />
      ),
    },
    {
      title: '设备型号',
      dataIndex: 'deviceType',
      width: 100,
      valueType: 'select',
      valueEnum: { d4sh: 'D4SH', d4h: 'D4H', t5: 'T5', t6: 'T6', t7: 'T7' },
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} placeholder="请选择" allowClear />
      ),
    },
    {
      title: '视频',
      dataIndex: 'url',
      search: false,
      width: 100,
      render: (_, record) => (
        <>
          {record.url && record.type === VideoTypeEnum.QINIU ? (
            <Image
              style={{ cursor: 'pointer' }}
              alt="视频"
              src={`${record.url}?vframe/jpg/offset/0.01/w/100/h/100`}
              preview={{
                imageRender: () => (
                  <VideoPlayer width={1000} height={800} src={record.url} />
                ),
                toolbarRender: () => null,
              }}
            />
          ) : null}
          {record.type === VideoTypeEnum.M3U8 ? (
            <>
              <Button
                type="link"
                icon={<PlayCircleFilled />}
                onClick={() => playVideo(record)}
              >
                观看视频
              </Button>
              <Image
                width={200}
                style={{ display: 'none' }}
                preview={{
                  visible: showM3u8ById[record.id],
                  imageRender: () => {
                    const { eventId, combineKey } = record;
                    return (
                      <>
                        <VideoPlayer
                          videoType={VideoType.HLS}
                          width={1000}
                          height={800}
                          src={getM3u8FileUrl(
                            { eventId, combineKey },
                            record.deviceType,
                          )}
                        />
                      </>
                    );
                  },
                  // src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
                  onVisibleChange: (value) => {
                    setShowM3u8ById({ [record.id]: value });
                  },
                }}
              />
            </>
          ) : null}
        </>
      ),
      // <img
      //   style={{ cursor: 'pointer' }}
      //   alt="视频"
      //   src={`${record.url}?vframe/jpg/offset/0.01/w/100/h/100`}
      //   onClick={() => setCurrentVideoUrl(record.url)}
      // />
    },
    {
      title: '宠物在干嘛',
      dataIndex: 'petAction',
      width: 100,
      valueType: 'select',
      valueEnum: {
        [PetActionEnum.PASSING_BY]: '路过',
        [PetActionEnum.EATING]: '吃饭',
        [PetActionEnum.NOT_PET]: '不是宠物',
        [PetActionEnum.OTHER]: '其它',
        [PetActionEnum.TOILET]: '如厕',
      },
    },
    {
      title: '视频反馈类型',
      dataIndex: 'doubt',
      width: 250,
      search: false,
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      width: 150,
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  useEffect(() => {
    if (listParam && deviceType) {
      requestAiVideoFeedbackList(listParam, deviceType);
    }
  }, [listParam, deviceType]);

  useEffect(() => {
    const formData: AiVideoFeedbackTableForm = {
      ...omit(urlParam, 'createdAt'),
    };
    if (urlParam.start && urlParam.end) {
      formData.createdAt = [dayjs(+urlParam.start), dayjs(+urlParam.end)];
    }
    // const createTimes: Dayjs[] = (
    //   formData.createdAt ? formData.createdAt.split(',') : []
    // ).map((item: string) => dayjs(+item));
    const form = formRef.current;
    // formData.createdAt = createTimes;
    form?.setFieldsValue(formData);

    if (formData.deviceType) {
      setDeviceType(formData.deviceType);
    }
    const { createdAt, ...rest } = formData;
    const _listParam: AiVideoFeedbackListParam = {
      ...initAiVideoFeedbackListParam,
      ...listParam,
      ...rest,
    };
    if (urlParam.start && urlParam.end) {
      _listParam.startTime = dayjs(+urlParam.start).valueOf();
      _listParam.endTime = dayjs(+urlParam.end).valueOf();
    }

    setListParam(_listParam);
  }, [urlParam]);

  return (
    <>
      <ProTable<any>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        formRef={formRef}
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
    </>
  );
};

export default List;
