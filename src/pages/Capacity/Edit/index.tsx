import { ApiSuccessEnum } from '@/models/common.interface';
import { getCycleTimeOptionList } from '@/models/common.util';
import {
  fetchDeviceCapacityCreation,
  fetchDeviceCapacityDetail,
  fetchDeviceCapacityUpdate,
} from '@/models/device/fetch';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { fetchCapacityList } from '@/models/util/fetch';
import { Capacity } from '@/models/util/interface';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, Form, Select, Space, message } from 'antd';
import { Checkbox, Col, Row } from 'antd/lib';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { DeviceCapacityForm } from '../interface';
import {
  initialCapacityForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const urlRestParam = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<DeviceCapacityForm>();
  const [capacityList, setCapacityList] = useState<Capacity[]>([]);

  const isEditing = useMemo(() => {
    return !!+(urlRestParam.id || 0);
  }, [urlRestParam]);

  const allCapacities = Form.useWatch('allCapacities', form);
  const initialCapacities = Form.useWatch('initialCapacities', form);

  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 12 },
  };

  const back = () => {
    history.back();
  };

  const requestCapacityList = async () => {
    try {
      const { items } = await fetchCapacityList();
      setCapacityList(items);
    } catch (error) {
      console.log(error);
    }
  };

  const requestDeviceCapacityDetail = async (_id: number) => {
    const result = await fetchDeviceCapacityDetail(_id);
    const formData = transferDetailToFormData(result);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = useCallback(
    async (formData: DeviceCapacityForm) => {
      const _id = +(urlRestParam.id || 0);
      const _param = transferFormDataToParam(formData, capacityList, _id);
      // console.log('submit', formData, _param);
      setLoading(true);
      let result = '';
      let state = '';
      try {
        if (_param.id) {
          state = '更新';
          result = await fetchDeviceCapacityUpdate(_param);
        } else {
          state = '创建';
          result = await fetchDeviceCapacityCreation(_param);
        }
        if (result === ApiSuccessEnum.success) {
          message.success(`${state}成功！`);
          back();
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    },
    [urlRestParam, capacityList],
  );

  useEffect(() => {
    requestCapacityList();
  }, []);

  useEffect(() => {
    if (!!+(urlRestParam.id || 0))
      requestDeviceCapacityDetail(+(urlRestParam.id || 0));
  }, [urlRestParam]);

  useEffect(() => {
    if (!allCapacities || !allCapacities.length || !initialCapacities) return;
    const _initialCapacityList: DeviceCapacityForm['initialCapacities'] =
      allCapacities.map((capacity) => {
        // 先找出能力原始对象
        const capacityInfo = capacityList.find((cap) => cap.type === capacity);
        // 再找出已经放入初始化能力数据中的能力数据
        const initialCapacity = initialCapacities.find(
          (ic) => ic.type === capacity,
        );
        // 如果初始化能力数据已经存在，则使用初始化的数据回填；否则用原始值进行初始化操作
        return (
          initialCapacity || {
            type: capacity,
            name: capacityInfo?.name || '',
            cycleTime: null,
            enable: false,
          }
        );
      });
    form.setFieldValue('initialCapacities', _initialCapacityList);
  }, [allCapacities, initialCapacities]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        layout="vertical"
        initialValues={initialCapacityForm}
        onFinish={submit}
      >
        <Form.Item
          name="deviceType"
          label="使用设备"
          wrapperCol={{ span: 8 }}
          rules={[
            {
              required: true,
              message: '请选择设备',
            },
          ]}
        >
          <Select
            disabled={isEditing}
            placeholder="请选择设备"
            options={[
              {
                label: SuitableDeviceTypeEnum.D4sh,
                value: SuitableDeviceTypeEnum.D4sh,
              },
              {
                label: SuitableDeviceTypeEnum.D4h,
                value: SuitableDeviceTypeEnum.D4h,
              },
              {
                label: SuitableDeviceTypeEnum.T5,
                value: SuitableDeviceTypeEnum.T5,
              },
              {
                label: SuitableDeviceTypeEnum.T6,
                value: SuitableDeviceTypeEnum.T6,
              },
              {
                label: SuitableDeviceTypeEnum.T7,
                value: SuitableDeviceTypeEnum.T7,
              },
            ]}
          />
        </Form.Item>
        <Form.Item
          name="allCapacities"
          label="全部能力"
          rules={[
            {
              required: true,
              message: '请选择至少一种能力',
            },
          ]}
        >
          <Checkbox.Group>
            <Row gutter={[16, 16]}>
              {capacityList.map((capacity, index) => (
                <Col span={8} key={String(index)}>
                  <Checkbox disabled={isEditing} value={capacity.type}>
                    {capacity.name}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </Form.Item>

        <div style={{ marginBottom: 16 }}>
          <Form.List
            name="initialCapacities"
            rules={[
              {
                validator: async (
                  _,
                  fields: DeviceCapacityForm['initialCapacities'],
                ) => {
                  if (
                    !fields ||
                    !fields.length ||
                    fields.every((field) => !field.enable)
                  ) {
                    return Promise.reject(
                      new Error('请初始化至少一种能力和其循环周期'),
                    );
                  }
                },
              },
            ]}
          >
            {(fields, _, { errors }) => (
              <>
                <Form.Item label="初始化能力" required></Form.Item>
                {fields.map((field, index) => (
                  <Form.Item key={field.key}>
                    <Row justify="space-between" align="bottom">
                      <Col span={12}>
                        <Form.Item
                          name={[field.name, 'enable']}
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={isEditing}
                            onChange={() =>
                              form.validateFields(['initialCapacities'])
                            }
                          >
                            {initialCapacities[index].name}
                          </Checkbox>
                        </Form.Item>{' '}
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={[field.name, 'cycleTime']}
                          label="循环周期（单位：日）"
                          rules={[
                            {
                              required: initialCapacities[index].enable,
                              message: '请选择循环周期',
                            },
                          ]}
                        >
                          <Select
                            disabled={isEditing}
                            placeholder="请选择循环周期"
                            options={getCycleTimeOptionList()}
                            onChange={() =>
                              form.validateFields(['initialCapacities'])
                            }
                          ></Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                ))}
                <Form.Item>
                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>
        </div>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          <Button
            type="primary"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading || isEditing}
            htmlType="submit"
          >
            提交
          </Button>
          <Button type="default" onClick={back}>
            取消
          </Button>
        </Space>
      </Form>
    </ProCard>
  );
};

export default Edit;
