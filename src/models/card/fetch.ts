import { cardApiOption } from '@/services/api/card';
import request, { Data } from '@/utils/request';
import qs from 'qs';
import { ApiSuccessEnum } from '../common.interface';
import {
  CouponCard,
  CouponCardCode,
  CouponCardCodeListParam,
  CouponCardDetail,
  CouponCardListParam,
  CouponCardParam,
  CouponCardUploadResult,
  CouponCardUploadValidResult,
} from './interface';

// 获取卡券列表
export const fetchCouponCardList = (
  param: CouponCardListParam,
): Promise<Data<CouponCard>> => {
  const config = cardApiOption.couponCardList;
  config.option.params = param;
  config.option.paramsSerializer = (params) => {
    return qs.stringify(params, { arrayFormat: 'comma' });
  };
  return request(config.url, config.option);
};

// 创建Card
export const fetchCouponCardCreation = (
  param: CouponCardParam,
): Promise<ApiSuccessEnum> => {
  const config = cardApiOption.couponCardCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 根据Card的id获取详情数据
export const fetchCouponCardDetail = (
  id: number,
): Promise<CouponCardDetail> => {
  const config = cardApiOption.couponCardDetail;
  config.option.restApi = { id: `${id}` };
  return request(config.url, config.option);
};

// 获取卡号列表
export const fetchCouponCardCodeList = (
  param: CouponCardCodeListParam,
): Promise<Data<CouponCardCode>> => {
  const config = cardApiOption.couponCardCodeList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 批量作废
export const fetchCouponCardAbandon = (
  cardCodes: string[],
): Promise<boolean> => {
  const config = cardApiOption.couponCardAbandon;
  config.option.params = { cardCodes };
  config.option.paramsSerializer = (params) => {
    return qs.stringify(params, { arrayFormat: 'comma' });
  };
  return request(config.url, config.option);
};
// excel文件上传
export const fetchCouponCardMobileUpload = (
  file: File,
): Promise<CouponCardUploadResult> => {
  const config = cardApiOption.couponCardMobileUpload;
  const formData = new FormData();
  formData.append('file', file);
  config.option.data = formData;
  return request(config.url, config.option);
};
// 文件内容校验
export const fetchCouponCardMobileFileValidtor = (
  mobileKey: string,
): Promise<CouponCardUploadValidResult> => {
  const config = cardApiOption.couponCardMobileFileValidtor;
  config.option.params = { mobileKey: mobileKey };
  return request(config.url, config.option);
};
