import { RefundOrderRecordListParam } from '@/models/history/interface';
import { initRefundOrderRecordListParam } from '@/models/history/util';
import dayjs from 'dayjs';
import { RefundRecordTableForm, UrlParam } from './interface';

export const transferFormDataToUrlParam = (
  formData: RefundRecordTableForm,
): UrlParam => {
  const param: UrlParam = {};

  if (formData.orderId) {
    param.orderId = formData.orderId;
  }

  if (formData.deviceType) {
    param.deviceType = formData.deviceType as string;
  }

  if (formData.skuName) {
    param.skuName = formData.skuName;
  }

  if (formData.operationTime && formData.operationTime.length > 0) {
    param.startTime = formData.operationTime[0].startOf('day').valueOf();
    param.endTime = formData.operationTime[1].endOf('day').valueOf();
  }

  return param;
};

export const transferUrlParamToFormData = (
  urlParam: UrlParam,
): RefundRecordTableForm => {
  const formData: RefundRecordTableForm = {};

  // 基础字段映射
  if (urlParam.orderId) formData.orderId = urlParam.orderId;
  if (urlParam.deviceType) formData.deviceType = urlParam.deviceType;
  if (urlParam.skuName) formData.skuName = urlParam.skuName;

  // 处理时间
  if (urlParam.startTime && urlParam.endTime) {
    formData.operationTime = [
      dayjs(+urlParam.startTime),
      dayjs(+urlParam.endTime),
    ];
  }
  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): RefundOrderRecordListParam => {
  const listParam: RefundOrderRecordListParam = {
    ...initRefundOrderRecordListParam,
  };
  if (urlParam.orderId) {
    listParam.orderId = urlParam.orderId;
  }
  if (urlParam.deviceType) {
    listParam.deviceType = urlParam.deviceType;
  }
  if (urlParam.startTime && urlParam.endTime) {
    listParam.startOperationTime = +urlParam.startTime;
    listParam.endOperationTime = +urlParam.endTime;
  }
  if (urlParam.skuName) {
    listParam.skuName = urlParam.skuName;
  }
  return listParam;
};
