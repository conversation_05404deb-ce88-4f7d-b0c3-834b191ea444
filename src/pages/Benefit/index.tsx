/**
 * 权益管理页面入口
 */

import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import { Col, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import DeviceTypeList from './DeviceTypeList';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Benefit: React.FC = () => {
  const location = useLocation();
  const [title] = useState(`权益管理`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  const [showDeviceTypeList, setShowDeviceTypeList] = useState(true);

  useEffect(() => {
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      { ...breadcrumbInfo, breadcrumbName: '权益' },
      '',
      location,
    );

    setShowDeviceTypeList(location.pathname.includes('benefit/list'));

    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route: any) => <span>{route.breadcrumbName}</span>,
          items: breadcrumbInfoList as any,
        },
      }}
    >
      <Row gutter={16} style={{ height: '100%' }}>
        {showDeviceTypeList ? (
          <Col span={5}>
            <DeviceTypeList />
          </Col>
        ) : null}
        <Col span={showDeviceTypeList ? 19 : 24}>
          <Outlet />
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Benefit;
