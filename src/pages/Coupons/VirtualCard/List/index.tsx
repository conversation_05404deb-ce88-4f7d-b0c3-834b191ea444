import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { fetchCouponCardList } from '@/models/card/fetch';
import { CouponCard, CouponCardListParam } from '@/models/card/interface';
import { initCouponCardListParam } from '@/models/card/util';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { deviceTypeEnum } from '@/models/device/util';
import { serviceTimeUnitNameObj } from '@/models/product/util';
import { PageActionType } from '@/pages/enum';
import { cardApiOption } from '@/services/api/card';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Image, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { forIn } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { VirtualCardTableForm, VirtualCardUrlParam } from '../interface';
import { transferUrlParamToListParam } from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<VirtualCardUrlParam>({});
  const [dataList, setDataList] = useState<CouponCard[]>([]);
  const [listParam, setListParam] = useState<CouponCardListParam>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const tableFormRef = useRef<ProFormInstance | undefined>();

  // 获取列表数据
  const requestVirtualCardList = async (param: CouponCardListParam) => {
    const { items, ...rest } = await fetchCouponCardList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  const editVirtualCard = (id: number) => {
    history.push(`/coupons/virtual-card/edit/${PageActionType.EDITING}/${id}`);
  };

  // 查看卡号，跳转卡号链接页面
  const gotoCodeList = (record: CouponCard) => {
    // history.push(`/coupons/virtual-card/code/list?batchId=${record.batchId}`);
    postMessageFunction({
      type: 'redirect',
      content: {
        redirectUrl: '/business/virtualCard/codeList',
        param: {
          batchId: record.batchId,
        },
      },
    });
  };

  // 导出卡号 按批次
  const exportCardNumber = (record: CouponCard) => {
    const url = `${location.origin}${getCurrentPrefix()}${
      cardApiOption.couponCardExportByBatchId.url
    }?X-Admin-Session=${localStorage.getItem('sessionToken')}&batchId=${
      record.batchId
    }`;
    window.open(url);
  };

  // 再次发卡
  const copyCouponCard = (record: CouponCard) => {
    history.push(
      `/coupons/virtual-card/edit/${PageActionType.COPYING}/${record.id}`,
    );
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: VirtualCardTableForm = form?.getFieldsValue();
          const param: VirtualCardUrlParam = {};
          forIn(formData, (value, key) => {
            if (value !== undefined) {
              if (key === 'createDate' && formData.createDate?.length) {
                param.createStart = formData.createDate[0].valueOf();
                param.createEnd = formData.createDate[1].valueOf();
              } else {
                (param as any)[key] = value;
              }
            }
          });
          // console.log(param);
          // setUrlParam(param);

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/virtualCard/list`,
              param: param as Record<string, any>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();

          // const _urlParam = {};
          // forIn(urlParam, (_, key) => {
          //   _urlParam[key] = undefined;
          // });
          // setUrlParam(_urlParam);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/virtualCard/list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<CouponCard>> = [
    {
      title: '生成批次',
      dataIndex: 'batchId',
      width: 180,
    },
    {
      title: '卡券名称',
      dataIndex: 'cardCouponName',
      width: 120,
    },
    {
      title: '卡券Plan',
      dataIndex: 'cardCouponNickName',
      width: 120,
    },
    {
      title: '适用设备',
      width: 120,
      dataIndex: 'deviceTypes',
      valueType: 'select',
      valueEnum: deviceTypeEnum,
      render: (_, record) => {
        const devcieTypes = record.skus.map((sku) => sku.deviceType);
        const deviceTypeList = Array.from(new Set(devcieTypes));
        return (
          <>
            {deviceTypeList.map((deviceType) => (
              <div key={deviceType}>{deviceType}</div>
            ))}
          </>
        );
      },
    },
    {
      title: '关联Sku',
      width: 120,
      dataIndex: 'relativeSku',
      render: (_, record) => (
        <>
          {record.skus.map((sku, index) => (
            <div key={String(index)}>
              <RedirectLink
                text={sku.skuName || ''}
                linkUrl="/business/cloudServiceSku"
                params={{ id: sku.productSkuId || 0 }}
              />
            </div>
          ))}
        </>
      ),
    },
    {
      title: '服务时长',
      dataIndex: 'serviceTime',
      width: 100,
      search: false,
      render: (_, record) =>
        `${record.serviceTime}${
          serviceTimeUnitNameObj[record.serviceTimeUnit]
        }`,
    },
    {
      title: '兑换有效期',
      dataIndex: 'exchangeEnd',
      width: 160,
      search: false,
      render: (_, record) =>
        dayjs(record.exchangeEnd).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '卡券封面',
      dataIndex: 'couponImage',
      width: 100,
      search: false,
      render: (_, record) =>
        record.couponImage ? <Image src={record.couponImage} /> : '-',
    },
    {
      title: '实际售价（元）',
      dataIndex: 'realPrice',
      width: 120,
      search: false,
      render: (_, record) => record.realPrice.toFixed(2),
    },
    {
      title: '卡券面额（元）',
      dataIndex: 'facePrice',
      width: 120,
      search: false,
      render: (_, record) => record.facePrice.toFixed(2),
    },
    {
      title: '生成数量',
      dataIndex: 'sendNum',
      width: 100,
      search: false,
    },
    {
      title: '生成时间',
      dataIndex: 'createDate',
      width: 180,
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.createDate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '需求方',
      dataIndex: 'demandSide',
      width: 100,
    },
    {
      title: '操作者',
      dataIndex: 'operatorName',
      width: 100,
      search: false,
    },
    {
      fixed: 'right',
      title: '操作',
      width: 230,
      search: false,
      dataIndex: 'action',
      render: (_, record) => (
        <Space>
          <a onClick={() => gotoCodeList(record)}>查看卡号</a>
          <a onClick={() => exportCardNumber(record)}>导出卡号</a>
          <>
            <a onClick={() => copyCouponCard(record)}>再次发卡</a>
            <Tooltip
              trigger={['click']}
              title="点击后将进入新增卡券页面，卡券规则将自动填充历史卡券信息，可以在历史信息基础上进行修改调整，提高卡券创建效率。"
            >
              <ExclamationCircleOutlined />
            </Tooltip>
          </>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (listParam) requestVirtualCardList(listParam);
  }, [listParam]);

  useEffect(() => {
    if (JSON.stringify(urlParam) === '{}') {
      setListParam({ ...initCouponCardListParam });
      return;
    }

    const tableForm: VirtualCardTableForm = {};
    const form = tableFormRef.current;
    if (!form) return;
    forIn(urlParam, (value, key) => {
      if (key === 'createStart' || key === 'createEnd') {
        tableForm.createDate = tableForm.createDate || [];
        tableForm.createDate.push(dayjs(+value));
      } else {
        tableForm[key] = value;
      }
    });
    form.setFieldsValue(tableForm);

    const param: CouponCardListParam = {
      ...transferUrlParamToListParam(urlParam),
    };
    // console.log(urlParam, tableForm, param);
    setListParam(param);
  }, [urlParam]);

  return (
    <ProTable<CouponCard>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={tableFormRef}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{
        reload: () => {
          if (listParam) setListParam({ ...listParam });
        },
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editVirtualCard(0)}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
