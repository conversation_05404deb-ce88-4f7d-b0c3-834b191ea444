export enum DomainTypeEnum {
  CURRENT = 'current',
  SG = 'sg',
  EU = 'eu',
  US = 'us',
}

export const domainTypeInfo: { [key in DomainTypeEnum]: string } = {
  [DomainTypeEnum.CURRENT]: '当前环境',
  [DomainTypeEnum.EU]: '欧洲环境',
  [DomainTypeEnum.SG]: '新加坡环境',
  [DomainTypeEnum.US]: '美西环境',
};

// 定义域名映射配置
const domainConfig: {
  [key in DomainTypeEnum]?: { sandbox: string; production: string };
} = {
  [DomainTypeEnum.SG]: {
    sandbox: 'oms-sandbox.petktasia.com',
    production: 'oms.petktasia.com',
  },
  [DomainTypeEnum.EU]: {
    sandbox: 'oms-sandbox.eu-pet.com',
    production: 'oms.eu-pet.com',
  },
};

// 构建完整域名的工具函数
const buildFullDomain = (protocol: string, domain: string) =>
  `${protocol}//${domain}`;

// 获取目标域名
const getDomain = (
  type: DomainTypeEnum,
  isLocalhost: boolean,
  protocol: string,
  currentOrigin: string,
) => {
  const config = domainConfig[type];
  if (config) {
    return buildFullDomain(
      protocol,
      isLocalhost ? config.sandbox : config.production,
    );
  }
  // 默认返回当前域名，如果是本地环境则返回测试域名
  return buildFullDomain(
    protocol,
    isLocalhost
      ? 'oms-sandbox.petktasia.com'
      : currentOrigin.replace(/\/$/, ''),
  );
};

export const getTargetDomain = (domainType: DomainTypeEnum) => {
  // 获取当前域名和协议
  const { protocol, origin: currentOrigin } = window.location;

  // 判断是否为本地开发环境
  const isLocalhost = currentOrigin.includes('localhost');

  return getDomain(domainType, isLocalhost, protocol, currentOrigin);
};

export const getCurrentDomainType = (): DomainTypeEnum => {
  // 定义各环境对应的域名列表
  const domainPatterns = {
    [DomainTypeEnum.EU]: [
      'oms.eu-pet.com',
      'oms-sandbox.eu-pet.com',
      'localhost:9990',
      'localhost:9991',
    ],
    [DomainTypeEnum.US]: [
      'oms.petkt.com',
      'oms-sandbox.petkt.com',
      'localhost:9994',
    ],
    [DomainTypeEnum.SG]: [
      'oms.petktasia.com',
      'oms-sandbox.petktasia.com',
      'localhost:9992',
      'localhost:9999',
    ],
  };

  // 获取当前域名
  const { origin } = window.location;
  const hostname = origin.replace(/^https?:\/\//, '');

  // 判断当前域名属于哪个环境
  let currentType = DomainTypeEnum.CURRENT;

  Object.entries(domainPatterns).forEach(([type, patterns]) => {
    if (patterns.some((pattern) => hostname.includes(pattern))) {
      currentType = type as DomainTypeEnum;
    }
  });

  // 只返回当前环境的域名类型
  return currentType;
};
