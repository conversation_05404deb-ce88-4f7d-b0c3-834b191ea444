import { http } from '@/services/base/request';
import { Upload, message } from 'antd';
import { useState } from 'react';

const SimpleUpload = (props) => {
  const { api, onRefresh } = props;

  // 上传完后，更新该值，用于清除Upload组件文件缓存
  const [nowDate, setNowDate] = useState(Date.now());

  const getUploadProps = () => ({
    name: 'file',
    accept: '.xls,.xlsx',
    headers: http.getAuthHeader(),
    action: http.getApi(api),
    onChange: (info) => {
      const { file } = info;
      if (file.status === 'done') {
        setNowDate(Date.now());
        const { response } = file;
        if (response.code !== 20000) {
          message.error(response.message);
          return;
        }

        message.success('导入成功');
        onRefresh && onRefresh();
      }
      if (file.status === 'error') {
        message.error(`${file.name}上传失败`);
      }
    },
  });

  return (
    <Upload key={nowDate} {...getUploadProps()} showUploadList={false}>
      {props.children}
    </Upload>
  );
};

export default SimpleUpload;
