# 小佩云存管理系统

## 项目简介
小佩云存管理系统是小佩商城的运营管理平台，用于管理小佩商城的各种业务功能，包括云服务套餐、权益管理、订单管理、服务管理等。本系统提供了完整的云服务产品生命周期管理能力，支持多币种、多渠道的运营管理。

## 技术栈
- 前端框架：[React](https://reactjs.org)
- 构建工具：[@umijs/max](https://umijs.org)
- UI 组件库：[Ant Design](https://ant.design) 和 [@ant-design/pro-components](https://procomponents.ant.design)
- 状态管理：[dva](https://dvajs.com)
- 请求库：[@mantas/request](https://www.npmjs.com/package/@mantas/request)
- 日期处理：[dayjs](https://day.js.org)
- 样式：[Less](http://lesscss.org)
- 包管理工具：[pnpm](https://pnpm.io)

## 快速开始

### 环境要求
- Node.js: >=14 <16
- 包管理工具: pnpm

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
项目支持多种环境配置，可以通过以下命令启动对应环境的开发服务器：

```bash
# 使用 mock 数据
pnpm start:mock

# 本地开发环境
pnpm start:local

# 沙箱环境
pnpm start:sandbox

# 沙箱环境2
pnpm start:sandbox2

# 线上环境
pnpm start:online

# 预发布环境
pnpm start:pre

# 测试环境
pnpm start:test
```

### 构建项目
```bash
pnpm build
```

## 项目架构

项目采用了典型的 UmiJS 应用架构，主要目录和文件如下：

```
com-petkit-bs-web/
  ├── config/                # 配置文件目录
  │   ├── proxy.ts          # API代理配置
  │   └── routes.ts         # 路由配置
  ├── mock/                 # 模拟数据
  ├── src/                  # 源代码目录
  │   ├── app.ts            # 应用入口
  │   ├── assets/           # 静态资源
  │   │   └── templates/    # 模板文件
  │   ├── components/       # 公共组件
  │   ├── layouts/          # 页面布局
  │   ├── locale/           # 国际化配置
  │   ├── models/           # 数据模型
  │   ├── pages/            # 页面组件
  │   ├── services/         # API服务
  │   ├── types/            # 类型定义
  │   └── utils/            # 工具函数
  ├── .umirc.ts             # UmiJS配置文件
  ├── package.json          # 项目依赖
  └── tsconfig.json         # TypeScript配置
```

## 开发指南

### 代码规范

#### 目录结构规范

新增模块时，应遵循以下目录结构：

```
src/pages/ModuleName/
  ├── index.tsx       # 模块主组件
  ├── index.less      # 模块样式
  ├── interface.ts    # 接口定义
  ├── util.ts         # 工具函数
  ├── List/           # 列表页面
  │   └── index.tsx
  └── Edit/           # 编辑页面
      └── index.tsx
```

#### 命名规范

- **组件文件**: 使用 PascalCase (如 `MyComponent.tsx`)或使用index.tsx放在组件命名的文件夹中
- **钩子文件**: 使用camelCase以`use`为前缀（例如，`useMyHook.ts`）
- **工具函数文件**: 使用 camelCase (如 `myUtil.ts`)
- **样式文件**: 使用 `.less` (如 `index.less`，`Button.less`)

### 开发最佳实践

1. **组件开发**:
   - 将大型组件拆分为小型、可复用的组件
   - 使用组合而非继承来复用组件逻辑
   - 保持单一职责原则，每个组件只做一件事

2. **状态管理**:
   - 使用 dva 进行状态管理
   - 将共享状态提升到合适的层级
   - 避免过度使用全局状态

3. **性能优化**:
   - 使用 `React.memo` 避免不必要的渲染
   - 使用 `useMemo` 和 `useCallback` 缓存计算结果和回调函数
   - 使用虚拟列表处理大量数据

4. **API 调用**:
   - 在 `src/services/api` 目录下定义 API 配置
   - 在 `src/models/*/fetch.ts` 文件中定义数据获取函数
   - 使用 `try/catch` 处理异常

## 主要业务模块

项目包含以下主要业务模块：

1. **云服务套餐管理**: 管理云服务套餐的SKU，包括列表查询、创建、编辑、上下架等操作
2. **权益管理**: 管理服务权益，包括列表查询、创建、编辑、删除等操作
3. **权益对比**: 管理权益对比功能，用于比较不同权益之间的差异
4. **容量管理**: 管理服务容量相关配置
5. **订单管理**: 管理订单信息，包括订单查询、详情查看等
6. **服务管理**: 管理用户已购买的服务，查看服务的使用状态和到期时间
7. **运营套餐管理**: 管理运营套餐，包括列表查询、创建、编辑、上下架、删除等操作
8. **优惠券管理**: 管理虚拟卡券，包括列表查询、创建、编辑等操作

## 开发环境配置

项目支持多种环境配置，通过 `REACT_APP_ENV` 环境变量控制：
- `mock`: 使用 mock 数据
- `local`: 本地开发环境
- `sandbox`: 沙箱环境
- `sandbox2`: 沙箱环境2
- `online`: 线上环境
- `pre`: 预发布环境
- `test`: 测试环境

## 文档指南

项目包含以下详细文档，位于 `.cursor/rules` 目录下：

- **React最佳实践**: [react.mdc] - React开发的通用最佳实践
- **项目概览**: [project-overview.mdc] - 项目整体概览和技术栈说明
- **代码结构**: [code-structure.mdc] - 详细的项目目录结构和关键文件说明
- **开发指南**: [development-guide.mdc] - 环境设置、代码规范和开发最佳实践
- **组件文档**: [components.mdc] - 公共组件的详细说明和使用示例
- **模型和服务**: [models-and-services.mdc] - 数据模型和API服务的使用方法
- **工具函数**: [utils-and-helpers.mdc] - 工具函数的详细说明和使用示例
- **业务模块**: [business-modules.mdc] - 业务模块的功能说明和业务流程

## 常见问题

1. **如何添加新路由?**
   - 在 `config/routes.ts` 文件中添加路由配置
   - 创建对应的页面组件

2. **如何使用dva进行状态管理?**
   - 在 `src/models` 目录下创建对应的模型文件
   - 定义namespace、state、effects和reducers
   - 使用 connect 方法将组件与状态连接

3. **如何处理API请求?**
   - 在 `src/api` 目录下定义API
   - 在 `src/api/fetch.ts` 文件中封装API调用
   - 参考`.claude/templates/README.md`
   - 如果有接口存在多出调用，遵循以下方式创建文件：
     - 在`src/models`目录下自定义`xxx.model.ts`文件
     - 在具体调用接的页面使用`dispatch`来调用
     - 在具体调用接的页面使用`useSelector`来获取返回值

4. **如何进行国际化?**
   - 在 `src/locale` 目录下定义国际化文本
   - 使用 `useIntl` 钩子获取国际化文本

## 团队协作

- 提交代码前请进行代码格式化: `pnpm prettier`
- 遵循约定的目录结构和命名规范
- 编写清晰的注释和文档
- 代码审查时注意业务逻辑和性能优化
