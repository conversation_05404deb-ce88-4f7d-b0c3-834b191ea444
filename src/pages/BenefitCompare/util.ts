import { deviceList } from '@/models/common.util';
import {
  BenefitCompare,
  BenefitCompareParam,
} from '@/models/product/interface';
import { uuid } from '../../utils/uuid';
import { BenefitCompareForm } from './interface';

export const initialBenefitCompareForm: BenefitCompareForm = {
  deviceType: null,
  images: [],
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: BenefitCompareForm,
  id?: number,
): BenefitCompareParam => {
  const param: BenefitCompareParam = {
    deviceType: formData.deviceType || deviceList[0],
    image: formData.images[0].url || '',
  };
  if (id) param.id = id;
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: BenefitCompare,
): BenefitCompareForm => {
  const formData: BenefitCompareForm = {
    deviceType: detail.deviceType,
    images: [
      {
        name: '配图',
        uid: uuid(),
        url: detail.image,
      },
    ],
  };
  return formData;
};
