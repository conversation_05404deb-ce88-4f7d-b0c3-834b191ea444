// import { SkuDetail, SkuParam } from '@/models/sku/interface';
// import { SkuForm } from './interface';

import {
  BundleSkuChangeLogsParam,
  BundleSkuListParam,
  ProductSKUBundle,
  ProductSkuBundleCreationParams,
  ProductSKUBundleUpdateParams,
} from '@/api/sku/bundle/types';
import { SelectOption, StatusEnum } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  ProductSkuCapacityTypeEnum,
  ProductSkuUpdatingLogsParam,
  RecordTypeEnum,
  SkuTypeEnum,
} from '@/models/product/interface';
import { initPaginatorParam } from '@/utils/request';
import { uuid } from '@/utils/uuid';
import { SaleStatusEnum, ServiceTimeUnitEnum } from './adapter';
import { ProductSkuForm, ProductSkuListForm } from './interface';

// export const initialProductSkuForm: SkuForm = {};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: ProductSKUBundle,
): ProductSkuForm => {
  const formData: ProductSkuForm = {
    // SKU内容
    name: detail.name,
    simpleName: detail.shortName,
    aliasName: detail.aliasName || '',
    sort: detail.sort || null,
    serviceTimeUnit: detail.serviceTimeUnit,
    deviceType:
      (detail.skuRelations?.[0]?.skuType as SuitableDeviceTypeEnum) || null,
    deviceNumber: detail.supportedDeviceNum || null,
    promotionName: detail.discountName || '',
    // SKU价格
    currencyPrice: {
      price: detail.price.price,
      linePrice: detail.price.linePrice,
      priceList: detail.price.priceList || [],
    },
    isAutoRenew: detail.price.isReNew === StatusEnum.ENABLE,
    isFirstPreferential:
      detail.price.firstPhasePrice !== undefined &&
      detail.price.firstPhasePrice !== null,
    preferentialPrice: {
      price: detail.price.firstPhasePrice || 0,
      priceList: detail.price.firstPhasePriceList || [],
    },
    // 营销设置
    cornerIcons: detail.cornerMarkIcon
      ? [{ uid: uuid(), name: '角标', url: detail.cornerMarkIcon }]
      : [],
    // description: detail.description,
  };

  return formData;
};

export const productSkuCapacityObj: {
  [key in ProductSkuCapacityTypeEnum]: string;
} = {
  [ProductSkuCapacityTypeEnum.CLOUD_STORAGE]: '云存储',
  [ProductSkuCapacityTypeEnum.SPLENDID_MOMENT]: '每日精彩',
  [ProductSkuCapacityTypeEnum.TIME_LAPSE]: '缩时录影',
};

export const skuTypeObj: { [key in SkuTypeEnum]: string } = {
  [SkuTypeEnum.EVENT]: '事件录制',
  [SkuTypeEnum.CVR]: '持续录制',
  [SkuTypeEnum.BASIC]: '基础服务',
};

export const recordTypeObj: { [key in RecordTypeEnum]: string } = {
  [RecordTypeEnum.EVENT]: '事件录制',
  [RecordTypeEnum.CVR]: '持续录制',
};

// serviceTimeUnitObj 现在通过 adapter 引入

// 录制方式选项
export const recordTypeOptions: Array<SelectOption<RecordTypeEnum>> = [
  {
    value: RecordTypeEnum.EVENT,
    label: '事件录制',
  },
  {
    value: RecordTypeEnum.CVR,
    label: 'CVR录制',
  },
];

// 循环周期选项
export const cycleTimeOptions: SelectOption[] = [
  {
    value: 7,
    label: '7天循环',
  },
  {
    value: 30,
    label: '30天循环',
  },
];

// 适用设备
// export const deviceTypeOptions: Array<SelectOption<SuitableDeviceTypeEnum>> = [
//   {
//     label: 'D4sh',
//     value: SuitableDeviceTypeEnum.D4sh,
//   },
//   {
//     label: 'D4h',
//     value: SuitableDeviceTypeEnum.D4h,
//   },
// ];

export const initialProductSkuForm: ProductSkuForm = {
  // SKU内容
  name: '',
  simpleName: '',
  aliasName: '',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  sort: null,
  deviceType: null,
  deviceNumber: null,
  promotionName: '',
  // SKU价格
  isAutoRenew: false,
  isFirstPreferential: false,
  // 营销设置
  cornerIcons: [],
  // description: '',
  currencyPrice: {
    price: null,
    linePrice: undefined,
    priceList: [],
  },
};

export const transferFormDataToParam = (
  formData: ProductSkuForm,
  relatedSkus?: Array<{
    deviceType: SuitableDeviceTypeEnum;
    skuId?: number;
    skuName?: string;
  }>,
  id?: number,
): ProductSkuBundleCreationParams | ProductSKUBundleUpdateParams => {
  const baseParam: ProductSkuBundleCreationParams = {
    name: formData.name,
    shortName: formData.simpleName,
    aliasName: formData.aliasName,
    discountName: formData.promotionName,
    supportedDeviceNum: formData.deviceNumber || 1,
    serviceTime: 1,
    serviceTimeUnit: formData.serviceTimeUnit,
    level: 1, // 默认等级
    sort: formData.sort || 1,
    skuRelations: [], // 初始化为空数组
    price: {
      price: formData.currencyPrice.price || 0,
      linePrice: formData.currencyPrice.linePrice || undefined,
      isReNew: formData.isAutoRenew ? StatusEnum.ENABLE : StatusEnum.DISABLE,
      priceList: formData.currencyPrice.priceList || [],
      firstPhasePriceList: formData.preferentialPrice?.priceList || [],
    },
    cornerMarkIcon:
      formData.cornerIcons && formData.cornerIcons.length
        ? formData.cornerIcons[0].url || ''
        : '',
  };

  // 处理SKU关联关系，从relatedSkus状态中获取数据
  if (relatedSkus && relatedSkus.length > 0) {
    const validRelatedSkus = relatedSkus.filter((item) => item.skuId);
    baseParam.skuRelations = validRelatedSkus.map((item) => ({
      skuId: item.skuId!,
      skuType: item.deviceType,
    }));
  }

  if (formData.preferentialPrice) {
    baseParam.price.firstPhasePrice = formData.preferentialPrice.price || 0;
  }

  if (id) {
    const updateParam: ProductSKUBundleUpdateParams = {
      ...baseParam,
      id,
      saleStatus: SaleStatusEnum.ONLINE, // 默认上架状态
      syncSupportedDeviceType: StatusEnum.DISABLE, // 默认不同步
    };
    return updateParam;
  }

  return baseParam;
};

export const initProductSkuUpdatingLogsParam: ProductSkuUpdatingLogsParam = {
  ...initPaginatorParam,
  skuId: 0,
};

export const initBundleSkuChangeLogsParam: BundleSkuChangeLogsParam = {
  ...initPaginatorParam,
  bundleSkuId: 0,
};

export const transferListFormToListParam = (
  formData: ProductSkuListForm,
): BundleSkuListParam => {
  const param: BundleSkuListParam = {
    ...initPaginatorParam,
  };
  param.bundleSkuId = formData.id;
  param.name = formData.name;
  param.shortName = formData.shortName;
  param.aliasName = formData.aliasName;
  param.serviceTime = formData.serviceTime;
  param.isRenew = formData.isReNew ? StatusEnum.ENABLE : StatusEnum.DISABLE;
  param.saleStatus = formData.saleStatus;
  return param;
};
