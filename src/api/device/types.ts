/**
 * 设备相关的类型定义
 */

// 适用设备类型枚举
export enum SuitableDeviceTypeEnum {
  CAMERA = 'camera',
  FEEDER = 'feeder',
  WATERER = 'waterer',
  LITTER = 'litter',
  AIR_PURIFIER = 'air_purifier',
  GENERAL = 'general',
}

// 设备类型选项
export interface DeviceTypeOption {
  label: string;
  value: SuitableDeviceTypeEnum;
}

// 设备类型名称映射
export const deviceTypeNameMap: Record<SuitableDeviceTypeEnum, string> = {
  [SuitableDeviceTypeEnum.CAMERA]: '摄像头',
  [SuitableDeviceTypeEnum.FEEDER]: '喂食器',
  [SuitableDeviceTypeEnum.WATERER]: '饮水机',
  [SuitableDeviceTypeEnum.LITTER]: '猫砂盆',
  [SuitableDeviceTypeEnum.AIR_PURIFIER]: '空气净化器',
  [SuitableDeviceTypeEnum.GENERAL]: '通用设备',
};

// 设备类型选项数组
export const deviceTypeOptions: DeviceTypeOption[] = [
  { label: '摄像头', value: SuitableDeviceTypeEnum.CAMERA },
  { label: '喂食器', value: SuitableDeviceTypeEnum.FEEDER },
  { label: '饮水机', value: SuitableDeviceTypeEnum.WATERER },
  { label: '猫砂盆', value: SuitableDeviceTypeEnum.LITTER },
  { label: '空气净化器', value: SuitableDeviceTypeEnum.AIR_PURIFIER },
  { label: '通用设备', value: SuitableDeviceTypeEnum.GENERAL },
];

// 设备基础信息
export interface DeviceInfo {
  id: string;
  name: string;
  type: SuitableDeviceTypeEnum;
  status: number;
  createTime: string;
  updateTime?: string;
}

// 设备列表查询参数
export interface DeviceListParam {
  offset: number;
  limit: number;
  type?: SuitableDeviceTypeEnum;
  status?: number;
  name?: string;
}

/**
 * 获取设备类型文本
 * @param type 设备类型
 * @returns 设备类型文本
 */
export const getDeviceTypeText = (type: SuitableDeviceTypeEnum): string => {
  return deviceTypeNameMap[type] || '未知设备';
};

/**
 * 获取设备类型选项
 * @returns 设备类型选项数组
 */
export const getDeviceTypeOptions = (): DeviceTypeOption[] => {
  return deviceTypeOptions;
};
