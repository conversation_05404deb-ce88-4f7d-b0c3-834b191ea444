/**
 * 权益表相关的API接口配置
 */

import { RequestMethod, RequestOption } from '@mantas/request';

export const benefitTableApi: Record<
  | 'benefitList'
  | 'benefitDetail'
  | 'benefitCreate'
  | 'benefitUpdate'
  | 'benefitDelete'
  | 'benefitReorder'
  | 'benefitStatusSwitch',
  RequestOption
> = {
  // 获取权益表列表
  benefitList: {
    url: '/adm/bs/product/sku/benefit/list',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 获取权益表详情
  benefitDetail: {
    url: '/adm/bs/product/sku/benefit/get',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 创建权益表
  benefitCreate: {
    url: '/adm/bs/product/sku/benefit/create',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 更新权益表
  benefitUpdate: {
    url: '/adm/bs/product/sku/benefit/update',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 删除权益表
  benefitDelete: {
    url: '/adm/bs/product/sku/benefit/delete',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 权益重排序
  benefitReorder: {
    url: '/adm/bs/product/sku/benefit/reorder',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 权益状态切换
  benefitStatusSwitch: {
    url: '/adm/bs/product/sku/benefit/updateStatus',
    option: {
      method: RequestMethod.Post,
    },
  },
};
