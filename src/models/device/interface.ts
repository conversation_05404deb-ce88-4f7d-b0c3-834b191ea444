import { StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';
import { Capacity } from '../util/interface';

export enum VideoTypeEnum {
  QINIU = 'qiniu',
  M3U8 = 'm3u8',
}

export enum SuitableDeviceTypeEnum {
  D4sh = 'D4sh',
  D4h = 'D4h',
  T5 = 'T5',
  T6 = 'T6',
  T7 = 'T7',
}

// 可关联的设备类型数组
export const RELATABLE_DEVICE_TYPES: SuitableDeviceTypeEnum[] = [
  SuitableDeviceTypeEnum.T5,
  SuitableDeviceTypeEnum.T6,
  SuitableDeviceTypeEnum.D4sh,
  SuitableDeviceTypeEnum.D4h,
];

export enum OTAStatusEnum {
  READY = 1,
  UPDATING = 2,
  UPDATED = 3,
  UPDATED_FAILURE = 4,
  OFFLINE = 5,
}

export enum SearchTypeEnum {
  ID = 1,
  MAC = 2,
  OWNER = 3,
  Sn = 4,
}

export enum WifiSearchTypeEnum {
  MAC = 'mac',
  SN = 'sn',
  ID = 'id',
}

export enum BluetoothModeEnum {
  NONE = 0,
  NORMAL = 1,
  SMART = 2,
}

export enum OpenEnum {
  BETA = 0,
  PUBLIC = 1,
}

export enum ForceUpgradeEnum {
  NOT_FORCE = 0,
  FORCE = 1,
}

export interface DeviceInfoParam {
  sn: string;
  deviceType: string;
}

export interface DeviceInfo {
  userId: number;
  deviceId: number;
  deviceType: string;
  deviceName: string;
  sn: string;
  zoneId: string;
}

/**
 * *********************
 * 设备列表相关
 */
export interface DeviceListParam extends PaginatorParam {
  type: SearchTypeEnum;
  s: string;
}

export interface WifiDeviceListParam extends Omit<DeviceListParam, 'type'> {
  type: WifiSearchTypeEnum;
}

export interface Device {
  id: number;
  mac: string;
  sn: string;
  secret: string;
  createdAt: number;
  name?: string;
  hardware: number;
  firmware: string;
  timezone: number;
  signupAt: number;
  locale: string;
  region: number;
  shareOpen: number;
  btMac?: string;
  settings: {
    manualLock: number;
    rev: number;
    dryTime: number;
    targetTemp: number;
    unit: number;
  };
  state: { pim: number; ota: OTAStatusEnum };
  specialLitterAd: {
    adDetailUrl: string;
    adSwitch: number;
    adLinkUrl: string;
    label: string;
    labelUrl: string;
    labelName: string;
  };
  status: StatusEnum;
  power?: StatusEnum;
  ledState?: StatusEnum;
  anionState?: StatusEnum;
  temp?: number;
  remainTime?: number;
  registerTime: number;
  tencentTuple: number;
  firmwareDetails: Array<{ module: string; version: string }>;
  relation?: { userId: string };
  mode?: BluetoothModeEnum;
}
/**
 * *********************
 */

/**
 * *********************
 * ai视频反馈相关
 */
/**
 * 宠物在干嘛
 */
export enum PetActionEnum {
  /**
   * 路过
   */
  PASSING_BY = 1,
  /**
   * 吃饭
   */
  EATING = 2,
  /**
   * 这不是宠物
   */
  NOT_PET = 3,
  /**
   * 其他
   */
  OTHER = 4,
  /**
   * 如厕
   */
  TOILET = 5,
}

export interface AiVideoFeedbackListParam extends PaginatorParam {
  deviceId?: number;
  userId?: string;
  startTime?: number;
  endTime?: number;
  petAction?: PetActionEnum;
}

export interface AiVideoFeedback {
  id: number;
  userId: string;
  deviceId: number;
  deviceType: string;
  eventId: string;
  url: string;
  type: VideoTypeEnum;
  size: number;
  petAction: PetActionEnum;
  doubt: string;
  createdAt: number;
  combineKey: string;
  upload: StatusEnum;
}
/**
 * *********************
 */

/**
 * ******************************************
 * @description 设备能力相关
 */
// 设备所关联的能力信息
export type DeviceCapacityInfo = Omit<Capacity, 'id'>;

// 设备能力列表参数
export interface DeviceCapacityListParam extends PaginatorParam {
  deviceType?: SuitableDeviceTypeEnum;
}

// 设备能力数据 列表+详情 共用
export interface DeviceCapacity {
  id: number;
  deviceType: SuitableDeviceTypeEnum;
  allCapacities: DeviceCapacityInfo[];
  initialCapacities: DeviceInitialCapacity[];
  createTime: number;
  updateTime: number;
}

// 设备所关联的 初始化能力信息
export interface DeviceInitialCapacity extends DeviceCapacityInfo {
  cycleTime: number;
}

// 创建/更新设备能力参数
export interface DeviceCapacityParam {
  id?: number;
  deviceType: string;
  allCapacities: AllCapacityParam[];
  initialCapacities: DeviceInitialCapacityParam[];
}

// 全部能力的参数格式
export type AllCapacityParam = Omit<Capacity, 'id'>;

// 创建/更新设备能力中初始化能力参数
export interface DeviceInitialCapacityParam {
  type: string;
  cycleTime: number;
  name: string;
}
/**
 * ******************************************
 */
/**
 * ******************************************
 * @description 设备能力相关
 */
// 设备所关联的能力信息
export interface M3u8InfoParam {
  eventId: string;
  combineKey: string;
}
/**
 * ******************************************
 */
