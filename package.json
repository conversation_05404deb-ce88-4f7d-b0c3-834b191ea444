{"private": true, "scripts": {"start": "max dev", "build": "max build", "postinstall": "max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "start:mock": "cross-env REACT_APP_ENV=mock pnpm start", "start:local": "cross-env REACT_APP_ENV=local pnpm start", "start:sandbox": "cross-env REACT_APP_ENV=sandbox pnpm start", "start:sandbox2": "cross-env REACT_APP_ENV=sandbox2 pnpm start", "start:online": "cross-env REACT_APP_ENV=online pnpm start", "start:pre": "cross-env REACT_APP_ENV=pre pnpm start", "start:test": "cross-env REACT_APP_ENV=test pnpm start"}, "engines": {"node": ">=14 <16"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.20", "@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@esbuild/darwin-arm64": "^0.25.5", "@mantas/component-uploader": "^0.4.0", "@mantas/request": "^2.10.3", "@umijs/hooks": "^1.9.3", "@umijs/max": "^4.0.81", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "^5.14.0", "classnames": "^2.3.2", "dayjs": "^1.11.9", "decimal.js": "^10.4.3", "esbuild": "^0.25.5", "hls.js": "^1.5.18", "lodash": "^4.17.21", "mimetypes": "^0.1.1", "qiniu-js": "^3.4.1", "qs": "^6.11.2", "react-activation": "^0.12.4", "react-player": "^2.16.0", "react-sortable-hoc": "^2.0.0"}, "devDependencies": {"@types/lodash": "^4.14.198", "@types/qs": "^6.9.8", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@umijs/max": "^4.0.29", "cross-env": "^7.0.3", "eslint-plugin-import": "^2.28.1", "lint-staged": "^10.5.4", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.3", "prettier-plugin-packagejson": "^2.4.5", "tailwindcss": "^3", "typescript": "^5.2.2", "umi-plugin-keep-alive": "^0.0.1-beta.35", "yorkie": "^2.0.0"}}