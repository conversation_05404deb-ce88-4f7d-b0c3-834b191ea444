import { skuApiOption } from '@/services/api/sku';
import request from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  ServicePlan,
  ServiceSku,
  UpdatingSkuExpirationParam,
  UpdatingSkuExpirationParamV2,
} from './interface';

// 获取ServiceSku列表
export const fetchServiceSkuList = (
  serviceId: number,
  device: string,
): Promise<ServiceSku[]> => {
  const config = skuApiOption.serviceSkuList;
  config.option.restApi = { device };
  config.option.data = { serviceId };
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取服务Plan列表 - 服务解耦 - v2.0.0
export const fetchServicePlanList = (
  serviceId: number,
  device: string,
): Promise<ServicePlan[]> => {
  const config = skuApiOption.servicePlanList;
  config.option.params = { serviceId, deviceType: device };
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新SKU到期时间
export const fetchUpdatingSkuExpirationDate = (
  param: UpdatingSkuExpirationParam,
  device: string,
): Promise<ApiSuccessEnum> => {
  const config = skuApiOption.updatingSkuExpirationDate;
  config.option.restApi = { device };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新SKU到期时间 - 服务解耦 - v2.0.0
export const fetchUpdatingSkuExpirationDateV2 = (
  param: UpdatingSkuExpirationParamV2,
): Promise<ApiSuccessEnum> => {
  const config = skuApiOption.updatingSkuExpirationDateV2;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
