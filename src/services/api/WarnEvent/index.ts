import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const eventApiOption: Record<
  'eventList' | 'updateStatus',
  RequestOption
> = {
  // 获取Order列表
  eventList: {
    url: '/adm/bs/events/queryList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  updateStatus: {
    url: '/adm/bs/events/updateStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
