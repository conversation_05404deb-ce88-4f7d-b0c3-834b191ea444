import {
  RefundDetail as RefundDetailInfo,
  RefundListInfo,
  RefundTypeEnum,
} from '@/models/refund/interface';
import {
  refundStatusNameObj,
  refundTypeEnumNameObj,
} from '@/models/refund/util';
import { Modal, Table, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';

interface Props {
  refundDetail: RefundDetailInfo;
  onClose: () => void;
}

const RefundDetail: React.FC<Props> = ({ refundDetail, onClose }: Props) => {
  const columns: ColumnsType<RefundListInfo> = [
    { title: '操作时间', dataIndex: 'createdTime' },
    { title: '时区', dataIndex: 'timezone' },
    {
      title: '退款方式',
      dataIndex: 'refundType',
      render: (_, record) =>
        refundTypeEnumNameObj[record.refundType as RefundTypeEnum],
    },
    {
      title: '退款金额',
      dataIndex: 'refundAmount',
      render: (_, record) => `${record.refundAmount}${record.currency}`,
    },
    {
      title: '退款状态',
      dataIndex: 'status',
      render: (_, record) => refundStatusNameObj[record.status],
    },
    { title: '退款原因', dataIndex: 'refundReason' },
    { title: '操作人', dataIndex: 'operator' },
  ];
  return (
    <Modal
      title={null}
      footer={null}
      open={true}
      width="80%"
      onCancel={onClose}
    >
      <Table<RefundListInfo>
        title={(data) => {
          console.log(data);
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Typography.Title
                level={4}
                style={{ marginBottom: 0, marginRight: 32 }}
              >
                退款详情
              </Typography.Title>
              <Typography.Text type="secondary" style={{ marginRight: 16 }}>
                退款笔数：{refundDetail.refundCount}
              </Typography.Text>
              <Typography.Text type="secondary">
                退款金额：{refundDetail.totalRefundAmount}
              </Typography.Text>
            </div>
          );
        }}
        dataSource={refundDetail.refundList}
        columns={columns}
      />
    </Modal>
  );
};

export default RefundDetail;
