import { fetchRefund, fetchRefundInfoById } from '@/models/refund/fetch';
import {
  AvailableRefundInfo,
  RefundParam,
  RefundResult,
  RefundStatusEnum,
  RefundTypeEnum,
} from '@/models/refund/interface';
import { ServicePlan } from '@/models/sku/interface';
import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Typography,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { RefundForm } from './interface';
import { initRefundForm } from './util';

interface Props {
  serviceSku: ServicePlan;
  availableRefundInfo: AvailableRefundInfo;
  onSubmit?: (refundResult: RefundResult) => void;
  onClose?: () => void;
}

const Refund: React.FC<Props> = ({
  serviceSku,
  availableRefundInfo,
  onSubmit,
  onClose,
}: Props) => {
  const INTERVAL_TIME = 30;
  const [form] = Form.useForm<RefundForm>();
  const type = Form.useWatch<RefundTypeEnum>('type', form);
  const [loading, setLoading] = useState(false);

  // 进行退款操作
  const requestRefundSuccessById = async (
    refundResult: RefundResult,
  ): Promise<RefundStatusEnum> => {
    const refundInfo = await fetchRefundInfoById(refundResult.refundId);
    return refundInfo.status;
  };

  const refundSuccess = (refundResult: RefundResult) => {
    setLoading(false);
    onSubmit?.(refundResult);
    message.success('退款成功');
  };

  const pollingRefundStatus = async (refundResult: RefundResult) => {
    const startTime = dayjs().unix();
    const pollingTimeId = setInterval(async () => {
      const now = dayjs().unix();
      const refundStatus = await requestRefundSuccessById(refundResult);

      if (
        now - startTime >= INTERVAL_TIME ||
        refundStatus === RefundStatusEnum.SUCCEEDED
      ) {
        clearInterval(pollingTimeId);
        refundSuccess(refundResult);
        return;
      } else if (refundStatus === RefundStatusEnum.FAILED) {
        clearInterval(pollingTimeId);
        setLoading(false);
        message.error('退款失败');
        return;
      }

      console.log('继续获取退款信息');
    }, 3000);
  };

  const submit = async (formData: RefundForm) => {
    const param: RefundParam = {
      orderId: serviceSku.orderId,
      amount: formData.amount || 0,
      reason: formData.reason,
    };
    console.log('submit', param);
    setLoading(true);
    try {
      const refundResult = await fetchRefund(param);
      if (refundResult.status === RefundStatusEnum.SUCCEEDED) {
        refundSuccess(refundResult);
        return;
      }
      pollingRefundStatus(refundResult);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const confirmToRefund = (formData: RefundForm) => {
    Modal.confirm({
      content: `提交后，退款金额将原路返回用户，是否确认？`,
      onOk: () => {
        submit(formData);
      },
    });
  };

  useEffect(() => {
    // if (type === RefundTypeEnum.BANLANCE) {
    //   form.setFieldValue('amount', availableRefundInfo.availableRefundAmount);
    // } else
    if (type === RefundTypeEnum.ALL) {
      form.setFieldValue('amount', availableRefundInfo.availableRefundAmount);
    } else {
      form.setFieldValue('amount', null);
    }
  }, [type]);

  return (
    <Modal
      title="退款"
      footer={null}
      open={true}
      width="60%"
      onCancel={onClose}
    >
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        form={form}
        initialValues={initRefundForm}
        onFinish={confirmToRefund}
      >
        <Form.Item
          name="type"
          label="退款类型"
          rules={[{ required: true, message: '请选择退款类型' }]}
        >
          <Radio.Group>
            <Radio value={RefundTypeEnum.PARTIAL}>指定金额</Radio>
            {/* <Radio value={RefundTypeEnum.BANLANCE}>余额</Radio> */}
            <Radio value={RefundTypeEnum.ALL}>全退</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="amount"
          label="退款金额(元)"
          rules={[
            { required: true, message: '请输入退款金额' },
            {
              validator: (_, value) => {
                console.log(value);
                // if (value === null || value === undefined) {
                //   return Promise.reject('请输入退款金额');
                // }
                if (typeof value === 'number' && value < 0.01) {
                  return Promise.reject('退款金额必须大于等于0.01');
                }
                if (value && `${value}`.split('.')[1]?.length > 2) {
                  return Promise.reject('退款金额必须保留2位小数');
                }
                return Promise.resolve();
              },
            },
          ]}
          extra={
            <Typography.Text type="secondary">
              当前可退最大金额：{availableRefundInfo.availableRefundAmount}
            </Typography.Text>
          }
        >
          <InputNumber
            disabled={type !== RefundTypeEnum.PARTIAL}
            min={0}
            max={availableRefundInfo.availableRefundAmount}
            step={0.01}
            placeholder="请输入退款金额（大于0，且最大不超过实际支付价格）"
          />
        </Form.Item>
        <Form.Item
          name="reason"
          label="退款原因"
          rules={[{ required: true, message: '请输入退款原因' }]}
        >
          <Input.TextArea
            rows={2}
            placeholder="请输入退款原因"
            maxLength={25}
            showCount
          />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
          <Button
            htmlType="submit"
            style={{ marginRight: 16 }}
            type="primary"
            disabled={loading}
            loading={loading}
          >
            提交
          </Button>
          <Button onClick={onClose}>取消</Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Refund;
