import {
  CouponCardUsageModeEnum,
  ExchangeStatusEnum,
  SendWayEnum,
} from '@/models/card/interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { ServiceTimeUnitEnum } from '@/models/product/interface';
import { UploadFile } from 'antd';
import { Dayjs } from 'dayjs';

export interface VirtualCardUrlParam
  extends Omit<VirtualCardTableForm, 'createDate' | 'deviceTypes'> {
  deviceTypes?: string;
  createStart?: number;
  createEnd?: number;
}

export interface VirtualCardTableForm {
  batchId?: string;
  cardCouponName?: string;
  cardCouponNickName?: string;
  deviceTypes?: SuitableDeviceTypeEnum[];
  activatedDeviceType?: SuitableDeviceTypeEnum;
  relativeSku?: string;
  createDate?: Dayjs[];
  demandSide?: string;
}

export interface DeviceSkuForm {
  skuId: number;
  deviceType: SuitableDeviceTypeEnum | null;
}

export interface VirtualCardForm {
  name: string;
  aliasName: string;
  deviceSkuList: DeviceSkuForm[];
  serviceTime: number | null;
  serviceTimeUnit: ServiceTimeUnitEnum;
  exchangeDate: Dayjs | null;
  coverImage: UploadFile[];
  realPrice: number | null;
  facePrice: number | null;
  userType: CouponCardUsageModeEnum;
  userMobileList: string[];
  userNum: number | null;
  demandSide: string;
}

export interface VirtualCardCodeUrlParam
  extends Omit<VirtualCardCodeTableForm, 'exchangeEndTime'> {
  exchangeStartTime?: number;
  exchangeEndTime?: number;
  startTime?: number;
  endTime?: number;
}

export interface VirtualCardCodeTableForm {
  cardCode?: string;
  mobile?: string;
  snCode?: string;
  exchangeStatus?: ExchangeStatusEnum;
  sendWay?: SendWayEnum;
  exchangedDate?: Dayjs[];
  batchId?: string;
  deviceTypes?: SuitableDeviceTypeEnum[];
  activatedDeviceType?: SuitableDeviceTypeEnum;
  createDate?: Dayjs[];
}
