// import { SkuDetail, SkuParam } from '@/models/sku/interface';
// import { SkuForm } from './interface';

import { SelectOption } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  ProductSku,
  ProductSkuBenefit,
  ProductSkuCapacityTypeEnum,
  ProductSkuDetail,
  ProductSkuListParam,
  ProductSkuParam,
  ProductSkuUpdatingLogsParam,
  RecordTypeEnum,
  ReNewEnum,
  ServiceTimeUnitEnum,
  SkuTypeEnum,
  SyncSkuParam,
} from '@/models/product/interface';
import { initPaginatorParam } from '@/utils/request';
import { uuid } from '@/utils/uuid';
import { ProductSkuForm, ProductSkuListForm } from './interface';

// export const initialProductSkuForm: SkuForm = {};

export const transferListFormDataToParam = (
  formData: ProductSkuListForm,
  listParam: ProductSkuListParam,
): ProductSkuListParam => {
  const param: ProductSkuListParam = {
    limit: listParam.limit,
    offset: 0,
  };
  param.skuId = formData.id;
  param.skuName = formData.name;
  param.skuAlias = formData.aliasName;
  param.capacity = formData.capacities;
  param.deviceType = formData.deviceType;
  if (formData.isReNew) {
    param.isReNew = +formData.isReNew;
  }
  if (formData.saleStatus) {
    param.saleStatus = +formData.saleStatus;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: ProductSkuDetail,
): ProductSkuForm => {
  const formData: ProductSkuForm = {
    // SKU内容
    name: detail.name,
    simpleName: detail.shortName,
    aliasName: detail.aliasName,
    level:
      detail.level !== undefined && detail.level !== null ? detail.level : null,
    sort: detail.sort || null,
    serviceTimeUnit: detail.serviceTimeUnit,
    deviceType: detail.deviceTypes[0],
    // SKU能力
    skuCapacities: detail.capacities.map((capacity) => ({
      type: capacity.type,
      name: '',
      minCycleTime: 0,
      cycleTime: capacity.cycleTime,
      enable: true,
    })),
    // SKU价格
    currencyPrice: {
      price: detail.price.price,
      linePrice: detail.price.linePrice,
      priceList: detail.price.priceList || [],
    },
    isAutoRenew: detail.price.isReNew,
    isFirstPreferential:
      detail.price.firstPhasePrice !== undefined &&
      detail.price.firstPhasePrice !== null,
    preferentialPrice: {
      price: detail.price.firstPhasePrice || 0,
      priceList: detail.price.firstPhasePriceList || [],
    },
    // 营销设置
    cornerIcons: detail.cornerMarkIcon
      ? [{ uid: uuid(), name: '角标', url: detail.cornerMarkIcon }]
      : [],
    // description: detail.description,
  };

  return formData;
};

export const productSkuCapacityObj: {
  [key in ProductSkuCapacityTypeEnum]: string;
} = {
  [ProductSkuCapacityTypeEnum.CLOUD_STORAGE]: '云存储',
  [ProductSkuCapacityTypeEnum.SPLENDID_MOMENT]: '每日精彩',
  [ProductSkuCapacityTypeEnum.TIME_LAPSE]: '缩时录影',
};

export const skuTypeObj: { [key in SkuTypeEnum]: string } = {
  [SkuTypeEnum.EVENT]: '事件录制',
  [SkuTypeEnum.CVR]: '持续录制',
  [SkuTypeEnum.BASIC]: '基础服务',
};

export const recordTypeObj: { [key in RecordTypeEnum]: string } = {
  [RecordTypeEnum.EVENT]: '事件录制',
  [RecordTypeEnum.CVR]: '持续录制',
};

export const serviceTimeUnitObj: { [key in ServiceTimeUnitEnum]?: string } = {
  [ServiceTimeUnitEnum.YEAR]: '1年',
  [ServiceTimeUnitEnum.MONTH]: '1月',
  // [ServiceTimeUnitEnum.DAY]: '日',
};

// 录制方式选项
export const recordTypeOptions: Array<SelectOption<RecordTypeEnum>> = [
  {
    value: RecordTypeEnum.EVENT,
    label: '事件录制',
  },
  {
    value: RecordTypeEnum.CVR,
    label: 'CVR录制',
  },
];

// 循环周期选项
export const cycleTimeOptions: SelectOption[] = [
  {
    value: 7,
    label: '7天循环',
  },
  {
    value: 30,
    label: '30天循环',
  },
];

// 适用设备
// export const deviceTypeOptions: Array<SelectOption<SuitableDeviceTypeEnum>> = [
//   {
//     label: 'D4sh',
//     value: SuitableDeviceTypeEnum.D4sh,
//   },
//   {
//     label: 'D4h',
//     value: SuitableDeviceTypeEnum.D4h,
//   },
// ];

export const initialProductSkuForm: ProductSkuForm = {
  // SKU内容
  name: '',
  simpleName: '',
  aliasName: '',
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  level: null,
  sort: null,
  deviceType: null,
  // SKU能力
  skuCapacities: [],
  // SKU价格
  isAutoRenew: false,
  isFirstPreferential: false,
  // 营销设置
  cornerIcons: [],
  // description: '',
  currencyPrice: {
    price: null,
    linePrice: undefined,
    priceList: [],
  },
};

export const transferFormDataToParam = (
  formData: ProductSkuForm,
  associateSkuList: ProductSku[],
  benefitList: ProductSkuBenefit[] = [],
  id?: number,
): ProductSkuParam => {
  const param: ProductSkuParam = {
    name: formData.name,
    shortName: formData.simpleName,
    aliasName: formData.aliasName,
    level: formData.level || 0,
    sort: formData.sort || 1,
    benefits: benefitList.map((item, index) => ({
      id: item.id,
      sort: index,
      handpick: item.handpick,
      selectedValue: item.selectedValue, // 添加选中的权益值
    })),
    capacities: formData.skuCapacities
      .filter((cap) => cap.cycleTime)
      .map((cap) => ({
        type: cap.type,
        cycleTime: cap.cycleTime || 0,
      })),
    serviceTime: 1,
    serviceTimeUnit: formData.serviceTimeUnit,
    deviceTypes: [formData.deviceType || SuitableDeviceTypeEnum.D4sh],
    price: {
      price: formData.currencyPrice.price || 0,
      linePrice: formData.currencyPrice.linePrice || undefined,
      isReNew: +formData.isAutoRenew as ReNewEnum,
      priceList: formData.currencyPrice.priceList || [],
      firstPhasePriceList: formData.preferentialPrice?.priceList || [],
    },
    cornerMarkIcon:
      formData.cornerIcons && formData.cornerIcons.length
        ? formData.cornerIcons[0].url || ''
        : '',
    // description: formData.description,
  };

  if (formData.preferentialPrice) {
    param.price.firstPhasePrice = formData.preferentialPrice.price || 0;
  }

  if (id) param.id = id;

  associateSkuList.forEach((item) => {
    param.relationSkuId = item.id;
  });

  return param;
};

export const initProductSkuUpdatingLogsParam: ProductSkuUpdatingLogsParam = {
  ...initPaginatorParam,
  skuId: 0,
};

export const transferListFormToListParam = (
  formData: ProductSkuListForm,
): ProductSkuListParam => {
  const param: ProductSkuListParam = {
    ...initPaginatorParam,
  };
  param.skuId = formData.id;
  param.skuName = formData.name;
  param.skuShortName = formData.shortName;
  param.skuAlias = formData.aliasName;
  param.capacity = formData.capacities;
  param.isReNew = formData.isReNew;
  param.saleStatus = formData.saleStatus;
  param.deviceType = formData.deviceType;
  param.sort = formData.sort;
  param.level = formData.level;
  param.serviceTime = formData.serviceTime;
  param.serviceTimeUnit = formData.serviceTimeUnit;
  return param;
};

export const transferFormDataToSyncSkuParam = (
  param: ProductSkuParam,
  benefitList: ProductSkuBenefit[] = [],
): SyncSkuParam => {
  const _benefitList = benefitList.map((benefit) => ({
    id: benefit.id,
    name: benefit.name,
    icon: benefit.icon,
    description: benefit.description,
    image: benefit.image,
    distributedUniqueId: benefit.distributedUniqueId,
  }));
  const syncParam: SyncSkuParam = {
    ...param,
    benefitList: _benefitList,
  };
  return syncParam;
};
