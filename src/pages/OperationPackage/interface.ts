import { CurrencyPrice } from '@/components/CurrencyPriceForm/interface';
import { StatusEnum } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { ActTimeEnum, ActTypeEnum } from '@/models/package/interface';
import { ServiceTimeUnitEnum } from '@/models/product/interface';
import { UploadFile } from 'antd';
import { Dayjs } from 'dayjs';

export enum ImageTypeEnum {
  ACT = 'act',
  POP = 'pop',
}

export type ImageConfig = {
  [key in ImageTypeEnum]: Partial<{ [key: string]: boolean }>;
};

export interface OperationPackageTableForm {
  saleStatus?: StatusEnum;
  deviceType?: SuitableDeviceTypeEnum;
  actName?: string;
}

export interface OperationPackageForm {
  deviceType: SuitableDeviceTypeEnum | null;
  content: ActTypeEnum | null;
  prop: number | null;
  // 活动配图
  actImage: { zh_CN: UploadFile[]; en_US: UploadFile[] };
  // 弹框配图
  popImage: { zh_CN: UploadFile[]; en_US: UploadFile[] };
  priority: number | null;
  actTime: ActTimeEnum;
  times: Dayjs[];
  activityName: string;
  isNeedUpdateSku: StatusEnum;
  // 服务时长
  serviceTime: number | null;
  serviceTimeUnit: ServiceTimeUnitEnum;
  activitySkuName: string;
  // 活动角标
  activityCornerIcons: UploadFile[];
  // // 活动价格
  // activityPrice: number | null;
  currencyPrice: CurrencyPrice;
}

export interface ActPropForm {
  name: string;
  number: number;
}
