/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

//  国内：正式 https://api.petkit.com     测试 https://api-sandbox.petkit.com
//  海外： 正式      测试

const localDomain = '192.168.17.45:9999';
// const localDomain = 'bs-local-devapi.petkit.com';
const mockRootDomain = 'yapi.petkit.com/mock';
const domains: { [key: string]: string } = {
  mockDomains: `${mockRootDomain}/${443}/`,
  sandboxDomains: `oms-sandbox.petkit.cn/6`,
  sandboxDomains2: `oms-sandbox2.petkit.cn/6`,
  onlineDomains: `oms.petkit.cn/latest`,
};

const getProxyApiInfo = (protocol: 'http' | 'https', domain: string = '') => {
  const _domain = domain.includes('http') ? domain : `${protocol}://${domain}`;
  return {
    '/adm/app': {
      target: `http://${domains.sandboxDomains}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: true,
    },
    '/adm': {
      target: `${_domain}`,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: true,
    },
  };
};

const proxy: { [key: string]: { [key: string]: any } } = {
  local: {
    ...getProxyApiInfo('http', `${localDomain}`),
  },
  mock: {
    ...getProxyApiInfo('http', `${domains.mockDomains}`),
  },
  sandbox: {
    ...getProxyApiInfo('http', `${domains.sandboxDomains}`),
  },
  sandbox2: {
    ...getProxyApiInfo('http', `${domains.sandboxDomains2}`),
  },
  online: {
    ...getProxyApiInfo('http', `${domains.onlineDomains}`),
  },
};

export default proxy;
