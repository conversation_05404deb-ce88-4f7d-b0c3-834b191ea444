import { DomainTypeEnum } from './targetDomain';

export const getCurrentPrefix = (
  domainType: DomainTypeEnum = DomainTypeEnum.CURRENT,
) => {
  const sandboxPrefix = '/6';
  const onlinePrefix = '/latest';

  // 当为EU或SG域名时，只判断是否为sandbox环境
  if (domainType === DomainTypeEnum.EU || domainType === DomainTypeEnum.SG) {
    return origin.includes('sandbox') ||
      origin.includes('local') ||
      origin.includes('127') ||
      origin.includes('192.168')
      ? sandboxPrefix
      : onlinePrefix;
  }

  // 当为当前域名时，保持原有逻辑
  if (origin.includes('sandbox')) return sandboxPrefix;
  else if (
    origin.includes('local') ||
    origin.includes('127') ||
    origin.includes('192.168') ||
    origin.includes('dev-dima')
  )
    return '';
  else return onlinePrefix;
};
