import type { ApiSuccessEnum, Data } from '@/api/common/types';
import type {
  Benefit,
  BenefitListParam,
  BenefitParam,
  ProductSku,
  ProductSkuListParam,
  ProductSkuParam,
} from '@/api/product/types';
import { request } from '@/utils/request';
import { RequestMethod, RequestOption } from '@mantas/request';

/**
 * 产品服务 - 按model模块分类封装
 */
export const productService = {
  /**
   * 获取产品SKU列表
   */
  getProductSkuList: (
    params: ProductSkuListParam,
  ): Promise<Data<ProductSku>> => {
    return request('/api/product/skus', {
      method: RequestMethod.GET,
      params,
    } as RequestOption);
  },

  /**
   * 创建产品SKU
   */
  createProductSku: (data: ProductSkuParam): Promise<ProductSku> => {
    return request('/api/product/skus', {
      method: RequestMethod.POST,
      data,
    } as RequestOption);
  },

  /**
   * 更新产品SKU
   */
  updateProductSku: (
    id: number,
    data: Partial<ProductSkuParam>,
  ): Promise<ProductSku> => {
    return request(`/api/product/skus/${id}`, {
      method: RequestMethod.PUT,
      data,
    } as RequestOption);
  },

  /**
   * 删除产品SKU
   */
  deleteProductSku: (id: number): Promise<ApiSuccessEnum> => {
    return request(`/api/product/skus/${id}`, {
      method: RequestMethod.DELETE,
    } as RequestOption);
  },

  /**
   * 获取权益列表
   */
  getBenefitList: (params: BenefitListParam): Promise<Data<Benefit>> => {
    return request('/api/product/benefits', {
      method: RequestMethod.GET,
      params,
    } as RequestOption);
  },

  /**
   * 创建权益
   */
  createBenefit: (data: BenefitParam): Promise<Benefit> => {
    return request('/api/product/benefits', {
      method: RequestMethod.POST,
      data,
    } as RequestOption);
  },

  /**
   * 更新权益
   */
  updateBenefit: (
    id: number,
    data: Partial<BenefitParam>,
  ): Promise<Benefit> => {
    return request(`/api/product/benefits/${id}`, {
      method: RequestMethod.PUT,
      data,
    } as RequestOption);
  },
};
