import { postMessageFunction } from '@/models/common.util';
import Request, {
  ErrorInfo,
  RequestOptionsInit,
  Result,
  TokenPlaceEnum,
} from '@mantas/request';
import { history } from '@umijs/max';
import { message } from 'antd';

interface ResponseResult<T> {
  result?: T;
  error?: ResponseError;
}

interface ResponseError {
  code: number;
  msg: string;
}

export interface PaginatorParam {
  limit: number;
  offset: number;
}

export interface Paginator {
  limit: number;
  offset: number;
  total: number;
}

export type Data<T = unknown> = Paginator & {
  items: T[];
};

export const initPaginatorParam: PaginatorParam = {
  limit: 10,
  offset: 0,
};

export const initPaginator: Paginator = {
  limit: 10,
  offset: 0,
  total: 0,
};

export const requestService: {
  // key: url
  errorInfoObj: Partial<{ [key: string]: Result }>;
} = {
  errorInfoObj: {},
};

let request: Request | null = null;

if (!request) {
  request = new Request({
    token: localStorage.getItem('sessionToken') || '',
    tokenKey: 'X-Admin-Session',
    // 不知道为什么，此处的session携带位置必须设置为body or query中才可以实现跨域，否则会报跨域问题
    tokenPlaces: [TokenPlaceEnum.PARAMS],
    timeout: 50 * 1000,
    customResolveResponseResult: (
      responseData: ResponseResult<any>,
      requestOption: RequestOptionsInit,
    ) => {
      const result: Result<any> = {
        code: -1,
        data: null,
        message: '请求失败！',
      };
      if (
        JSON.stringify(responseData) === '{}' ||
        responseData.result ||
        responseData.result === 0
      ) {
        result.code = 0;
        result.message = '';
        result.data = responseData.result;
        requestService.errorInfoObj[requestOption.url] = undefined;
      } else if (responseData.error) {
        result.code = responseData.error.code;
        result.message = responseData.error.msg;
        requestService.errorInfoObj[requestOption.url] = result;
      }
      return result;
    },
    resolveHttpStatusOperation: {
      401: () => {
        const isLoginPage = history.location.pathname === '/login';
        if (!isLoginPage) {
          history.replace('/login');
        }
      },
    },
    onErrorInfo: (errorInfo: ErrorInfo) => {
      const noTipCodeList = [653007, 651005];
      console.log(errorInfo);
      const {
        response: {
          data: { code },
        },
      } = errorInfo;
      if (!noTipCodeList.includes(code)) {
        message.error(errorInfo.errorMessage);
        return;
      }

      if (code === 651005) {
        message.error('设备存在有效的云存套餐');
        return;
      }

      if (code === 653007) {
        message.error('云存套餐限制转移');
        return;
      }

      console.log('errorInfo', errorInfo);
      try {
        const noTipCodeList = [653007, 653013, 653015, 651005];
        const {
          response: {
            data: { code },
            request: { url },
          },
        } = errorInfo;

        if (code === 5 || code === 6) {
          if (url.includes('forwardSync')) {
            return;
          }

          postMessageFunction({
            type: 'relogin',
          });
        }

        if (!noTipCodeList.includes(code)) {
          message.error(errorInfo.errorMessage);
        }
      } catch (error) {
        console.log(error);
      }
    },
  });
}

export default request.getRequestResult();
