import request, { Data } from '@/utils/request';
import { subscriptionApiOption } from './api';
import { Subscription, SubscriptionListParam } from './interface';

// 获取Subscription列表
export const fetchSubscriptionList = (
  param?: SubscriptionListParam,
): Promise<Data<Subscription>> => {
  const config = subscriptionApiOption.subscriptionList;
  config.option.params = param;
  return request(config.url, config.option);
};
