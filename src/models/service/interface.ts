import { PaginatorParam } from '@/utils/request';
import { ReNewEnum, SkuTypeEnum } from '../product/interface';

export enum ServiceStatusEnum {
  /**
   * 1: 生效中
   */
  ON_SERVICE = 1,
  /**
   * 2: 已过期
   */
  OUT_DATE = 2,
}

export enum ServiceStatusV2Enum {
  /**
   * 1: 已过期
   */
  OUT_DATE = 1,
  /**
   * 2: 生效中
   */
  ON_SERVICE = 2,
  /**
   * 3: 待生效
   */
  WAIT_ON_SERVICE = 3,
  /**
   * 4: 已删除
   */
  DELETED = 4,
  /**
   * 5: 未开通
   */
  NO_SERVICE = 5,
}

export enum ServiceUpdatingTypeEnum {
  DEVICE_SN = 1,
  USER_ID = 2,
}

export enum ServicePlanChangeTypeEnum {
  // TRANSFER("Plan变更", new String[] {"设备SN", "用户ID"}),
  TRANSFER = 'TRANSFER',
  // CREATE("服务创建", new String[] {"全部字段"}),
  CREATE = 'CREATE',
  // PLAN_UPDATE("Plan变更", new String[] {"服务Plan"}),
  PLAN_UPDATE = 'PLAN_UPDATE',
  // APPEND_PLAN("Plan变更", new String[] {"服务Plan"}),
  APPEND_PLAN = 'APPEND_PLAN',
  // SERVICE_LINK("服务变更", new String[] {"设备SN"}),
  SERVICE_LINK = 'SERVICE_LINK',
  // SERVICE_UNLINK("服务变更", new String[] {"设备SN"}),
  SERVICE_UNLINK = 'SERVICE_UNLINK',
  // DELETE_SERVICE("服务变更", new String[] {"服务状态"}),
  DELETE_SERVICE = 'DELETE_SERVICE',
  // REFUND("Plan变更", new String[] {"服务Plan"});
  REFUND = 'REFUND',
}

export enum ServicePlanStatusEnum {
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED',
  EXPIRED = 'EXPIRED',
  PENDING = 'PENDING',
  NOT_YET_ACTIVE = 'NOT_YET_ACTIVE',
}

// 列表参数数据
export interface ServiceListParam extends PaginatorParam {
  serviceId?: number;
  userId?: string;
  deviceId?: number;
  serviceStatus?: ServiceStatusEnum;
  sn?: string;
}

export interface ServiceListV2Param extends ServiceListParam {
  deviceType?: string;
}

// 列表数据
export interface Service {
  serviceId: number;
  userId: number;
  deviceId: number;
  shortName: string;
  cloudStorage: SkuTypeEnum;
  cycle: number;
  serviceStatus: ServiceStatusEnum;
  indateStr: string;
  skuId: number;
  sn: string;
  deviceType: string;
  capacity: { type: string; cycleTime: number }[];
}

// 服务管理V2列表数据
export interface ServiceV2 {
  userId: number;
  deviceId: number;
  sn: string;
  serviceId: number;
  serviceStatus: ServiceStatusV2Enum;
  skuId: number;
  deviceType: string;
  zoneId: string;
  servicePlans: ServicePlanInfo[];
}
export interface ServicePlanInfo {
  id: number;
  serviceId: number;
  deviceId: number;
  deviceType: string;
  sn: string;
  status: ServicePlanStatusEnum;
  skuId: number;
  skuName: string;
  skuShortName: string;
  workTime: number;
  workIndate: number;
  zoneId: string;
  createdAt: string;
  subscribe: ReNewEnum;
  workIndateStr: string;
}

export interface ServiceUpdatingHistoryListParam extends PaginatorParam {
  serviceId: number;
}

export interface ServiceUpdatingHistoryListV2Param
  extends ServiceUpdatingHistoryListParam {
  deviceType: string;
}

export interface ServiceUpdatingHistory {
  changeType: ServiceUpdatingTypeEnum;
  before: string;
  after: string;
  operationTimeStr: string;
}

export interface ServiceUpdatingHistoryV2 {
  changeType: ServiceUpdatingTypeEnum;
  id: number;
  userId: number;
  changeDesc: string;
  changeField: string;
  changeBefore: string;
  changeAfter: string;
  operator: string;
  operatorTime: number;
  status: boolean;
}

export interface ServiceTransferParam {
  serviceId: number;
  sn: string;
  userId: number;
}

export interface ServiceTransferV2Param {
  serviceId: number;
  transfereeSn: string;
  userId: number;
  deviceType: string;
}
