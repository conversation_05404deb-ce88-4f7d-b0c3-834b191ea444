import request from '@/utils/request';
import { currencyApiOption } from './api';
import { Currency } from './interface';

// 获取Currency列表
export const fetchCurrencyList = (): Promise<Currency[]> => {
  const config = currencyApiOption.currencyList;
  return request(config.url, config.option);
};

// 获取Currency代码列表
export const fetchCurrencyApiOption = (): Promise<string[]> => {
  const config = currencyApiOption.currencyCodeList;
  return request(config.url, config.option);
};
