import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { spanConfig } from '@/models/common.util';
import { ConnectState } from '@/models/connect';
import { deviceTypeEnum } from '@/models/device/util';
import {
  fetchOperationPackageDeletion,
  fetchOperationPackageList,
  fetchOperationStatusSwtich,
} from '@/models/package/fetch';
import {
  ActTimeEnum,
  OperationPackage,
  OperationPackageListParam,
} from '@/models/package/interface';
import { initOperationPackageListParam } from '@/models/package/util';
import { antdUtils } from '@/utils/antd.util';
import { Paginator, initPaginator } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history, useDispatch, useSelector } from '@umijs/max';
import { But<PERSON>, Popconfirm, Space, Switch, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { OperationPackageTableForm } from '../interface';

const List: React.FC = () => {
  const dispatch = useDispatch();
  const [dataList, setDataList] = useState<OperationPackage[]>([]);
  const [listParam, setListParam] = useState<OperationPackageListParam>(
    initOperationPackageListParam,
  );
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);

  const activityContentInfo:
    | {
        [key: string]: string;
      }
    | undefined = useSelector(
    ({ package: _package }: ConnectState) => _package.activityContentInfo,
  );

  // 获取列表数据
  const requestOperationPackageList = async (
    param: OperationPackageListParam,
  ) => {
    const { items, ...rest } = await fetchOperationPackageList(param);
    setPaginator(rest);
    setDataList(items);
  };

  const requestOperationPackageDeletion = async (record: OperationPackage) => {
    const result = await fetchOperationPackageDeletion(record.id);
    if (result === ApiSuccessEnum.success) {
      message.success(`删除成功`);
      if (dataList.length > 1) {
        setListParam({ ...listParam });
      } else {
        setListParam({
          ...listParam,
          offset:
            listParam.offset - listParam.limit > 0
              ? listParam.offset - listParam.limit
              : 0,
        });
      }
    }
  };

  // 编辑
  const editOperationPackage = (id: number = 0) => {
    history.push(`/service/operation/package/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: OperationPackageTableForm = form?.getFieldsValue();
          const _listParam: OperationPackageListParam = {
            ...initOperationPackageListParam,
            ...formData,
          };
          setListParam({ ..._listParam });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam(initOperationPackageListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const onSaleStatusChanged = async (ev: boolean, record: OperationPackage) => {
    const result = await fetchOperationStatusSwtich(record.id, +ev);
    if (result !== ApiSuccessEnum.success) return;
    message.success(`${ev ? '上架' : '下架'}成功`);
    if (dataList.length > 1) {
      setListParam({ ...listParam });
    } else {
      setListParam({
        ...listParam,
        offset:
          listParam.offset - listParam.limit > 0
            ? listParam.offset - listParam.limit
            : 0,
      });
    }
  };

  const columns: Array<ProColumns<OperationPackage>> = [
    {
      title: '适用设备',
      dataIndex: 'deviceType',
      width: 100,
      valueType: 'select',
      valueEnum: deviceTypeEnum,
    },
    {
      title: '关联Sku',
      dataIndex: 'relationSkuId',
      width: 100,
      search: false,
      render: (_, row) => (
        <RedirectLink
          text={row.relationSkuId || ''}
          linkUrl={`/business/cloudServiceSku`}
          params={{ redirect: 'edit', skuId: row.relationSkuId || 0 }}
        />
      ),
    },
    {
      title: '活动名称',
      dataIndex: 'actName',
      width: 120,
    },
    {
      title: '活动内容',
      dataIndex: 'actContent',
      width: 120,
      valueType: 'select',
      valueEnum: activityContentInfo,
      render: (_, row) =>
        activityContentInfo
          ? activityContentInfo[row.actContent]
          : row.actContent,
    },
    {
      title: '活动属性/次数',
      dataIndex: 'actProp',
      width: 120,
      search: false,
      render: (_, record) => `${record.actProp}/${record.actNum}`,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 100,
      search: false,
    },
    {
      title: '活动时间',
      dataIndex: 'effectDate',
      width: 200,
      search: false,
      render: (_, row) => {
        if (row.actTimeEnum === ActTimeEnum.FOREVER) {
          return '永久有效';
        }
        return `${dayjs(row.effectDate).format(
          'YYYY-MM-DD HH:mm:ss',
        )} ~ ${dayjs(row.inEffectDate).format('YYYY-MM-DD HH:mm:ss')}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'saleStatus',
      valueType: 'select',
      valueEnum: { 1: '上架', 0: '下架' },
      width: 100,
      render: (_, row) => (
        <Switch
          unCheckedChildren="下架"
          checkedChildren="上架"
          checked={!!row.saleStatus}
          onChange={(ev) => onSaleStatusChanged(ev, row)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      search: false,
      render: (_, row) => (
        <Space>
          <Button type="link" onClick={() => editOperationPackage(row.id)}>
            编辑
          </Button>
          {row.saleStatus === StatusEnum.DISABLE ? (
            <Popconfirm
              title="确定删除该行数据么？"
              onConfirm={() => requestOperationPackageDeletion(row)}
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          ) : null}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestOperationPackageList(listParam);
  }, [listParam]);

  useEffect(() => {
    dispatch({ type: 'package/requestActivityContentInfo' });
  }, [dispatch]);

  return (
    <ProTable<OperationPackage>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      options={{
        reload: () => setListParam({ ...listParam }),
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editOperationPackage()}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        ...antdUtils.transferPaginatorToTablePagination(paginator),
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
