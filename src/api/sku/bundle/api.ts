/**
 * 组合SKU管理相关的API接口配置
 */

import { RequestMethod, RequestOption } from '@mantas/request';

export const bundleSkuApi: Record<
  'list' | 'detail' | 'create' | 'update' | 'updateSaleStatus' | 'changeLogs',
  RequestOption
> = {
  // 查询组合SKU列表
  list: {
    url: '/adm/bs/product/sku/bundle/list',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 获取组合SKU详情
  detail: {
    url: '/adm/bs/product/sku/bundle/detail',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 创建组合SKU
  create: {
    url: '/adm/bs/product/sku/bundle/create',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 更新组合SKU
  update: {
    url: '/adm/bs/product/sku/bundle/update',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 批量上下架组合SKU
  updateSaleStatus: {
    url: '/adm/bs/product/sku/bundle/updateSaleStatus',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 查询组合SKU变更记录
  changeLogs: {
    url: '/adm/bs/product/sku/bundle/changeLogs',
    option: {
      method: RequestMethod.Get,
    },
  },
};
