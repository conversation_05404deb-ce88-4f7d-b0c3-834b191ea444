import { SaleStatusEnum, ServiceTimeUnitEnum } from '@/api/sku/bundle/types';
import { CurrencyPrice } from '@/components/CurrencyPriceForm/interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { ProductSkuCapacityTypeEnum } from '@/models/product/interface';
import { UploadFile } from 'antd/lib/upload';

export interface ProductSkuListForm {
  id?: number;
  name?: string;
  shortName?: string;
  aliasName?: string;
  capacities?: ProductSkuCapacityTypeEnum;
  isReNew?: number;
  saleStatus?: SaleStatusEnum;
  deviceType?: string;
  deviceNumber?: number;
  sort?: number;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
}

export interface ProductSkuForm {
  // SKU内容
  name: string;
  simpleName: string;
  aliasName: string;
  sort: number | null;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceType: SuitableDeviceTypeEnum | null;
  deviceNumber: number | null;
  promotionName: string;
  // SKU价格
  currencyPrice: CurrencyPrice;
  isAutoRenew: boolean;
  isFirstPreferential: boolean;
  // preferentialPrice?: number;
  preferentialPrice?: CurrencyPrice;
  // 营销设置
  cornerIcons: UploadFile[];
  // description: string;
}
