import CurrencyPriceForm from '@/components/CurrencyPriceForm';
import SkuSelector from '@/components/SkuSelector';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';
import Uploader from '@/components/Uploader';
import { fetchActPropCreation, fetchActPropList } from '@/models/actProp/fetch';
import { ActProp, ActPropParam } from '@/models/actProp/interface';
import {
  ApiSuccessEnum,
  SelectOption,
  StatusEnum,
} from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { deviceTypeOptions } from '@/models/device/util';
import {
  fetchActivityPackageDetail,
  fetchActivityPackagePriorityValidator,
  fetchActivityPackageSave,
} from '@/models/package/fetch';
import {
  ActTimeEnum,
  ActTypeEnum,
  OperationPackageDetail,
} from '@/models/package/interface';
import { fetchProductSkuDetail } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  SaleStatusEnum,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { initRelationProductSkuListParam } from '@/models/product/util';
import { initPaginatorParam } from '@/utils/request';
import {
  InfoCircleFilled,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Alert,
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
  UploadFile,
  message,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useMemo, useState } from 'react';
import {
  ActPropForm,
  ImageConfig,
  ImageTypeEnum,
  OperationPackageForm,
} from '../interface';
import {
  initImageConfig,
  initialOperationPackageForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';
import ActPropEditor from './ActPropEditor';

const cardStyle: React.CSSProperties = {
  marginBottom: 16,
};

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const urlRestParam = useParams<{ id: string }>();
  const [form] = Form.useForm<OperationPackageForm>();
  const actTime = Form.useWatch('actTime', form);
  const [actPropList, setActPropList] = useState<ActProp[]>([]);
  const [showActPropEditor, setShowActPropEditor] = useState(false);
  const [detail, setDetail] = useState<OperationPackageDetail>();
  const [loading, setLoading] = useState(false);
  const [showSkuSelector, setShowSkuSelector] = useState(false);
  const [associateSkuList, setAssociateSkuList] = useState<ProductSku[]>([]);
  const [associateSkuListParam, setAssociateSkuListParam] =
    useState<RelationProductSkuListParam>(initRelationProductSkuListParam);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };
  const _activityContentOptionsList: SelectOption[] = useSelector(
    ({ package: _package }: ConnectState) => _package.activityContentOptionList,
  );
  const isEditing = useMemo(() => {
    // 返回是否存在urlRestParam.id,同时detail.saleStatus为上架状态
    return !!+(urlRestParam.id || 0);
  }, [urlRestParam, detail]);

  const activityContentOptionsList = useMemo(() => {
    if (!detail) {
      return _activityContentOptionsList.filter(
        (option) => option.value !== ActTypeEnum.EARLY_BIRD_SISTY_DAY,
      );
    }
    return _activityContentOptionsList;
  }, [_activityContentOptionsList, detail]);

  // 缓存活动配图的英文是否被选择
  const [imageConfig, setImageConfig] = useState<ImageConfig>(initImageConfig);

  const isNeedUpdateSku = Form.useWatch('isNeedUpdateSku', form);
  const content = Form.useWatch('content', form);

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const actPropOptions = useMemo(() => {
    let options: SelectOption[] = [];
    if (content === ActTypeEnum.FREE_ACTIVITY) {
      options = actPropList
        .filter((prop) => prop.actNum === 1)
        .map((prop) => ({ value: prop.id, label: prop.actProp }));
    } else {
      options = actPropList.map((prop) => ({
        value: prop.id,
        label: prop.actProp,
      }));
    }
    return options;
  }, [content, actPropList]);

  // 获取活动属性列表
  const requestActPropList = async () => {
    const actPropList: ActProp[] = await fetchActPropList();
    setActPropList(actPropList);
  };

  // 添加活动属性
  const requestActPropAdd = async (actPropFormData: ActPropForm) => {
    const param: ActPropParam = {
      actNum: actPropFormData.number,
      actProp: actPropFormData.name,
    };
    await fetchActPropCreation(param);
    message.success('添加成功');
    requestActPropList();
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const _detail = await fetchActivityPackageDetail(id);
    setDetail(_detail);
    // console.log(_detail.deviceType);
    const formData = transferDetailToFormData(_detail);
    form.setFieldsValue(formData);

    const relationDetail = await fetchProductSkuDetail(_detail.relationSkuId);
    setAssociateSkuList([relationDetail]);
  };

  const onActPropEditorCancel = () => {
    setShowActPropEditor(false);
  };

  const onActPropEditorConfirm = async (actPropForm: ActPropForm) => {
    if (!actPropForm.name) {
      message.warning('请输入活动属性名称！');
      return;
    }
    if (!actPropForm.number) {
      message.warning('请输入活动次数！');
      return;
    }
    try {
      await requestActPropAdd(actPropForm);
      onActPropEditorCancel();
    } catch (error) {
      console.log(error);
    }
  };

  const showProductSelector = () => {
    const formData = form.getFieldsValue();
    const { deviceType } = formData;

    if (!deviceType) {
      message.warning('请先选择适用设备');
      return;
    }
    const _param: RelationProductSkuParam = {
      // isReNew: ReNewEnum.NOT_RENEW,
      uniteDeviceTypes: [deviceType],
      uniteCapacities: [],
      saleStatus: SaleStatusEnum.ON_SALE,
    };
    setAssociateSkuListParam({
      ...initPaginatorParam,
      payload: _param,
    });
    setShowSkuSelector(true);
  };

  const validateImage = async (
    value: UploadFile[],
    type: ImageTypeEnum,
    key: string,
  ) => {
    // console.log('validateImage', value, type, key, content, imageConfig);

    // if (!value || !value.length || !value[0].url) {
    //   return Promise.reject(`请上传图片`);
    // }
    if (imageConfig[type][key] && (!value || !value.length)) {
      return Promise.reject(`请上传图片`);
    }

    return Promise.resolve();
  };

  // 提交form表单
  const submit = async (formData: OperationPackageForm) => {
    if (!associateSkuList || !associateSkuList.length) {
      message.warning('请选择活动sku');
      return;
    }

    if (!formData.actImage.zh_CN || !formData.actImage.zh_CN.length) {
      message.warning('请上传至少一张中文活动配图');
      return;
    }

    // if (
    //   !formData.currencyPrice.priceList ||
    //   !formData.currencyPrice.priceList.length
    // ) {
    //   message.warning('当前地区没有对应的货币种类，请重新选择地区');
    //   return;
    // }

    // setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      associateSkuList,
      actPropList,
      imageConfig,
      +(urlRestParam?.id || 0),
    );
    console.log('submit', formData, _param);
    // return;
    let state = '';
    try {
      if (urlRestParam && urlRestParam.id && +urlRestParam.id) {
        state = '更新';
      } else {
        state = '创建';
      }
      const result = await fetchActivityPackageSave(_param);
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (urlRestParam && urlRestParam.id && +urlRestParam.id) {
      requestDetailById(+urlRestParam.id);
    }
  }, [urlRestParam]);

  useEffect(() => {
    // 额外dva接口请求
    requestActPropList();
  }, []);

  useEffect(() => {
    dispatch({ type: 'package/requestActivityContentInfo' });
  }, [dispatch]);

  useEffect(() => {
    form.validateFields(['relativeSku']);
  }, [associateSkuList]);

  // 活动内容发生变化了
  useEffect(() => {
    switch (content) {
      case ActTypeEnum.FREE_ACTIVITY:
        setImageConfig({
          [ImageTypeEnum.ACT]: { zh_CN: true, en_US: true },
          [ImageTypeEnum.POP]: { zh_CN: true, en_US: true },
        });
        // 设置关联的SKU价格为0;本地化货币开始，无需去自动变化
        // form.setFieldValue('activityPrice', 0);
        break;
      case ActTypeEnum.PROMOTION_ACTIVITY:
        setImageConfig({
          [ImageTypeEnum.ACT]: { zh_CN: true, en_US: false },
          [ImageTypeEnum.POP]: { zh_CN: false, en_US: false },
        });
        break;
    }
  }, [content]);

  return (
    <>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialOperationPackageForm}
      >
        <ProCard title="基础设置" style={cardStyle}>
          <Form.Item
            name="deviceType"
            label="适用设备"
            rules={[{ required: true, message: '请选择适用设备' }]}
          >
            <Select
              options={deviceTypeOptions}
              placeholder="请选择适用设备"
              disabled={isEditing}
              onChange={() => {
                if (form.getFieldValue('priority'))
                  form.validateFields(['priority']);

                setAssociateSkuList([]);
                form.resetFields(['activityName']);
                form.resetFields(['serviceTime']);
                form.resetFields(['serviceTimeUnit']);
                // form.resetFields(['activityPrice']);
              }}
            />
          </Form.Item>
          <Form.Item
            name="activityName"
            label="活动名称"
            rules={[{ required: true, message: '请输入活动名称' }]}
          >
            <Input placeholder="请输入活动名称" />
          </Form.Item>
          <Form.Item
            name="prop"
            label="活动属性"
            style={{ marginBottom: 10 }}
            rules={[{ required: true, message: '请选择活动属性' }]}
            extra={
              <>
                <div>请先选择活动内容再选择活动属性</div>
                <Button
                  type="link"
                  icon={<PlusOutlined />}
                  onClick={() => setShowActPropEditor(true)}
                >
                  添加
                </Button>
              </>
            }
          >
            <Select
              disabled={isEditing || !content}
              placeholder="请选择活动属性"
              options={actPropOptions}
            />
          </Form.Item>
          <Form.Item
            name="content"
            label="活动内容"
            rules={[{ required: true, message: '请选择活动内容' }]}
          >
            <Select
              placeholder="请选择活动内容"
              disabled={isEditing}
              options={activityContentOptionsList}
              // options={[]}
            />
          </Form.Item>
          {content ? (
            <>
              <Form.Item
                label={
                  <>
                    <span className="error-red" style={{ marginRight: 4 }}>
                      *
                    </span>
                    活动配图
                  </>
                }
              >
                <div>
                  <Checkbox
                    disabled
                    checked={imageConfig[ImageTypeEnum.ACT].zh_CN}
                    style={{ marginBottom: 10 }}
                  >
                    中文
                  </Checkbox>
                  <Form.Item
                    name={['actImage', 'zh_CN']}
                    shouldUpdate
                    getValueFromEvent={normFile}
                    valuePropName="fileList"
                    rules={[
                      {
                        validator: (_, value) => {
                          return validateImage(
                            value,
                            ImageTypeEnum.ACT,
                            'zh_CN',
                          );
                        },
                      },
                    ]}
                    extra={
                      <Typography.Text type="secondary">
                        <InfoCircleFilled
                          style={{ color: '#1890ff', marginRight: 8 }}
                        />
                        配图图标尺寸为686px*128px
                      </Typography.Text>
                    }
                  >
                    <Uploader />
                  </Form.Item>
                </div>
                <div>
                  <Checkbox
                    disabled={content !== ActTypeEnum.PROMOTION_ACTIVITY}
                    checked={imageConfig[ImageTypeEnum.ACT].en_US}
                    style={{ marginBottom: 10 }}
                    onChange={({ target: { checked } }) => {
                      setImageConfig({
                        ...imageConfig,
                        [ImageTypeEnum.ACT]: {
                          ...imageConfig[ImageTypeEnum.ACT],
                          en_US: checked,
                        },
                      });
                    }}
                  >
                    英文
                  </Checkbox>
                  {imageConfig[ImageTypeEnum.ACT].en_US ? (
                    <Form.Item
                      name={['actImage', 'en_US']}
                      shouldUpdate
                      getValueFromEvent={normFile}
                      valuePropName="fileList"
                      rules={[
                        {
                          validator: (_, value) => {
                            return validateImage(
                              value,
                              ImageTypeEnum.ACT,
                              'en_US',
                            );
                          },
                        },
                      ]}
                      extra={
                        <Typography.Text type="secondary">
                          <InfoCircleFilled
                            style={{ color: '#1890ff', marginRight: 8 }}
                          />
                          配图图标尺寸为686px*128px
                        </Typography.Text>
                      }
                    >
                      <Uploader />
                    </Form.Item>
                  ) : null}
                </div>
              </Form.Item>
              {content !== ActTypeEnum.PROMOTION_ACTIVITY ? (
                <Form.Item
                  label={
                    <>
                      <span className="error-red" style={{ marginRight: 4 }}>
                        *
                      </span>
                      弹框配图
                    </>
                  }
                >
                  <div>
                    <Checkbox
                      disabled
                      checked={imageConfig[ImageTypeEnum.POP].zh_CN}
                      style={{ marginBottom: 10 }}
                    >
                      中文
                    </Checkbox>
                    {imageConfig[ImageTypeEnum.POP].zh_CN ? (
                      <Form.Item
                        name={['popImage', 'zh_CN']}
                        shouldUpdate
                        getValueFromEvent={normFile}
                        valuePropName="fileList"
                        rules={[
                          {
                            validator: (_, value) => {
                              return validateImage(
                                value,
                                ImageTypeEnum.POP,
                                'zh_CN',
                              );
                            },
                          },
                        ]}
                        extra={
                          <Typography.Text type="secondary">
                            <InfoCircleFilled
                              style={{ color: '#1890ff', marginRight: 8 }}
                            />
                            配图图标尺寸为686px*128px
                          </Typography.Text>
                        }
                      >
                        <Uploader />
                      </Form.Item>
                    ) : null}
                  </div>
                  <div>
                    <Checkbox
                      disabled
                      checked={imageConfig[ImageTypeEnum.POP].en_US}
                      style={{ marginBottom: 10 }}
                    >
                      英文
                    </Checkbox>
                    {imageConfig[ImageTypeEnum.POP].en_US ? (
                      <Form.Item
                        name={['popImage', 'en_US']}
                        shouldUpdate
                        getValueFromEvent={normFile}
                        valuePropName="fileList"
                        extra={
                          <Typography.Text type="secondary">
                            <InfoCircleFilled
                              style={{ color: '#1890ff', marginRight: 8 }}
                            />
                            配图图标尺寸为686px*128px
                          </Typography.Text>
                        }
                        rules={[
                          {
                            validator: (_, value) => {
                              return validateImage(
                                value,
                                ImageTypeEnum.POP,
                                'en_US',
                              );
                            },
                          },
                        ]}
                      >
                        <Uploader />
                      </Form.Item>
                    ) : null}
                  </div>
                </Form.Item>
              ) : null}
            </>
          ) : null}

          <Form.Item
            name="priority"
            label="优先级"
            tooltip={{
              icon: <QuestionCircleOutlined />,
              title:
                '针对所有活动（活动时间交叉时），满足条件的设备仅能看到优先级最高的',
              trigger: 'click',
            }}
            rules={[
              { required: true, message: '请输入优先级（请输入大于0的整数）' },
              {
                validator: async (_, value) => {
                  const _deviceType = form.getFieldValue('deviceType');
                  if (!_deviceType) {
                    form.validateFields(['deviceType']);
                    return Promise.reject('请先设置适用设备');
                  }
                  if (value === detail?.priority) {
                    return Promise.resolve();
                  }

                  const errorMessage = '已存在该优先级的值';
                  try {
                    const result = await fetchActivityPackagePriorityValidator({
                      priority: value,
                      deviceType: _deviceType,
                    });
                    if (result === ApiSuccessEnum.success) {
                      return Promise.resolve();
                    } else {
                      return Promise.reject(errorMessage);
                    }
                  } catch (error) {
                    return Promise.reject(errorMessage);
                  }
                },
              },
            ]}
            extra={
              <Alert message="数字越大，优先级越高" type="info" showIcon />
            }
          >
            <InputNumber
              min={1}
              placeholder="请输入优先级（请输入大于0的整数）"
              parser={(value) => (value ? parseInt(value, 10) : 0)}
              formatter={(value) => (value ? value.toString() : '')}
              wheel={false}
            />
          </Form.Item>
          <Form.Item
            label={
              <>
                <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
                活动时间
              </>
            }
          >
            <Space align="baseline">
              <Form.Item
                name="actTime"
                rules={[{ required: true, message: '请选择活动的时间类型' }]}
                extra={
                  <div style={{ marginTop: 16 }}>
                    {actTime === ActTimeEnum.CUSTOM ? (
                      <Form.Item
                        name="times"
                        rules={[
                          {
                            required: true,
                            message: '请选择活动的开始结束时间',
                          },
                        ]}
                        style={{ marginBottom: 0 }}
                      >
                        <DatePicker.RangePicker
                          // style={{ width: '100%' }}
                          placeholder={['开始时间', '结束时间']}
                          showTime
                        />
                      </Form.Item>
                    ) : null}
                  </div>
                }
              >
                <Radio.Group>
                  <Radio value={ActTimeEnum.CUSTOM}>自定义时间段</Radio>
                  <Radio value={ActTimeEnum.FOREVER}>永久有效</Radio>
                </Radio.Group>
              </Form.Item>
              <Tooltip title="针对所有活动（活动时间交叉时），满足条件的设备仅能看到优先级最高的">
                <QuestionCircleOutlined
                  style={{
                    marginLeft: 16,
                    cursor: 'pointer',
                    color: '#000',
                    fontSize: 16,
                  }}
                />
              </Tooltip>
            </Space>
          </Form.Item>
        </ProCard>

        <ProCard title="活动套餐" style={cardStyle}>
          <Form.Item
            label={
              <>
                <span className="error-red" style={{ marginRight: 4 }}>
                  *
                </span>
                活动sku
              </>
            }
            rules={[
              {
                validator: async () => {
                  if (!associateSkuList || !associateSkuList.length) {
                    return Promise.reject(new Error('请选择一个云存sku'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Button
              icon={<PlusOutlined />}
              type="link"
              htmlType="button"
              onClick={showProductSelector}
              style={{ marginBottom: 16 }}
            >
              去选择sku
            </Button>
            {associateSkuList && associateSkuList.length ? (
              <Table<ProductSku>
                rowKey="id"
                columns={(
                  associateSkuTableColumns as ColumnsType<ProductSku>
                ).concat([
                  {
                    title: '状态',
                    dataIndex: 'saleStatus',
                    fixed: 'right',
                    width: 100,
                    render: (_, row) =>
                      row.saleStatus ? (
                        <p style={{ color: '#3199F5' }}>上架</p>
                      ) : (
                        '下架'
                      ),
                  },
                ])}
                scroll={{
                  x: associateSkuTableColumns
                    .filter((col) => col.dataIndex === 'action')
                    .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
                }}
                dataSource={associateSkuList}
                pagination={false}
              />
            ) : null}
          </Form.Item>
          <Form.Item
            name="isNeedUpdateSku"
            label="是否更改SKU"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio value={StatusEnum.ENABLE}>是</Radio>
              <Tooltip
                title="确定不更新SKU吗？"
                open={isNeedUpdateSku === StatusEnum.DISABLE}
                placement="right"
              >
                <Radio value={StatusEnum.DISABLE}>否</Radio>
              </Tooltip>
            </Radio.Group>
          </Form.Item>
          {isNeedUpdateSku === StatusEnum.ENABLE ? (
            <>
              <Form.Item
                label="活动sku名称"
                name="activitySkuName"
                rules={[{ required: true, message: '请输入活动sku名称' }]}
              >
                <Input
                  placeholder="请输入活动sku名称"
                  maxLength={15}
                  showCount
                />
              </Form.Item>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="服务时长"
                    name="serviceTime"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 14 }}
                    rules={[
                      {
                        required: true,
                        message: '请输入服务时长(大于0的整数)',
                      },
                    ]}
                  >
                    <InputNumber
                      min={1}
                      placeholder="请输入服务时长(大于0的整数)"
                      wheel={false}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="serviceTimeUnit"
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio
                        key={ServiceTimeUnitEnum.MONTH}
                        value={ServiceTimeUnitEnum.MONTH}
                      >
                        月
                      </Radio>
                      <Radio
                        key={ServiceTimeUnitEnum.DAY}
                        value={ServiceTimeUnitEnum.DAY}
                      >
                        天
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="活动价格(元)" name="currencyPrice">
                <CurrencyPriceForm formItems={['price']} />
              </Form.Item>

              <Form.Item
                name="activityCornerIcons"
                label="活动角标"
                shouldUpdate
                getValueFromEvent={normFile}
                valuePropName="fileList"
                extra={
                  <Typography.Text type="secondary">
                    <InfoCircleFilled
                      style={{ color: '#1890ff', marginRight: 8 }}
                    />
                    活动sku的引流图标，高度为30px或其倍数
                  </Typography.Text>
                }
              >
                <Uploader />
              </Form.Item>
            </>
          ) : null}
        </ProCard>

        <ProCard style={{ ...cardStyle, textAlign: 'center' }}>
          <Form.Item noStyle>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button
              type="default"
              htmlType="button"
              onClick={() => history.back()}
            >
              取消
            </Button>
          </Form.Item>
        </ProCard>
      </Form>
      {showSkuSelector ? (
        <SkuSelector
          param={associateSkuListParam}
          open
          onOk={(ev) => setAssociateSkuList([ev])}
          onCancel={() => setShowSkuSelector(false)}
        />
      ) : null}

      {showActPropEditor ? (
        <ActPropEditor
          onConfirm={onActPropEditorConfirm}
          onCancel={onActPropEditorCancel}
        />
      ) : null}
    </>
  );
};

export default Edit;
