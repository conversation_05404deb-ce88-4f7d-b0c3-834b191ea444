import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const VirtualCard: React.FC = () => {
  const location = useLocation();
  const [title] = useState(
    `虚拟${
      location.pathname.includes('virtual-card/code') ? '卡号' : '卡券'
    }管理`,
  );
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    // const _breadcrumbInfoList = global.getBreadcrumbInfo(breadcrumbInfo, location);
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      {
        ...breadcrumbInfo,
        breadcrumbName: `虚拟${
          location.pathname.includes('virtual-card/code') ? '卡号' : '卡券'
        }`,
      },
      '',
      location,
    );

    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.breadcrumbName}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default VirtualCard;
