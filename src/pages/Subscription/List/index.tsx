import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchCancelSubscription } from '@/models/refund/fetch';
import { fetchSubscriptionList } from '@/models/subscription/fetch';
import {
  Subscription,
  SubscriptionListParam,
  SubscriptionStateEnum,
} from '@/models/subscription/interface';
import {
  initSubscriptionListParam,
  subscriptionPeriodTypeName,
  subscriptionStateName,
} from '@/models/subscription/util';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Modal, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { forIn } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { SubscriptionTableForm, SubscriptionUrlParam } from '../interface';
import { transformUrlParamToListParam } from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<SubscriptionUrlParam>();
  const formRef = useRef<ProFormInstance<SubscriptionTableForm>>();
  const [dataList, setDataList] = useState<Subscription[]>([]);
  const [listParam, setListParam] = useState<SubscriptionListParam>();
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);

  // 获取列表数据
  const requestSubscriptionList = async (
    param: SubscriptionListParam = initSubscriptionListParam,
  ) => {
    const { items, ...rest } = await fetchSubscriptionList(param);
    setPaginator(rest);
    setDataList(items);
  };

  // 取消订阅
  const cancelSubscription = async (record: Subscription) => {
    Modal.confirm({
      content: '提交后，将取消当前套餐的订阅，是否确认？',
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        const { subscriptionNo } = record;
        try {
          await fetchCancelSubscription(undefined, subscriptionNo);
          message.success('已成功解约');
          setListParam({ ...(listParam || initSubscriptionListParam) });
        } catch (error) {
          console.log(error);
        }
      },
    });
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue();
          const _urlParam: SubscriptionUrlParam = {};
          console.log(formData);

          forIn(formData, (value, key) => {
            if (value) {
              if (key === 'subscribedTime') {
                _urlParam.startTime = dayjs(value[0]).startOf('d').valueOf();
                _urlParam.endTime = dayjs(value[1]).endOf('d').valueOf();
              } else {
                _urlParam[key] = value;
              }
            }
          });

          console.log(_urlParam);

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/subscription`,
              param: _urlParam,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/subscription`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (!listParam || pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<Subscription>> = [
    {
      title: '订阅号',
      dataIndex: 'subscriptionNo',
      width: 150,
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      width: 100,
      render: (_, row) => (
        <RedirectLink
          text={row.userId}
          linkUrl={`/user/users`}
          params={{
            username: row.userId,
          }}
        />
      ),
    },
    {
      title: '支付平台',
      dataIndex: 'platform',
      search: false,
      width: 100,
      // valueEnum: payTypeObj,
      // render: (_, row) => payTypeObj[row.platform],
    },
    {
      title: '渠道侧订阅号',
      dataIndex: 'agreementNo',
      search: false,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueEnum: subscriptionStateName,
      width: 120,
    },
    {
      title: '订阅周期',
      dataIndex: 'periodNum',
      width: 100,
      search: false,
    },
    {
      title: '周期类型',
      dataIndex: 'periodType',
      valueEnum: subscriptionPeriodTypeName,
      width: 120,
      search: false,
    },
    {
      title: '实付金额',
      dataIndex: 'amount',
      search: false,
      width: 150,
    },
    {
      title: '货币名称',
      dataIndex: 'currency',
      search: false,
      // valueEnum: currencyCodeObj,
      // renderFormItem: (_, { defaultRender, ...rest }) => (
      //   <Select {...rest} placeholder="请选择" allowClear showSearch />
      // ),
      width: 100,
    },
    {
      title: 'SKU ID',
      dataIndex: 'skuId',
      width: 120,
      search: false,
    },
    {
      title: 'SKU名称',
      dataIndex: 'skuName',
      width: 150,
      search: false,
    },
    {
      title: '设备ID',
      dataIndex: 'deviceId',
      width: 120,
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      valueEnum: { d4sh: 'D4SH', d4h: 'D4H', t5: 'T5', t6: 'T6', t7: 'T7' },
      valueType: 'select',
      width: 120,
    },
    {
      title: '签约时间',
      dataIndex: 'subscribedTime',
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
      valueType: 'dateRange',
      width: 180,
    },
    {
      title: '支付失败原因',
      dataIndex: 'lastDeductErrorMsg',
      width: 150,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      fixed: 'right',
      render: (_, row) =>
        row.state === SubscriptionStateEnum.ACTIVITY ? (
          <Button danger onClick={() => cancelSubscription(row)}>
            取消订阅
          </Button>
        ) : null,
    },
  ];

  useEffect(() => {
    const formData: SubscriptionTableForm = {
      subscriptionNo: urlParam.subscriptionNo,
      userId: urlParam.userId,
      deviceId: urlParam.deviceId,
      state: urlParam.state,
      deviceType: urlParam.deviceType,
    };
    // 获取开始结束时间
    if (urlParam.startTime && urlParam.endTime) {
      const startTimes: Dayjs[] = [];
      startTimes.push(dayjs(+urlParam.startTime));
      startTimes.push(dayjs(+urlParam.endTime));
      formData.subscribedTime = startTimes;
    }

    console.log(formData);

    // 获取formRef的current属性
    const form = formRef.current;
    // 将formData中的值赋给form
    form?.setFieldsValue(formData);

    // 将formData中的值赋给_listParam
    const _listParam = transformUrlParamToListParam(urlParam);
    // 设置listP
    setListParam(_listParam);
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestSubscriptionList(listParam);
  }, [listParam]);

  return (
    <ProTable<Subscription>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        pageSize: paginator.limit,
        total: paginator.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
