import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const actPropApiOption: Record<
  'actPropList' | 'actPropCreation',
  RequestOption
> = {
  // 获取ActProp列表
  actPropList: {
    url: '/adm/bs/act/prop/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 创建ActProp
  actPropCreation: {
    url: '/adm/bs/act/prop',
    option: {
      ...defaultOptions,
      headers: {
        ...defaultOptions.headers,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      requestType: 'form',
      method: RequestMethod.Post,
    },
  },
};
