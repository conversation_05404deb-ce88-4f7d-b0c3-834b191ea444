import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const centerApiOption: Record<
  | 'deviceList'
  | 'linkHistory'
  | 'setLogLevel'
  | 'restartDevice'
  | 'bindDevice'
  | 'unbindDevice'
  | 'deviceSnList'
  | 'deleteDeviceSn'
  | 'deviceSnCreation'
  | 'deviceModuleList'
  | 'deviceModuleEdition'
  | 'deviceModuleDeletion',
  RequestOption
> = {
  // 设备列表
  deviceList: {
    url: '/adm/center/:device/devices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 绑定历史
  linkHistory: {
    url: '/adm/center/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设置log等级
  setLogLevel: {
    url: '/adm/center/:device/log_level',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 重启设备
  restartDevice: {
    url: '/adm/center/:device/reboot',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 解绑设备
  bindDevice: {
    url: '/adm/center/:device/link',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 绑定设备
  unbindDevice: {
    url: '/adm/center/:device/unlink',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备sn列表
  deviceSnList: {
    url: '/adm/center/:device/sn_list',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 删除设备SN号
  deleteDeviceSn: {
    url: '/adm/center/:device/sn_remove',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 创建SN号
  deviceSnCreation: {
    url: '/adm/center/:device/sn_save',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },

  // 获取设备模块列表数据
  deviceModuleList: {
    url: '/adm/center/:device/modules',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  deviceModuleEdition: {
    url: '/adm/center/:device/module_save',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  deviceModuleDeletion: {
    url: '/adm/center/:device/module_remove',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
};
