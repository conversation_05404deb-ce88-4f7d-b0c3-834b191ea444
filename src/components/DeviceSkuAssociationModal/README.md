# DeviceSkuAssociationModal 设备类型关联SKU弹框

## 功能说明

设备类型关联SKU弹框组件，用于展示和选择适合特定设备类型的SKU，支持单选SKU并完成关联操作。

## 主要特性

- ✅ **Modal状态管理**: 支持显示/隐藏状态管理
- ✅ **SKU列表获取**: 自动获取适合指定设备类型的SKU列表
- ✅ **搜索功能**: 支持通过SKU ID、名称、别名进行搜索
- ✅ **单选模式**: 支持单选SKU进行关联
- ✅ **分页支持**: 支持分页展示大量SKU数据
- ✅ **关联状态更新**: 确认后更新对应设备类型的关联状态
- ✅ **TypeScript支持**: 完整的TypeScript类型定义

## 组件结构

```
DeviceSkuAssociationModal/
├── DeviceSkuAssociationModal.tsx  # 主组件
├── useDeviceSkuAssociation.ts     # 状态管理Hook
├── util.tsx                       # 表格列定义工具函数
├── example.tsx                    # 使用示例
├── index.ts                       # 导出文件
└── README.md                      # 说明文档
```

## 基本使用

### 1. 导入组件和Hook

```tsx
import React from 'react';
import { Button, message } from 'antd';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { DeviceSkuAssociationModal, useDeviceSkuAssociation } from '@/components/DeviceSkuAssociationModal';
```

### 2. 使用Hook管理状态

```tsx
const MyComponent: React.FC = () => {
  const {
    visible,
    currentDeviceType,
    relationParam,
    showModal,
    hideModal,
    handleConfirm,
  } = useDeviceSkuAssociation((deviceType, sku) => {
    // 关联成功回调
    message.success(`设备类型 ${deviceType} 成功关联SKU: ${sku.name}`);
    // 这里可以调用API保存关联关系
    console.log('关联成功:', { deviceType, sku });
  });

  // 显示Modal
  const handleShowModal = () => {
    showModal(SuitableDeviceTypeEnum.D4sh);
  };

  return (
    <div>
      <Button type="primary" onClick={handleShowModal}>
        关联D4sh设备SKU
      </Button>
      
      {currentDeviceType && (
        <DeviceSkuAssociationModal
          visible={visible}
          deviceType={currentDeviceType}
          relationParam={relationParam}
          onConfirm={handleConfirm}
          onCancel={hideModal}
        />
      )}
    </div>
  );
};
```

### 3. 自定义查询参数

```tsx
const handleShowModalWithCustomParam = () => {
  const customParam = {
    limit: 20,
    offset: 0,
    payload: {
      uniteDeviceTypes: [SuitableDeviceTypeEnum.D4sh],
      uniteCapacities: [],
      saleStatus: SaleStatusEnum.ON_SALE, // 只显示在售的SKU
      level: 1, // 只显示特定等级的SKU
    },
  };
  
  showModal(SuitableDeviceTypeEnum.D4sh, customParam);
};
```

## API 文档

### DeviceSkuAssociationModal Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| visible | boolean | ✅ | - | Modal是否可见 |
| deviceType | SuitableDeviceTypeEnum | ✅ | - | 设备类型 |
| relationParam | RelationProductSkuListParam | ❌ | - | 关联查询参数 |
| onConfirm | (deviceType, sku) => void | ✅ | - | 确认选择SKU的回调 |
| onCancel | () => void | ✅ | - | 取消的回调 |
| title | string | ❌ | '关联SKU' | Modal标题 |

### useDeviceSkuAssociation Hook

#### 参数
- `onAssociate?: (deviceType: SuitableDeviceTypeEnum, sku: ProductSku) => void` - 关联成功回调

#### 返回值
| 属性 | 类型 | 说明 |
|------|------|------|
| visible | boolean | Modal是否可见 |
| currentDeviceType | SuitableDeviceTypeEnum \| null | 当前设备类型 |
| relationParam | RelationProductSkuListParam \| undefined | 关联参数 |
| showModal | (deviceType, param?) => void | 显示Modal |
| hideModal | () => void | 隐藏Modal |
| handleConfirm | (deviceType, sku, onSuccess?) => void | 处理确认关联 |

## 支持的设备类型

- `D4sh` - D4sh设备
- `D4h` - D4h设备  
- `T5` - T5设备
- `T6` - T6设备
- `T7` - T7设备

## 功能特性详解

### 1. 自动筛选SKU
组件会根据传入的设备类型自动筛选适合该设备类型的SKU列表。

### 2. 搜索功能
- SKU ID搜索
- SKU名称模糊搜索
- SKU别名模糊搜索

### 3. 表格展示信息
- SKU ID
- SKU名称和别名
- SKU能力（带标签展示）
- 适用设备类型
- 服务时长
- 价格信息
- 自动续费状态
- 在售状态
- 关联SKU信息
- 创建时间

### 4. 分页功能
- 支持分页查看
- 可调整每页显示数量
- 支持快速跳转

## 最佳实践

1. **状态管理**: 使用提供的Hook进行状态管理，避免手动管理复杂状态
2. **错误处理**: 在关联成功回调中添加适当的错误处理逻辑
3. **加载状态**: 组件内置了加载状态，无需额外处理
4. **类型安全**: 充分利用TypeScript类型定义，确保类型安全

## 注意事项

1. 组件依赖于现有的产品和设备相关的API接口
2. 需要确保用户有相应的权限访问SKU数据
3. Modal会在关闭时自动清理状态，支持重复使用
4. 建议在关联成功回调中处理具体的业务逻辑，如保存关联关系到后端

## 更新日志

- v1.0.0: 初始版本，实现基本的设备类型关联SKU功能
