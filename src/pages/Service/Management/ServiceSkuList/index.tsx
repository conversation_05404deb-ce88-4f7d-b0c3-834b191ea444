import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { ApiSuccessEnum } from '@/models/common.interface';
import { ReNewEnum } from '@/models/product/interface';
import {
  fetchCancelSubscription,
  fetchRefundDetail,
  fetchRefundRetrieveBalanceDetail,
} from '@/models/refund/fetch';
import {
  AvailableRefundInfo,
  RefundDetail as RefundDetaiInfo,
} from '@/models/refund/interface';
import {
  fetchServiceSkuList,
  fetchUpdatingSkuExpirationDate,
} from '@/models/sku/fetch';
import { ServiceSku, UpdatingSkuExpirationParam } from '@/models/sku/interface';
import { serviceSkuStatusNameObj } from '@/models/sku/util';
import { DownOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  MenuProps,
  Modal,
  Space,
  message,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';
import Refund from './Refund';
import RefundDetail from './RefundDetail';
import styles from './index.less';

interface Props {
  deviceType: string;
  serviceId: number;
  showModal: boolean;
  onRefundSuccess?: () => void;
  onClose?: (shouldRefresh?: boolean) => void;
}

interface FormData {
  expirationDate: Dayjs | null;
  reason: string;
}

const initFormData: FormData = {
  expirationDate: null,
  reason: '',
};

const ServiceSkuList: React.FC<Props> = ({
  serviceId,
  showModal,
  onClose,
  deviceType,
  onRefundSuccess,
}: Props) => {
  const [dataList, setDataList] = useState<ServiceSku[]>([]);
  const [originDataList, setOriginDataList] = useState<ServiceSku[]>([]);
  const [showEdit, setShowEdit] = useState(false);
  const [selectedServiceSku, setSelectedServiceSku] = useState<ServiceSku>();
  const [form] = Form.useForm<FormData>();
  const [loading, setLoading] = useState(false);
  const [selectedData, setSelectedData] = useState<ServiceSku>();
  // 可退款的信息
  const [availableRefundInfo, setAvailableRefundInfo] =
    useState<AvailableRefundInfo>();
  const [selectedOrderRefundDetail, setSelectedOrderRefundDetail] =
    useState<RefundDetaiInfo>();

  // 获取列表数据
  const requestServiceSkuList = async (_serviceId: number) => {
    let list = await fetchServiceSkuList(_serviceId, deviceType);
    setDataList(list);
    setOriginDataList(list);
  };

  // 修改效期
  const editServiceSku = (row: ServiceSku) => {
    setShowEdit(true);
    setSelectedServiceSku(row);
    form.setFieldsValue({ expirationDate: dayjs(row.indateStr) });
  };

  // 退款
  const refundService = async (row: ServiceSku) => {
    const { orderId } = row;
    const result = await fetchRefundRetrieveBalanceDetail(orderId);
    console.log(result);
    if (!result.availableRefundAmount) {
      message.warning('当前订单无法退款!');
      return;
    }
    setSelectedData(row);
    setAvailableRefundInfo(result);
  };

  // 退款详情
  const refundDetail = async (row: ServiceSku) => {
    const { orderId } = row;
    const result = await fetchRefundDetail(orderId);
    if (!result.refundList || !result.refundList.length) {
      message.warning('当前订单无退款记录!');
      return;
    }
    setSelectedOrderRefundDetail(result);
  };

  // 取消签约
  const cancelSubscription = async (row: ServiceSku) => {
    Modal.confirm({
      content: '提交后，将取消当前套餐的订阅，是否确认？',
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        const { orderId } = row;
        try {
          await fetchCancelSubscription(orderId);
          message.success('已成功解约');
          requestServiceSkuList(serviceId);
        } catch (error) {
          console.log(error);
        }
      },
    });
  };

  const filterDataList = (searchValue: string) => {
    if (!searchValue) setDataList([...originDataList]);

    const resultDataList = originDataList.filter((item) => {
      let rowStr = '';
      let key: keyof ServiceSku;
      for (key in item) {
        if (item[key]) {
          rowStr += item[key];
        }
      }
      return rowStr.includes(searchValue);
    });
    setDataList([...resultDataList]);
  };

  const holdTheSameDateTime = (currentTime: Dayjs, timeZone: string): Dayjs => {
    // console.log(currentTime.utcOffset(), dayjs().tz(timeZone).utcOffset());
    const offset = currentTime.utcOffset() - dayjs().tz(timeZone).utcOffset();
    console.log(
      currentTime.utcOffset(),
      dayjs().tz(timeZone).utcOffset(),
      offset,
    );
    const targetDateTime = currentTime.add(offset, 'minutes').tz(timeZone);
    return targetDateTime;
  };

  const submit = (ev: FormData) => {
    if (!selectedServiceSku || !ev.expirationDate) return;
    // const expirationDate = ev.expirationDate?.tz('PRC');

    const targetDateTime = holdTheSameDateTime(
      ev.expirationDate,
      selectedServiceSku.zoneId,
    );
    // console.log(ev.expirationDate, ev.expirationDate.valueOf());
    const formatedExpirationDate = targetDateTime.format('YYYY-MM-DD');
    Modal.confirm({
      title: '修改确认',
      content: `修改后，服务将于${formatedExpirationDate} 23:59:59到期，是否确认修改？`,
      onOk: async () => {
        setLoading(true);
        try {
          // 直接使用新接口调用，不判断是否是d4sh
          const result = await fetchUpdatingSkuExpirationDate(
            {
              id: selectedServiceSku.id,
              // 使用设备时区的时间戳
              date: targetDateTime.endOf('d').unix(),
            } as UpdatingSkuExpirationParam,
            deviceType,
          );
          // if (deviceType.toLowerCase() === 'd4sh') {
          //   result =
          // } else {
          //   result = await fetchUpdatingSkuExpirationDate(
          //     {
          //       id: selectedServiceSku.id,
          //       // 使用设备时区的时间戳
          //       date: targetDateTime.endOf('d').unix(),
          //     },
          //     deviceType,
          //   );
          // }
          if (result === ApiSuccessEnum.success) {
            message.success('修改成功！');
            requestServiceSkuList(serviceId);
            if (isFunction(onClose)) setShowEdit(false);
          }
        } catch (e) {
          console.log(e);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const columns: Array<ProColumns<ServiceSku>> = [
    {
      title: 'Plan ID',
      dataIndex: 'skuId',
      width: 100,
      render: (text, row) => (
        <RedirectLink
          text={text}
          linkUrl="/business/cloudServiceSku"
          params={{ id: row.skuId }}
        />
      ),
    },
    {
      title: 'Plan名称',
      dataIndex: 'shortName',
      width: 150,
    },
    {
      title: '订单编号',
      dataIndex: 'orderId',
      width: 180,
      render: (text, row) => (
        <RedirectLink
          text={text}
          linkUrl="/business/order"
          params={{ orderId: row.orderId }}
        />
      ),
    },
    {
      title: '服务状态',
      dataIndex: 'skuStatus',
      render: (_, row) => serviceSkuStatusNameObj[row.skuStatus],
      width: 120,
    },
    {
      title: '到期时间',
      dataIndex: 'indateStr',
      width: 120,
    },
    {
      title: 'Plan能力',
      dataIndex: 'capacity',
      width: 180,
      render: (_, row) => {
        let value: React.ReactNode = '-';
        try {
          const capacity: { type: string; cycleTime: number }[] = row.capacity
            ? JSON.parse(row.capacity)
            : row.capacity;
          value = '';
          (capacity || []).forEach((cap) => {
            value = (
              <>
                {value}
                <div>
                  {cap.type} {cap.cycleTime}天循环
                </div>
              </>
            );
          });
          return value;
        } catch (error) {
          console.log(error);
        }
      },
    },
    {
      title: '服务时长',
      dataIndex: 'serviceDuration',
      width: 120,
    },
    {
      title: '自动续费',
      dataIndex: 'skuId',
      render: (_, row) => (row.autoPay ? '是' : '否'),
      width: 120,
    },
    {
      title: '时区',
      dataIndex: 'timeZone',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 120,
      render: (_, row) => {
        const items: MenuProps['items'] = [
          {
            key: 'editExpiration',
            label: (
              <a onClick={() => editServiceSku(row)} className={styles.button}>
                修改效期
              </a>
            ),
          },
          {
            key: 'refund',
            label: (
              <a onClick={() => refundService(row)} className={styles.button}>
                退款
              </a>
            ),
          },
          {
            key: 'refundDetail',
            label: (
              <a onClick={() => refundDetail(row)} className={styles.button}>
                退款详情
              </a>
            ),
          },
        ];
        if (row.autoPay === ReNewEnum.RENEW) {
          items.push({
            key: 'unsign',
            label: (
              <a
                onClick={() => cancelSubscription(row)}
                className={styles.button}
              >
                取消订阅
              </a>
            ),
          });
        }
        return (
          <Dropdown menu={{ items }}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                更多操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  useEffect(() => {
    requestServiceSkuList(serviceId);
  }, []);

  return (
    <>
      <Modal
        key="table"
        width={950}
        destroyOnClose
        open={showModal}
        footer={null}
        onCancel={() => onClose?.()}
      >
        <ProTable<ServiceSku>
          dataSource={dataList}
          columns={columns}
          defaultSize="small"
          rowKey="id"
          search={false}
          scroll={{
            x: columns
              .filter((col) => col.dataIndex !== 'action')
              .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
          }}
          options={{ reload: () => requestServiceSkuList(serviceId) }}
          toolbar={{
            search: {
              onSearch: filterDataList,
            },
          }}
        />
      </Modal>
      <Modal
        width={500}
        key="form"
        destroyOnClose
        open={showEdit}
        title="编辑"
        footer={null}
        onCancel={() => setShowEdit(false)}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={form}
          initialValues={initFormData}
          onFinish={submit}
        >
          <Form.Item
            name="expirationDate"
            label="到期时间"
            rules={[{ required: true, message: '请选择到期时间' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          {/* <Form.Item
            name="reason"
            label="修改原因"
            rules={[{ required: true, message: '请输入修改原因' }]}
          >
            <Input placeholder="请输入修改原因" />
          </Form.Item> */}
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Space>
              <Button htmlType="submit" type="primary" loading={loading}>
                提交
              </Button>
              <Button
                type="default"
                htmlType="button"
                onClick={() => {
                  form.resetFields();
                  setShowEdit(false);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {selectedData && availableRefundInfo ? (
        <Refund
          serviceSku={selectedData}
          availableRefundInfo={availableRefundInfo}
          onSubmit={() => {
            setAvailableRefundInfo(undefined);
            requestServiceSkuList(serviceId);
            onRefundSuccess?.();
          }}
          onClose={() => setAvailableRefundInfo(undefined)}
        />
      ) : null}
      {selectedOrderRefundDetail ? (
        <RefundDetail
          refundDetail={selectedOrderRefundDetail}
          onClose={() => setSelectedOrderRefundDetail(undefined)}
        ></RefundDetail>
      ) : null}
    </>
  );
};

export default ServiceSkuList;
