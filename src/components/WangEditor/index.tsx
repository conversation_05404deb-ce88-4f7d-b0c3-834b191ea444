import { QiniuOssOption, qiniuOssService } from '@/services/qiniuOss.service';
import {
  DomEditor,
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
} from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css';
import React, { useEffect, useState } from 'react';

interface Props {
  value?: string;
  onChange?: (ev: string) => void;
  placeholder?: string;
}

// url, // 图片 src ，必须
// alt, // 图片描述文字，非必须
// href // 图片的链接，非必须
type InsertFnType = (url: string, alt: string, href: string) => void;

const WangEditor: React.FC<Props> = ({
  value,
  onChange,
  placeholder,
}: Props) => {
  // 工具栏配置
  const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: ['emotion'],
  };
  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    // TS 语法
    // const editorConfig = {                         // JS 语法
    placeholder: placeholder || '请输入内容...',
  };

  // 上传图片配置
  editorConfig.MENU_CONF = {
    uploadImage: {
      async customUpload(file: File, insertFn: InsertFnType) {
        const qiniuOssOption: QiniuOssOption = {
          file,
          onError: (err) => {
            console.log(err);
          },
          onSuccess: (body) => {
            insertFn(body.url, body.url, body.url);
          },
        };
        qiniuOssService.run(qiniuOssOption);
      },
    },
    uploadVideo: {
      async customUpload(file: File, insertFn: InsertFnType) {
        const qiniuOssOption: QiniuOssOption = {
          file,
          onError: (err) => {
            console.log(err);
          },
          onSuccess: (body) => {
            insertFn(body.url, body.url, body.url);
          },
        };
        qiniuOssService.run(qiniuOssOption);
      },
    },
  };

  // editor 实例
  const [editor, setEditor] = useState<IDomEditor | null>(null);
  // 编辑器内容
  // const [html, setHtml] = useState(value || '<p>hello</p>');

  useEffect(() => {
    if (editor) {
      const toolbar = DomEditor.getToolbar(editor);
      if (toolbar) {
        const curToolbarConfig = toolbar.getConfig();
        console.log(curToolbarConfig.toolbarKeys);
      }
    }
    return () => {
      if (editor === null) return;
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);

  return (
    <>
      <div style={{ border: '1px solid #ccc', zIndex: 100 }}>
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc' }}
        />
        <Editor
          defaultConfig={editorConfig}
          value={value}
          onCreated={setEditor}
          onChange={(_editor) => onChange && onChange(_editor.getHtml())}
          mode="default"
          style={{ height: '500px', overflowY: 'hidden' }}
        />
      </div>
    </>
  );
};

export default WangEditor;
