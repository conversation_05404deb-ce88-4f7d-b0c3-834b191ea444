variables:
  GIT_STRATEGY: fetch
stages:
  - build

build-oms-bs-web:
  stage: build
  artifacts:
    name: '$CI_COMMIT_REF_NAME'
    paths:
      - dist
  cache:
    untracked: true
    paths:
      - node_modules
  before_script:
    - pnpm install
  script:
    - pnpm build
  only:
    - /^t.*$/
    - /^d.*$/
    - master
    - test-develop
    - oversea_test
    - prod
    - oversea-prod
  tags:
    - build-oms-bs-web
# test runner
