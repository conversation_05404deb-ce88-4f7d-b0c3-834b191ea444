import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const devicepackageApiOption: Record<
  'devicepackageDevices' | 'devicepackageDeviceLink' | 'devicepackageUnlink',
  RequestOption
> = {
  // 获取设备列表(包括云存服务信息)
  devicepackageDevices: {
    url: '/adm/bs/devicepackage/devices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 关联设备
  devicepackageDeviceLink: {
    url: '/adm/bs/devicepackage/device/link',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 解除关联
  devicepackageUnlink: {
    url: '/adm/bs/devicepackage/device/unlink',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
