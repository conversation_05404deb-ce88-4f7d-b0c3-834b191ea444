import type { ApiSuccessEnum, Data } from '@/api/common/types';
import type { CreateUserData, User, UserQueryParams } from '@/api/user/types';
import { request } from '@/utils/request';
import { RequestMethod, RequestOption } from '@mantas/request';

/**
 * 用户服务 - 按model模块分类封装
 */
export const userService = {
  /**
   * 获取用户列表
   */
  getUserList: (params: UserQueryParams): Promise<Data<User>> => {
    return request('/api/users', {
      method: RequestMethod.GET,
      params,
    } as RequestOption);
  },

  /**
   * 创建用户
   */
  createUser: (data: CreateUserData): Promise<User> => {
    return request('/api/users', {
      method: RequestMethod.POST,
      data,
    } as RequestOption);
  },

  /**
   * 更新用户
   */
  updateUser: (id: number, data: Partial<CreateUserData>): Promise<User> => {
    return request(`/api/users/${id}`, {
      method: RequestMethod.PUT,
      data,
    } as RequestOption);
  },

  /**
   * 删除用户
   */
  deleteUser: (id: number): Promise<ApiSuccessEnum> => {
    return request(`/api/users/${id}`, {
      method: RequestMethod.DELETE,
    } as RequestOption);
  },

  /**
   * 获取用户详情
   */
  getUserDetail: (id: number): Promise<User> => {
    return request(`/api/users/${id}`, {
      method: RequestMethod.GET,
    } as RequestOption);
  },
};
