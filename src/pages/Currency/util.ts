import { CurrencyDetail, CurrencyParam } from '@/models/currency/interface';
import { CurrencyForm } from './interface';

export const initialCurrencyForm: CurrencyForm = {};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: CurrencyForm,
  id?: number,
): CurrencyParam => {
  const param: CurrencyParam = {};
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: CurrencyDetail,
): CurrencyForm => {
  const formData: CurrencyForm = {};
  return formData;
};
