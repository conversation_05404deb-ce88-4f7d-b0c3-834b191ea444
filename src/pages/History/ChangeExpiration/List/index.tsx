import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  ChangeExpirationRecord,
  ChangeExpirationRecordListParam,
} from '@/models/history/interface';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import {
  fetchChangeExpirationList,
  fetchChangeExpirationListExportUrl,
} from '../../../../models/history/fetch';
import { HistoryRecordTypeEnum } from '../../interface';
import { historyRecordTypeMap } from '../../util';
import { ChangeExpirationTableForm, UrlParam } from '../interface';
import {
  transferFormDataToUrlParam,
  transferUrlParamToFormData,
  transferUrlParamToListParam,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<ChangeExpirationRecord[]>([]);
  const [listParam, setListParam] = useState<ChangeExpirationRecordListParam>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const formRef = useRef<ProFormInstance<ChangeExpirationTableForm>>();

  // 获取列表数据
  const requestChangeExpirationList = async (
    param: ChangeExpirationRecordListParam,
  ) => {
    const { items, ...rest } = await fetchChangeExpirationList(
      param as ChangeExpirationRecordListParam,
    );
    setPagination(rest);
    setDataList(items);
  };

  const exportData = async () => {
    const url = await fetchChangeExpirationListExportUrl(
      omit(listParam, ['offset', 'limit']),
    );
    window.open(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue();
          const param: UrlParam = transferFormDataToUrlParam(formData);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/business/historyRecord/changeExpiration',
              param,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<ChangeExpirationRecord>> = [
    {
      title: '操作类型',
      dataIndex: 'operationType',
      search: false,
      render: () =>
        historyRecordTypeMap[HistoryRecordTypeEnum.CHANGE_EXPIRATION],
    },
    {
      title: '订单单号',
      dataIndex: 'orderId',
      render: (_, record) => (
        <RedirectLink
          text={record.orderId}
          linkUrl={`/business/order?orderId=${record.orderId}`}
        />
      ),
    },
    {
      title: `设备类型`,
      dataIndex: 'deviceType',
      valueType: 'select',
      valueEnum: {
        d4sh: 'D4sh',
        d4h: 'D4h',
        t5: 'T5',
        t6: 'T6',
      },
    },
    {
      title: 'Plan名称',
      dataIndex: 'skuName',
    },
    {
      title: '效期到期时间变革',
      dataIndex: 'expirationTimeChange',
      search: false,
      render: (_, record) =>
        `${dayjs(record.beforeChangeExpiration).format(
          'YYYY-MM-DD HH:mm:ss',
        )} 变更为 ${dayjs(record.afterChangeExpiration).format(
          'YYYY-MM-DD HH:mm:ss',
        )}`,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.operationTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      search: false,
    },
  ];

  useEffect(() => {
    if (!formRef || !formRef.current) return;
    const formData = transferUrlParamToFormData(urlParam);
    formRef.current.setFieldsValue(formData);
    const _listParam = transferUrlParamToListParam(urlParam);
    console.log(_listParam);
    setListParam(_listParam);
  }, [urlParam, formRef]);

  useEffect(() => {
    if (listParam) {
      requestChangeExpirationList(listParam);
    }
  }, [listParam]);

  return (
    <ProTable<ChangeExpirationRecord>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      formRef={formRef}
      rowKey="operationTime"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => listParam && setListParam({ ...listParam }) }}
      toolbar={{
        actions: [
          <Button key="export" type="primary" onClick={() => exportData()}>
            导出报表
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
