import { RefundStatusEnum, RefundTypeEnum } from './interface';

export const refundStatusNameObj: { [key in RefundStatusEnum]: string } = {
  [RefundStatusEnum.FAILED]: '退款失败',
  [RefundStatusEnum.PENDING]: '退款中',
  [RefundStatusEnum.SUCCEEDED]: '退款成功',
};

export const refundTypeEnumNameObj: { [key in RefundTypeEnum]: string } = {
  [RefundTypeEnum.PARTIAL]: '指定金额',
  [RefundTypeEnum.ALL]: '全款',
};
