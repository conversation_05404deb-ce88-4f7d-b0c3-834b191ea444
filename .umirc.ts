import { defineConfig } from '@umijs/max';
import proxy from './config/proxy';
import routes from './config/routes';

const { REACT_APP_ENV } = process.env;

export default defineConfig({
  antd: {},
  dva: {},
  locale: {
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  // layout: {
  //   // https://umijs.org/zh-CN/plugins/plugin-layout
  //   // locale: false,
  //   // siderWidth: 208,
  //   // title: 'Petkit运营管理系统',
  //   layout: 'mix',
  // },
  // devServer: {
  //   port: REACT_APP_ENV === 'local' ? 15001 : 15000,
  // },
  title: '小佩商城',
  ignoreMomentLocale: true,
  // nodeModulesTransform: {
  //   type: 'none',
  // },
  moment2dayjs: {},
  proxy: proxy[REACT_APP_ENV || 'sandbox'],
  routes,
  base: '/business/',
  publicPath: '/business/',
  esbuildMinifyIIFE: true,
  tailwindcss: {},
});
