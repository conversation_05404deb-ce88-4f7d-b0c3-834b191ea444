/**
 * Tailwind CSS 测试组件
 * 用于验证 Tailwind CSS 是否正常工作，特别是 important 配置
 */

import { <PERSON><PERSON>, Card, Space } from 'antd';
import React from 'react';

const TailwindTest: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center text-blue-600 mb-8">
          Tailwind CSS 集成测试
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 基础样式测试 */}
          <Card title="基础样式" className="shadow-lg">
            <div className="space-y-4">
              <p className="text-gray-700">这是一段测试文本</p>
              <div className="w-full h-4 bg-blue-200 rounded-full">
                <div className="h-4 bg-blue-600 rounded-full w-3/4"></div>
              </div>
              <Button type="primary" className="w-full">
                Antd 按钮
              </Button>
            </div>
          </Card>

          {/* 颜色测试 */}
          <Card title="颜色系统" className="shadow-lg">
            <div className="grid grid-cols-3 gap-2">
              <div className="h-12 bg-red-500 rounded"></div>
              <div className="h-12 bg-green-500 rounded"></div>
              <div className="h-12 bg-blue-500 rounded"></div>
              <div className="h-12 bg-yellow-500 rounded"></div>
              <div className="h-12 bg-purple-500 rounded"></div>
              <div className="h-12 bg-pink-500 rounded"></div>
            </div>
          </Card>

          {/* 响应式测试 */}
          <Card title="响应式布局" className="shadow-lg">
            <div className="space-y-2">
              <div className="bg-blue-100 p-2 rounded text-sm">默认: 全宽</div>
              <div className="bg-green-100 p-2 rounded text-sm md:bg-green-200">
                md: 绿色变深
              </div>
              <div className="bg-purple-100 p-2 rounded text-sm lg:bg-purple-200">
                lg: 紫色变深
              </div>
            </div>
          </Card>

          {/* Important 测试 */}
          <Card title="Important 测试" className="shadow-lg col-span-full">
            <div className="space-y-4">
              <p className="text-lg font-semibold text-gray-800">
                以下元素测试 important 配置是否生效：
              </p>

              {/* 这个元素有内联样式，测试 Tailwind 的 important 是否能覆盖 */}
              <div
                style={{
                  backgroundColor: 'red',
                  color: 'white',
                  padding: '8px',
                }}
                className="bg-green-500 text-black p-4 rounded"
              >
                如果看到绿色背景和黑色文字，说明 Tailwind important 配置生效
              </div>

              {/* 测试与 Antd 样式的兼容性 */}
              <Space>
                <Button
                  type="primary"
                  className="bg-purple-600 border-purple-600 hover:bg-purple-700"
                >
                  自定义紫色按钮
                </Button>
                <Button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none">
                  渐变按钮
                </Button>
              </Space>

              {/* 测试复杂布局 */}
              <div className="flex flex-wrap gap-4 p-4 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg">
                <div className="flex-1 min-w-48 p-4 bg-white rounded shadow">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    卡片 1
                  </h3>
                  <p className="text-gray-600">Flexbox 布局测试</p>
                </div>
                <div className="flex-1 min-w-48 p-4 bg-white rounded shadow">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    卡片 2
                  </h3>
                  <p className="text-gray-600">响应式网格</p>
                </div>
                <div className="flex-1 min-w-48 p-4 bg-white rounded shadow">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    卡片 3
                  </h3>
                  <p className="text-gray-600">阴影效果</p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 状态指示器 */}
        <div className="mt-8 p-6 bg-white rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">配置状态</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>Tailwind CSS 已加载</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>Important 模式已启用</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>与 Ant Design 兼容</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>PostCSS 配置正确</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindTest;
