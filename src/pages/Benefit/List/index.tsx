/**
 * 权益表列表页面 - 支持拖拽排序
 */
import {
  fetchBenefitDelete,
  fetchBenefitList,
  fetchBenefitReorder,
} from '@/api/benefit/fetch';
import { BenefitInfo, BenefitListParam } from '@/api/benefit/types';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { initBenefitListParam } from '@/models/product/util';
import useUrlState from '@ahooksjs/use-url-state';
import {
  DeleteOutlined,
  EditOutlined,
  HolderOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { history } from '@umijs/max';
import { Button, Image, message, Modal, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { BenefitTableForm, UrlParam } from '../interface';
import { allOption } from '../util';
import { DraggableWrapper } from './DragComponents';
import DraggableRow, { RowContext } from './DraggableRow';

const BenefitList: React.FC = () => {
  const formRef = useRef<ProFormInstance<BenefitTableForm>>();
  const [urlParams] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<BenefitInfo[]>([]);
  const [listParam, setListParam] = useState<BenefitListParam>();

  // 拖拽相关状态
  // const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragMode, setIsDragMode] = useState(false);
  const [originalDataList, setOriginalDataList] = useState<BenefitInfo[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // 获取列表数据
  const requestBenefitTableList = async (param: BenefitListParam) => {
    try {
      const result = await fetchBenefitList(param);
      console.log(result);

      setDataList(result);
    } catch (error) {
      setDataList([]);
    }
  };

  // 拖拽开始处理
  const handleDragStart = useCallback(() =>
    // event: any
    {
      // const { active } = event;
      // setActiveId(active.id);
    }, []);

  // 拖拽结束处理
  const handleDragEnd = useCallback(
    (event: any) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      const oldIndex = dataList.findIndex((item) => item.groupId === active.id);
      const newIndex = dataList.findIndex((item) => item.groupId === over.id);

      if (oldIndex === -1 || newIndex === -1) {
        return;
      }

      // 只更新本地数据，不调用接口
      const newDataList = arrayMove(dataList, oldIndex, newIndex);

      // 更新每个item的sort值 - 使用原始数据的sort值
      const updatedDataList = newDataList.map((item, index) => ({
        ...item,
        sort: originalDataList[index].sort, // 使用原始位置的sort值
      }));

      setDataList(updatedDataList);
      setHasUnsavedChanges(true);
    },
    [dataList, originalDataList],
  );

  // 保存排序
  const handleSaveSort = useCallback(async () => {
    if (!hasUnsavedChanges) return;

    // 准备重排序参数 - 新的行数据中的sort使用原行数据中的sort字段进行覆盖
    const reorderParams = {
      deviceType: urlParams.deviceType,
      sortList: dataList.map((item, index) => ({
        groupId: item.groupId,
        sort: originalDataList[index].sort, // 使用原始位置的sort值
      })),
    };

    try {
      // 调用重排序接口
      await fetchBenefitReorder(reorderParams);
      setIsDragMode(false);
      message.success('排序保存成功');
    } catch (error) {
      message.error('排序保存失败');
    } finally {
      setHasUnsavedChanges(false);
    }
  }, [hasUnsavedChanges, dataList, originalDataList, urlParams.deviceType]);

  // 切换排序模式
  const toggleDragMode = useCallback(() => {
    if (isDragMode) {
      // 退出排序模式 - 恢复到原始状态并重新获取数据
      if (hasUnsavedChanges) {
        Modal.confirm({
          title: '确认取消排序',
          content: '有未保存的排序变更，确定要取消吗？',
          onOk: () => {
            setDataList(originalDataList);
            setHasUnsavedChanges(false);
            setIsDragMode(false);
            // 重新获取数据确保最新状态
            if (listParam) {
              requestBenefitTableList(listParam);
            }
          },
        });
      } else {
        setIsDragMode(false);
      }
    } else {
      // 进入排序模式 - 保存当前数据作为原始数据
      setOriginalDataList([...dataList]);
      setHasUnsavedChanges(false);
      setIsDragMode(true);
    }
  }, [isDragMode, hasUnsavedChanges, dataList, originalDataList, listParam]);

  // 删除权益表
  const handleDelete = useCallback(
    async (id: number) => {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个权益表吗？删除后不可恢复。',
        onOk: async () => {
          try {
            await fetchBenefitDelete(id);
            message.success('删除成功');
            if (!listParam) return;
            setListParam({ ...listParam });
          } catch (error) {
            message.error('删除失败');
          }
        },
      });
    },
    [listParam],
  );

  // 编辑
  const editBenefitTable = (groupId: string) => {
    const url = `/service/benefit/edit/${groupId}?deviceType=${urlParams.deviceType}`;
    console.log(groupId);
    history.push(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: BenefitTableForm = form?.getFieldsValue();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/benefit`,
              param: { ...formData },
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/benefit`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const DragHandle: React.FC = () => {
    const { setActivatorNodeRef, listeners } = useContext(RowContext);
    return (
      <Button
        type="text"
        size="small"
        icon={<HolderOutlined />}
        style={{ cursor: 'move' }}
        ref={setActivatorNodeRef}
        {...listeners}
      />
    );
  };

  // 表格列配置
  const columns: ProColumns<BenefitInfo>[] = [
    // 只在排序模式下显示拖拽手柄列
    ...(isDragMode
      ? [
          {
            title: '',
            dataIndex: 'dragHandle',
            key: 'dragHandle',
            width: 50,
            search: false,
            render: () => <DragHandle />, // 实际渲染在DraggableRow中处理
          },
        ]
      : []),
    {
      title: '权益名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <Space>
          {record.icon && (
            <Image
              width={24}
              height={24}
              src={record.icon}
              preview={false}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          )}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: 'groupId',
      dataIndex: 'groupId',
      key: 'groupId',
      width: 200,
      search: false,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 60,
      search: false,
    },
    {
      title: '是否核心权益',
      dataIndex: 'isCoreBenefit',
      key: 'isCoreBenefit',
      width: 120,
      search: false,
      render: (_, record) => (record.isCoreBenefit ? '是' : '-'),
    },
    {
      title: '权益描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      search: false,
    },
    {
      title: '权益配图',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      search: false,
      render: (_, record) =>
        record.image ? (
          <Image
            width={60}
            height={60}
            src={record.image}
            style={{ objectFit: 'cover' }}
          />
        ) : (
          <span>-</span>
        ),
    },
    {
      title: '权益预设值',
      dataIndex: 'attributes',
      key: 'attributes',
      width: 150,
      search: false,
      render: (_, record) => {
        const attributes = record.attributes;
        if (!attributes || attributes.length === 0) return <span>-</span>;
        const displayItems = attributes.slice(0, 3);
        const remainingCount = Math.max(0, attributes.length - 3);

        return (
          <Space wrap>
            {displayItems.map((attr: any, index: number) => (
              <Tag
                key={index}
                color={attr.isDefaultAttribute ? 'blue' : 'default'}
              >
                {attr.attributeText}
              </Tag>
            ))}
            {remainingCount > 0 && <Tag>+{remainingCount}</Tag>}
          </Space>
        );
      },
    },
    {
      title: '适用设备',
      dataIndex: 'deviceTypes',
      key: 'deviceTypes',
      width: 120,
      search: false,
      render: () => (
        <Space wrap>
          <Tag color="processing">通用</Tag>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      search: false,
      render: (_, record) =>
        dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right',
      search: false,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => editBenefitTable(record.groupId)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(Number(record.groupId))}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (!urlParams.deviceType) return;

    const _listParam: BenefitListParam = {
      ...initBenefitListParam,
      deviceType:
        urlParams.deviceType === allOption.value
          ? undefined
          : urlParams.deviceType,
      name: urlParams.name || undefined,
    };
    setListParam(_listParam);

    const form = formRef.current;
    if (!form) {
      return;
    }
    const tableFormData: BenefitTableForm = {
      name: urlParams.name || undefined,
    };
    form.setFieldsValue(tableFormData);
  }, [urlParams]);

  useEffect(() => {
    if (!listParam) return;
    requestBenefitTableList(listParam);
  }, [listParam]);

  // 表格配置
  const tableProps: ProTableProps<BenefitInfo, BenefitTableForm> = {
    dataSource: dataList,
    columns,
    defaultSize: 'small' as const,
    rowKey: 'groupId',
    formRef,
    search: {
      defaultCollapsed: false,
      span: spanConfig,
      optionRender: searchOptionRender,
    },
    options: {
      reload: () => {
        if (listParam) setListParam({ ...listParam });
      },
    },
    scroll: {
      x: columns
        .filter((col) => col.dataIndex !== 'action')
        .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
    },
    toolbar: {
      actions: [
        <Button key="sort" onClick={toggleDragMode}>
          {isDragMode ? '取消排序' : '排序'}
        </Button>,
        ...(isDragMode
          ? [
              <Button
                key="save-sort"
                type="primary"
                disabled={!hasUnsavedChanges}
                onClick={handleSaveSort}
              >
                保存排序
              </Button>,
            ]
          : []),
        <Button
          key="create"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => editBenefitTable('')}
        >
          新增
        </Button>,
      ],
    },
    pagination: {
      pageSize: 20,
      showQuickJumper: true,
      showSizeChanger: true,
      pageSizeOptions: [20, 50, 100],
    },
    // pagination: {
    //   sho,
    // },
    // pagination: {
    //   pageSize: paginator.limit,
    //   total: paginator.total,
    //   current: Math.trunc(paginator.offset / paginator.limit) + 1,
    //   showQuickJumper: true,
    //   onChange: onPaginationChanged,
    // },
  };

  // 根据拖拽模式渲染不同的表格
  if (isDragMode) {
    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <ProTable<BenefitInfo>
          {...tableProps}
          components={{
            body: {
              wrapper: DraggableWrapper,
              row: DraggableRow,
            },
          }}
        />
        {/* <DragOverlay>{activeId ? <div>正在拖拽...</div> : null}</DragOverlay> */}
      </DndContext>
    );
  }

  return <ProTable<BenefitInfo> {...tableProps} />;
};

export default BenefitList;
