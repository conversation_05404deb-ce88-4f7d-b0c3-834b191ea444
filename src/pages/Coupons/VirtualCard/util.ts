import {
  CouponCardCodeListParam,
  CouponCardDetail,
  CouponCardListParam,
  CouponCardParam,
  CouponCardSkuParam,
  CouponCardUploadResult,
  CouponCardUploadValidResult,
  CouponCardUsageModeEnum,
} from '@/models/card/interface';
import {
  initCouponCardCodeListParam,
  initCouponCardListParam,
} from '@/models/card/util';
import {
  divideWithPrecision,
  formatToTwoDecimals,
  multiplyWithPrecision,
} from '@/models/common.util';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { ProductSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import { PageActionType } from '@/pages/enum';
import { uuid } from '@/utils/uuid';
import dayjs from 'dayjs';
import {
  DeviceSkuForm,
  VirtualCardCodeUrlParam,
  VirtualCardForm,
  VirtualCardUrlParam,
} from './interface';

// 卡券列表接口参数转换
export const transferUrlParamToListParam = (
  urlParam: VirtualCardUrlParam,
): CouponCardListParam => {
  const listParam: CouponCardListParam = {
    ...initCouponCardListParam,
    deviceType: (urlParam?.deviceTypes as SuitableDeviceTypeEnum) || undefined,
    batchId: urlParam?.batchId || undefined,
    cardCouponName: urlParam?.cardCouponName || undefined,
    cardCouponNickName: urlParam?.cardCouponNickName || undefined,
    skuName: urlParam?.relativeSku || undefined,
    demandSide: urlParam?.demandSide || undefined,
    startTime: urlParam?.createStart || undefined,
    endTime: urlParam?.createEnd || undefined,
  };
  // 防止startTime比endTime大
  if (
    listParam.startTime &&
    listParam.endTime &&
    listParam.startTime < listParam.endTime
  ) {
    const temp = listParam.startTime;
    listParam.startTime = listParam.endTime;
    listParam.endTime = temp;
  }
  return listParam;
};

// 卡号列表接口参数转换
export const transferCodeUrlParamToCodeListParam = (
  urlParam: VirtualCardCodeUrlParam,
): CouponCardCodeListParam => {
  const listParam: CouponCardCodeListParam = {
    ...initCouponCardCodeListParam,
    cardCode: urlParam.cardCode || undefined,
    mobile: urlParam.mobile || undefined,
    snCode: urlParam.snCode || undefined,
    exchangeStatus: urlParam.exchangeStatus || undefined,
    exchangeStartTime: urlParam.exchangeStartTime || undefined,
    exchangeEndTime: urlParam.exchangeEndTime || undefined,
    batchId: urlParam.batchId || undefined,
    deviceTypes: urlParam.deviceTypes || undefined,
    activatedDeviceType: urlParam.activatedDeviceType || undefined,
    startTime: urlParam.startTime || undefined,
    endTime: urlParam.endTime || undefined,
    sendWay: urlParam.sendWay || undefined,
  };
  return listParam;
};

export const initDeviceSkuForm: DeviceSkuForm = {
  skuId: 0,
  deviceType: null,
};

export const initVirtualCardForm: VirtualCardForm = {
  name: '',
  aliasName: '',
  deviceSkuList: [initDeviceSkuForm],
  serviceTime: null,
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  exchangeDate: null,
  coverImage: [],
  realPrice: null,
  facePrice: null,
  userType: CouponCardUsageModeEnum.USER,
  userMobileList: [],
  userNum: null,
  demandSide: '',
};

// 将接口返回的详情转换为formData
export const transferDetailToFormData = (
  detail: CouponCardDetail,
  pageActionType: PageActionType = PageActionType.COPYING,
): VirtualCardForm => {
  const formData: VirtualCardForm = {
    name: detail.cardCouponName,
    aliasName: detail.cardCouponNickName,
    deviceSkuList: detail.skus.map((sku) => ({
      skuId: sku.productSkuId,
      deviceType: sku.deviceType,
    })),
    serviceTime: detail.serviceTime,
    serviceTimeUnit: detail.serviceTimeUnit,
    exchangeDate:
      pageActionType === PageActionType.EDITING
        ? dayjs(detail.exchangeEnd)
        : null,
    coverImage: detail.couponImage
      ? [
          {
            uid: uuid(),
            name: `${detail.cardCouponName}卡券封面`,
            url: detail.couponImage,
          },
        ]
      : [],
    realPrice: detail.realPrice,
    facePrice: detail.facePrice,
    userType: CouponCardUsageModeEnum.USER,
    userMobileList: [],
    userNum: null,
    demandSide: detail.demandSide,
  };
  return formData;
};

export const transferFormDataToParam = (
  formData: VirtualCardForm,
  associateSkuListGroup: ProductSku[][],
  uploadResult?: CouponCardUploadResult,
) => {
  const param: CouponCardParam = {
    couponImage: formData.coverImage[0]?.url || '',
    cardCouponName: formData.name,
    cardCouponNickName: formData.aliasName,
    demandSide: formData.demandSide,
    expiredTime: formData.exchangeDate?.valueOf() || dayjs().valueOf(),
    realPrice: formData.realPrice || 0,
    facePrice: formData.facePrice || 0,
    mobileKey: uploadResult?.mobileKey || uploadResult?.emailKey || '',
    mode: formData.userType,
    serviceTime: formData.serviceTime || 1,
    serviceTimeUnit: formData.serviceTimeUnit,
    skuRequestParams: associateSkuListGroup
      .map((group, index) => {
        const deviceSku = formData.deviceSkuList[index];
        const skuRequestParam: CouponCardSkuParam = {
          deviceType: deviceSku.deviceType || SuitableDeviceTypeEnum.D4sh,
          productSkuId: deviceSku.skuId,
          skuName: group?.[0]?.name || '',
        };
        // console.log(deviceSku, skuRequestParam);
        return skuRequestParam;
      })
      .filter((sku) => sku.deviceType && sku.productSkuId),
  };
  console.log(formData.deviceSkuList, associateSkuListGroup);
  if (formData.userType === CouponCardUsageModeEnum.NUM) {
    param.sendNum = formData.userNum || 0;
  }
  return param;
};

export const calculateEstimatedPrice = (
  associateSkuListGroup: ProductSku[][],
  formData: VirtualCardForm,
  couponCardUploadValidResult?: CouponCardUploadValidResult,
) => {
  const monthDay = 30;
  const skuList = associateSkuListGroup
    .reduce((prev, curr) => curr.concat(prev), [])
    .map((sku) => {
      // 日均价
      let pricePerDay = 0;
      const price = sku.price.price;
      const serviceTime = sku.serviceTime;
      switch (sku.serviceTimeUnit) {
        case ServiceTimeUnitEnum.MONTH:
          pricePerDay = divideWithPrecision(price, monthDay * serviceTime);
          break;
        case ServiceTimeUnitEnum.YEAR:
          pricePerDay = divideWithPrecision(price, monthDay * 12 * serviceTime);
          break;
        default:
          break;
      }
      return pricePerDay;
    });

  const maxPrice = skuList.reduce(
    (prev, curr) => (prev > curr ? prev : curr),
    0,
  );

  let serviceTime: number = formData.serviceTime || 0;
  switch (formData.serviceTimeUnit) {
    case ServiceTimeUnitEnum.MONTH:
      serviceTime = (formData.serviceTime || 0) * monthDay;
      break;
    default:
      break;
  }

  // 预计金额等于 日均价*服务时长*卡券数量
  const estimatedPrice = multiplyWithPrecision(
    maxPrice,
    serviceTime,
    couponCardUploadValidResult?.total || formData.userNum || 0,
  );

  return formatToTwoDecimals(estimatedPrice);
};
