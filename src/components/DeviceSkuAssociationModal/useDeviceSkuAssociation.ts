import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  ProductSku,
  RelationProductSkuListParam,
} from '@/models/product/interface';
import { useCallback, useState } from 'react';

interface UseDeviceSkuAssociationReturn {
  /** Modal是否可见 */
  visible: boolean;
  /** 当前设备类型 */
  currentDeviceType: SuitableDeviceTypeEnum | null;
  /** 关联参数 */
  relationParam: RelationProductSkuListParam | undefined;
  /** 显示Modal */
  showModal: (
    deviceType: SuitableDeviceTypeEnum,
    param?: RelationProductSkuListParam,
  ) => void;
  /** 隐藏Modal */
  hideModal: () => void;
  /** 处理确认关联 */
  handleConfirm: (
    deviceType: SuitableDeviceTypeEnum,
    sku: ProductSku,
    onSuccess?: (deviceType: SuitableDeviceTypeEnum, sku: ProductSku) => void,
  ) => void;
}

/**
 * 设备类型关联SKU Modal状态管理Hook
 * @param onAssociate 关联成功回调
 */
export const useDeviceSkuAssociation = (
  onAssociate?: (deviceType: SuitableDeviceTypeEnum, sku: ProductSku) => void,
): UseDeviceSkuAssociationReturn => {
  const [visible, setVisible] = useState(false);
  const [currentDeviceType, setCurrentDeviceType] =
    useState<SuitableDeviceTypeEnum | null>(null);
  const [relationParam, setRelationParam] =
    useState<RelationProductSkuListParam>();

  const showModal = useCallback(
    (
      deviceType: SuitableDeviceTypeEnum,
      param?: RelationProductSkuListParam,
    ) => {
      setCurrentDeviceType(deviceType);
      setRelationParam(param);
      setVisible(true);
    },
    [],
  );

  const hideModal = useCallback(() => {
    setVisible(false);
    setCurrentDeviceType(null);
    setRelationParam(undefined);
  }, []);

  const handleConfirm = useCallback(
    (
      deviceType: SuitableDeviceTypeEnum,
      sku: ProductSku,
      onSuccess?: (deviceType: SuitableDeviceTypeEnum, sku: ProductSku) => void,
    ) => {
      // 首先调用外部传入的关联回调
      if (onAssociate) {
        onAssociate(deviceType, sku);
      }

      // 然后调用局部成功回调
      if (onSuccess) {
        onSuccess(deviceType, sku);
      }

      // 隐藏Modal
      hideModal();
    },
    [onAssociate, hideModal],
  );

  return {
    visible,
    currentDeviceType,
    relationParam,
    showModal,
    hideModal,
    handleConfirm,
  };
};
