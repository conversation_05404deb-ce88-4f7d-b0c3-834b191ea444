/**
 * 表格拖拽排序组件
 */

import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import React from 'react';

// 拖拽包装器组件
export const DraggableWrapper: React.FC<any> = (props) => {
  const { children, ...restProps } = props;

  // children[1] 是 dataSource，检查是否为数组
  // 当 dataSource 为空数组时，antd 会给出 'No Data' 元素
  const items =
    children[1] instanceof Array
      ? children[1].map((child: any) => child.key)
      : [];

  return (
    <SortableContext
      items={items}
      strategy={verticalListSortingStrategy}
      {...restProps}
    >
      <tbody {...restProps}>{children}</tbody>
    </SortableContext>
  );
};
