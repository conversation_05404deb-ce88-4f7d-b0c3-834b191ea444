import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction } from '@/models/common.util';
import { ConnectState } from '@/models/connect';
import { SearchTypeEnum, WifiSearchTypeEnum } from '@/models/device/interface';
import { deviceTypeEnum } from '@/models/device/util';
import { fetchOrderList, fetchOrderListExport } from '@/models/order/fetch';
import { Order, OrderListParam } from '@/models/order/interface';
import {
  initOrderListParam,
  orderStatusObj,
  payTypeObj,
} from '@/models/order/util';
import { antdUtils } from '@/utils/antd.util';
import global from '@/utils/global';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useDispatch, useSelector } from '@umijs/max';
import { Button, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { forIn, omit } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { OrderTableForm } from '../interface';

const List: React.FC = () => {
  const dispatch = useDispatch();
  const [urlParam] = useUrlState<OrderTableForm>();
  const formRef = useRef<ProFormInstance<OrderTableForm>>();
  const [dataList, setDataList] = useState<Order[]>([]);
  const [listParam, setListParam] = useState<OrderListParam>();
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);

  const currencyCodeObj = useSelector(
    ({ currency }: ConnectState) => currency.currencyCodeObj,
  );

  // 获取列表数据
  const requestOrderList = async (
    param: OrderListParam = initOrderListParam,
  ) => {
    const { items, ...rest } = await fetchOrderList(param);
    setPaginator(rest);
    setDataList(items);
  };

  // 导出报表
  const requestExportOrderList = async (
    param: Omit<OrderListParam, 'limit' | 'offset'>,
  ) => {
    const url = await fetchOrderListExport(param);
    window.open(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: OrderTableForm = form?.getFieldsValue();
          const param: Partial<
            Omit<OrderTableForm, 'createTime'> & {
              createTime: number[];
            }
          > = {};
          // const param: Partial<
          //   Record<keyof OrderTableForm | string, OrderTableForm[keyof OrderTableForm]>
          // > = {};
          forIn(formData, (value, key) => {
            if (value) {
              if (key === 'createTime') {
                param[key] = (value as Dayjs[]).length
                  ? [value[0].valueOf(), value[1].valueOf()]
                  : [];
              } else {
                param[key] = value;
              }
            }
          });

          if (!param.createTime || !param.createTime.length) {
            delete param.createTime;
          }

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/order`,
              param,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/order`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;
    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<Order>> = [
    {
      title: '订单编号',
      dataIndex: 'orderId',
      width: 150,
    },
    {
      title: '平台订单ID',
      dataIndex: 'tradeNo',
      width: 150,
    },
    {
      title: 'SKU ID',
      dataIndex: 'skuId',
      render: (_, row) => (
        <RedirectLink
          text={row.skuId || ''}
          linkUrl={`/business/cloudServiceSku`}
          params={{ redirect: 'edit', skuId: row.skuId || 0 }}
        />
      ),
      width: 100,
    },
    {
      title: 'SKU名称',
      dataIndex: 'skuName',
      width: 200,
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      width: 120,
      render: (_, row) => (
        <RedirectLink
          text={row.userId}
          linkUrl={`/user/users`}
          params={{
            username: row.userId,
          }}
        />
      ),
    },
    {
      title: '设备ID',
      dataIndex: 'device',
      width: 120,
      search: false,
      render: (_, row) => (
        <RedirectLink
          text={row.device.deviceId}
          linkUrl={`/${row.device.deviceType.toLocaleLowerCase()}/devices`}
          params={{
            type: global.bluetoothDeviceList.includes(
              row.device.deviceType.toLocaleLowerCase(),
            )
              ? SearchTypeEnum.ID
              : WifiSearchTypeEnum.ID,
            s: row.device.deviceId || '',
          }}
        />
      ),
    },
    {
      title: '设备类型',
      dataIndex: 'device',
      width: 120,
      valueType: 'select',
      valueEnum: deviceTypeEnum,
      render: (_, row) => row.device.deviceType,
    },
    {
      title: '实付金额',
      dataIndex: 'amount',
      search: false,
      width: 150,
    },
    {
      title: '是否补差价',
      dataIndex: 'upgrade',
      width: 100,
      valueType: 'select',
      valueEnum: { 1: '是', 0: '否' },
      formItemProps: {
        name: 'isUpgrade',
      },
      render: (_, row) => (row.upgrade ? '是' : '否'),
    },
    {
      title: '货币名称',
      dataIndex: 'currency',
      valueType: 'select',
      valueEnum: currencyCodeObj,
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} placeholder="请选择" allowClear showSearch />
      ),
      search: false,
      width: 100,
    },
    {
      title: '支付方式',
      dataIndex: 'platform',
      valueType: 'select',
      valueEnum: payTypeObj,
      render: (_, row) => payTypeObj[row.platform],
      width: 120,
    },
    {
      title: '自动续费',
      dataIndex: 'isReNew',
      valueType: 'select',
      valueEnum: {
        0: '否',
        1: '是',
      },
      render: (_, row) => (row.isReNew ? '是' : '否'),
      width: 120,
    },
    {
      title: '订单状态',
      dataIndex: 'state',
      valueType: 'select',
      valueEnum: orderStatusObj,
      render: (_, row) => orderStatusObj[row.state],
      width: 120,
    },
    {
      title: '活动',
      tooltip: '指关联此SKU的活动名称',
      dataIndex: 'activityName',
      width: 120,
      search: false,
      render: (_, row) => row.activityName || '-',
    },
    {
      title: '是否活动',
      dataIndex: 'isActivity',
      hideInTable: true,
      valueType: 'select',
      valueEnum: { 1: '是', 0: '否' },
    },
    {
      title: '下单时间',
      dataIndex: 'createTime',
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
      valueType: 'dateTimeRange',
      width: 180,
    },
    {
      title: '支付失败原因',
      dataIndex: 'subscriptionDeductErrorMsg',
      width: 150,
      search: false,
    },
  ];

  useEffect(() => {
    dispatch({ type: 'currency/requestCurrencyCodeList' });
  }, [dispatch]);

  useEffect(() => {
    const formData = urlParam;
    // 将formData中的createTime字段以逗号分隔，转换为dayjs对象
    const createTimes: Dayjs[] = (
      formData.createTime ? formData.createTime.split(',') : []
    ).map((item: string) => dayjs(+item));
    // 获取formRef的current属性
    const form = formRef.current;
    // 将createTimes中的值赋给formData
    formData.createTime = createTimes;
    // 将formData中的值赋给form
    form?.setFieldsValue(formData);

    // 删除formData中的createTime字段
    delete formData.createTime;
    // 将formData中的值赋给_listParam
    const _listParam: OrderListParam = {
      ...initOrderListParam,
      ...listParam,
      ...omit(formData, 'device'),
      deviceType: formData.device,
    };
    // 如果createTimes存在，并且不为空，则将createTimes中的开始时间和结束时间赋给_listParam
    if (createTimes && createTimes.length) {
      _listParam.createTimeStart = createTimes[0].valueOf();
      _listParam.createTimeEnd = createTimes[1].valueOf();
    }

    // 设置listP
    setListParam(_listParam);
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestOrderList(listParam);
  }, [listParam]);

  return (
    <ProTable<Order>
      dataSource={dataList}
      columns={columns}
      formRef={formRef}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: 8,
        optionRender: searchOptionRender,
      }}
      toolbar={{
        actions: [
          <Button
            key="export"
            type="primary"
            onClick={() => {
              // 使用lodash去除listParam中的offset和limit
              const _listParam = omit(listParam, ['offset', 'limit']);
              requestExportOrderList(_listParam);
            }}
          >
            导出报表
          </Button>,
        ],
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      // toolbar={{
      //   actions: [
      //     <Button key="button" type="primary" icon={<PlusOutlined />} onClick={() => editOrder(0)}>
      //       新增
      //     </Button>,
      //   ],
      // }}
      pagination={{
        ...antdUtils.transferPaginatorToTablePagination(paginator),
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
