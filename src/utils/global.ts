import { LocaleEnum } from '@/locale';
import {
  BreadcrumbInfo,
  SelectOption,
  ValueType,
} from '@/models/common.interface';
import { Location } from '@umijs/max';
import { StorageService } from './Storage';

class Global {
  static instance: Global;
  baiduHmKey = '';
  iconfontUrl = '';
  // 蓝牙设备列表
  bluetoothDeviceList = [
    'hg',
    'fit',
    'go',
    'k3',
    'aq',
    'aqr',
    'p3',
    'h2',
    'w5',
    'r2',
  ];
  // wifi设备列表
  wifiDeviceList = [
    't3',
    'mate',
    'feeder',
    'cozy',
    'feedermini',
    'aqh1',
    'k2',
    't4',
    't5',
    't6',
    'd3',
    'd4',
    'd4s',
    'h3',
    'd4sh',
    'd4h',
  ];
  centerDeviceList = ['aqh1', 'd4s', 't4', 't5', 'h3'];

  deviceNames: { [key: string]: string } = {
    hg: '烘干箱',
  };

  storageService: StorageService;

  getToken: () => string;
  private _language: LocaleEnum = LocaleEnum.CN;

  constructor() {
    this.storageService = new StorageService();
    this.getToken = () => this.storageService?.getLocalItem('token') || '';

    if (typeof Global.instance === 'object') {
      return Global.instance;
    }

    console.log('new global');

    Global.instance = this;
    return this;
  }

  set language(_language: LocaleEnum) {
    this._language = _language;
  }

  get language(): LocaleEnum {
    return this._language;
  }

  getOptionsByMap(map: Map<ValueType, string>): SelectOption[] {
    const options: SelectOption[] = [];
    map.forEach((value, key) => {
      options.push({
        label: value,
        value: key,
      });
    });
    return options;
  }

  getBreadcrumbInfo(
    oldBreadcrumbInfo: BreadcrumbInfo,
    suffix?: string,
    location?: Location,
  ): BreadcrumbInfo[] {
    const newRoutes = [oldBreadcrumbInfo];
    const listBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${suffix}列表`,
    };
    const detailBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${suffix}详情`,
    };
    const editBreadcrumbInfo: BreadcrumbInfo = {
      path: '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${suffix}`,
    };
    if (location?.pathname.includes('list')) {
      newRoutes.push(listBreadcrumbInfo);
    } else if (location?.pathname.includes('detail')) {
      newRoutes.push(listBreadcrumbInfo);
      newRoutes.push(detailBreadcrumbInfo);
    } else if (location?.pathname.includes('edit')) {
      newRoutes.push(listBreadcrumbInfo);
      editBreadcrumbInfo.breadcrumbName = `${
        location.pathname.includes('edit/0') ? '新增' : '编辑'
      }${editBreadcrumbInfo.breadcrumbName}`;
      newRoutes.push(editBreadcrumbInfo);
    }
    console.log(newRoutes);
    return newRoutes;
  }
}

export default new Global();
