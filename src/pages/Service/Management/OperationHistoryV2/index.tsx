import { Key } from '@/models/common.interface';
import { fetchServiceUpdatingHistoryListV2 } from '@/models/service/fetch';
import {
  ServiceUpdatingHistoryListParam,
  ServiceUpdatingHistoryV2,
} from '@/models/service/interface';
import {
  initServiceUpdatingHistoryListParam,
  servicePlanChangeTypeName,
} from '@/models/service/util';
import { Paginator, initPaginator } from '@/utils/request';
import { uuid } from '@/utils/uuid';
import { Modal, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { forIn, isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';

interface Props {
  deviceType: string;
  serviceId: number;
  showModal: boolean;
  onClose?: () => void;
}

const keyNameMap = {
  orderId: '订单编号',
  workIndate: '到期时间',
  changeReason: '修改原因',
  tradeNo: '交易号',
  serviceStatus: '服务状态',
  deviceSn: '设备SN',
  planName: 'Plan名称',
  payWay: '支付方式',
  serviceId: 'Plan ID',
};

const OperationHistoryV2: React.FC<Props> = ({
  showModal,
  serviceId,
  onClose,
  deviceType,
}: Props) => {
  const [listParam, setListParam] = useState<ServiceUpdatingHistoryListParam>({
    ...initServiceUpdatingHistoryListParam,
    serviceId,
  });
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [list, setList] = useState<Array<ServiceUpdatingHistoryV2 & Key>>([]);

  const requestHistoryList = async (
    _listParam: ServiceUpdatingHistoryListParam,
  ) => {
    let result = await fetchServiceUpdatingHistoryListV2({
      ..._listParam,
      deviceType,
    });
    if (!result) return;
    const { items, ...rest } = result;
    setPaginator(rest);
    setList(items.map((item) => ({ ...item, key: uuid() })));
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let current = page;
    if (pageSize !== listParam.limit) {
      current = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (current - 1) * pageSize,
    });
  };

  const columns: ColumnsType<ServiceUpdatingHistoryV2> = [
    {
      title: '变更类型',
      dataIndex: 'changeType',
      width: 100,
      render: (_, record) => servicePlanChangeTypeName[record.changeType],
    },
    {
      title: '变更字段',
      dataIndex: 'changeField',
      width: 100,
      render: (_, record) => (
        <div>
          {record.changeField.split('|').map((item, key) => (
            <div key={String(key)}>{item}</div>
          ))}
        </div>
      ),
    },
    {
      title: '变更前',
      dataIndex: 'changeBefore',
      width: 300,
      render: (_, record) => {
        const valueList: string[] = [];
        let changeBefore = record.changeBefore;
        if (!record.changeBefore || record.changeBefore === '-') {
          changeBefore = '{}';
        }
        forIn(JSON.parse(changeBefore), (value, key) => {
          valueList.push(`${keyNameMap[key] || key}:${value}`);
        });
        if (!valueList || !valueList.length) return '-';
        return valueList.map((item, index) => (
          <div key={String(index)}>{item || '-'}</div>
        ));
      },
    },
    {
      title: '变更后',
      dataIndex: 'changeAfter',
      width: 300,
      render: (_, record) => {
        const valueList: string[] = [];
        let changeAfter = record.changeAfter;
        if (!record.changeAfter || record.changeAfter === '-') {
          changeAfter = '{}';
        }
        forIn(JSON.parse(changeAfter), (value, key) => {
          valueList.push(`${keyNameMap[key] || key}:${value}`);
        });
        if (!valueList || !valueList.length) return '-';
        return valueList.map((item, index) => (
          <div key={String(index)}>{item || '-'}</div>
        ));
      },
    },
    {
      title: '操作者',
      dataIndex: 'operator',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operatorTime',
      width: 200,
      render: (_, record) =>
        dayjs(record.operatorTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  useEffect(() => {
    if (listParam && listParam.serviceId) {
      requestHistoryList(listParam);
    }
  }, [listParam]);

  return (
    <Modal
      width={950}
      destroyOnClose
      open={showModal}
      footer={null}
      title="变更历史"
      onCancel={() => isFunction(onClose) && onClose()}
    >
      <Table<ServiceUpdatingHistoryV2>
        dataSource={list}
        columns={columns}
        size="small"
        rowKey="id"
        scroll={{
          x: columns.reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          total: paginator.total,
          pageSize: paginator.limit,
          showSizeChanger: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default OperationHistoryV2;
