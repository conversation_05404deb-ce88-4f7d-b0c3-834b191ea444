import { ProductSku } from '@/models/product/interface';
import { serviceTimeUnitNameObj } from '@/models/product/util';
import { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';

// 选择关联SKU和展示关联SKU所用到的column定义
export const associateSkuTableColumns: Array<ProColumns<ProductSku>> = [
  {
    title: 'SKU ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: 'SKU名称',
    dataIndex: 'name',
    width: 130,
  },
  {
    title: 'SKU别名',
    dataIndex: 'aliasName',
    width: 130,
  },
  {
    title: 'SKU能力',
    dataIndex: 'capacities',
    width: 130,
    search: false,
    render: (_, record) => (
      <div>
        {(record.capacities || []).map((capacity, index) => (
          <div key={String(index)}>
            {capacity.name}
            {capacity.cycleTime}天循环
          </div>
        ))}
      </div>
    ),
  },
  {
    title: '适用设备',
    dataIndex: 'deviceTypes',
    width: 120,
    search: false,
    render: (_, record) =>
      (record.deviceTypes || []).map((deviceType) => deviceType),
  },
  // {
  //   title: 'Plan能力',
  //   dataIndex: 'capacities',
  //   valueType: 'select',
  //   width: 150,
  //   search: false,
  // },
  {
    title: '服务时长',
    dataIndex: 'serviceTime',
    search: false,
    width: 120,
    render: (_, row) =>
      `${row.serviceTime}${serviceTimeUnitNameObj[row.serviceTimeUnit]}`,
  },
  {
    title: '关联Sku',
    dataIndex: 'relationSkuId',
    search: false,
    width: 120,
    render: (_, row) => row.relationSkuId || '--',
  },
  {
    title: '自动续费',
    dataIndex: 'isRenew',
    search: false,
    width: 120,
    render: (_, row) => (row.price.isReNew ? '是' : '否'),
  },
  {
    title: '价格(元)',
    dataIndex: 'price',
    search: false,
    width: 120,
    render: (_, row) => row.price.price,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    search: false,
    width: 180,
    render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
  },
];
