import Uploader from '@/components/Uploader';
import { ApiSuccessEnum } from '@/models/common.interface';
import { deviceList, transferArrayToSelectOption } from '@/models/common.util';
import {
  fetchBenefitCompareAdd,
  fetchBenefitCompareDetail,
  fetchBenefitCompareUpdate,
} from '@/models/product/fetch';
import { InfoCircleFilled } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import { Button, Form, Select, Typography, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { BenefitCompareForm } from '../interface';
import {
  initialBenefitCompareForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<BenefitCompareForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const isEditing = useMemo(() => {
    return Boolean(param && param.id && +param.id);
  }, [param]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchBenefitCompareDetail(id);
    const formData = transferDetailToFormData(detail);
    // console.log(formData);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: BenefitCompareForm) => {
    if (!formData.images.length || !formData.images[0].url) {
      message.warning('图片没有上传成功，请重新上传！');
      return;
    }

    setLoading(true);
    const _param = transferFormDataToParam(formData, +(param.id || 0));
    // console.log(_param);
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchBenefitCompareUpdate(_param);
      } else {
        state = '创建';
        result = await fetchBenefitCompareAdd(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialBenefitCompareForm}
      >
        <Form.Item
          name="deviceType"
          label="适用设备"
          rules={[{ required: true, message: '请选择适用设备' }]}
        >
          <Select
            placeholder="请选择适用设备"
            disabled={isEditing}
            options={transferArrayToSelectOption(deviceList)}
          />
        </Form.Item>
        <Form.Item
          name="images"
          label="配图"
          getValueFromEvent={normFile}
          extra={
            <Typography.Text type="secondary">
              <InfoCircleFilled style={{ color: '#1890ff', marginRight: 8 }} />
              特权对比配图尺寸为260px*146px
            </Typography.Text>
          }
          shouldUpdate
          valuePropName="fileList"
          rules={[{ required: true, message: '请上传配图' }]}
        >
          <Uploader />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
