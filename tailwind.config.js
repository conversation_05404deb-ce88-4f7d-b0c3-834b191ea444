module.exports = {
  important: true,
  content: [
    './src/pages/**/*.tsx',
    './src/components/**.tsx',
    './src/layouts/**.tsx',
  ],
  theme: {
    extend: {
      fontSize: {
        '1.5xl': '1.375rem',
        '2.5xl': '1.625rem',
      },
      spacing: {
        0.5: '0.125rem',
        1.5: '0.375rem',
        2.5: '0.625rem',
        3.5: '0.875rem',
        4.5: '1.125rem',
        5.5: '1.375rem',
        33: '8.125rem',
        34: '8.5rem',
        '78.x': '78px',
      },
      borderWidth: {
        1: '1px',
        3: '3px',
        5: '5px',
        7: '7px',
      },
      borderRadius: {
        half: '50%',
      },
      inset: {
        '-3.x': '-3px',
        '-11.x': '-11px',
      },
      maxWidth: {
        80: '5rem',
      },
      minWidth: {
        80: '5rem',
      },
      scale: {
        80: '0.8',
        85: '0.85',
        90: '0.9',
      },
    },
  },
};
