import { SubscriptionListParam } from '@/models/subscription/interface';
import { initSubscriptionListParam } from '@/models/subscription/util';
import { SubscriptionUrlParam } from './interface';

export const transformUrlParamToListParam = (
  urlParam: SubscriptionUrlParam,
): SubscriptionListParam => {
  const listParam = {
    ...initSubscriptionListParam,
    subNo: urlParam.subscriptionNo,
    deviceId: urlParam.deviceId,
    userId: urlParam.userId,
    state: urlParam.state,
    deviceType: urlParam.deviceType,
    startTime: urlParam.startTime,
    endTime: urlParam.endTime,
  };

  return listParam;
};

// export const initialSubscriptionForm: SubscriptionForm = {
// };

// // 将formData转换为param
// export const transferFormDataToParam = (formData: SubscriptionForm, id?: number): SubscriptionParam => {
//   const param: SubscriptionParam = {
//   };
//   id && (param.id = id);
//   return param;
// };

// // 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: SubscriptionDetail): SubscriptionForm => {
//   const formData: SubscriptionForm = {
//   };
//   return formData;
// };
