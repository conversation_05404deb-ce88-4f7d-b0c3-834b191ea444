/**
 * 权益表管理页面工具函数
 */
import {
  BenefitAttributeParam,
  BenefitDetail,
  BenefitSaveParam,
} from '@/api/benefit/types';
import {
  benefitSuitableDeviceTypeOptions,
  BenefitValueTypeEnum,
} from '@/api/benefit/utils';
import { SuitableDeviceTypeEnum } from '@/api/device';
import { ZH_VALUE } from '@/models/app/util';
import { SelectOption, StatusEnum } from '@/models/common.interface';
import { uuid } from '@/utils/uuid';
import { BenefitAttributeFormData, BenefitFormData } from './interface';

export const allOption: SelectOption = { label: '全部', value: 'ALL' };

/**
 * 获取设备类型标签
 * @param value 设备类型值
 * @returns 设备类型标签
 */
export const getDeviceTypeLabel = (value: string): string => {
  const option = benefitSuitableDeviceTypeOptions.find(
    (opt) => opt.value === value,
  );
  return option?.label || value;
};

/**
 * 验证权益表表单数据
 * @param data 表单数据
 * @returns 验证结果
 */
export const validateBenefitTableForm = (
  data: any,
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('权益名称不能为空');
  }

  if (!data.description?.trim()) {
    errors.push('权益描述不能为空');
  }

  if (!data.introduction?.trim()) {
    errors.push('权益介绍不能为空');
  }

  if (!data.deviceTypes || data.deviceTypes.length === 0) {
    errors.push('请选择适用设备');
  }

  if (!data.attributes || data.attributes.length === 0) {
    errors.push('请至少添加一个权益属性');
  }

  // 验证权益属性
  if (data.attributes && data.attributes.length > 0) {
    data.attributes.forEach((attr: any, index: number) => {
      if (!attr.attributeText?.trim()) {
        errors.push(`第${index + 1}个权益属性文本不能为空`);
      }
      if (
        attr.priority === undefined ||
        attr.priority < 1 ||
        attr.priority > 99
      ) {
        errors.push(`第${index + 1}个权益属性优先级必须在1-99之间`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * 格式化权益属性显示
 * @param attributes 权益属性数组
 * @param maxDisplay 最大显示数量
 * @returns 格式化后的显示信息
 */
export const formatAttributesDisplay = (
  attributes: any[],
  maxDisplay: number = 3,
): { displayItems: any[]; remainingCount: number } => {
  if (!attributes || attributes.length === 0) {
    return { displayItems: [], remainingCount: 0 };
  }

  const displayItems = attributes.slice(0, maxDisplay);
  const remainingCount = Math.max(0, attributes.length - maxDisplay);

  return { displayItems, remainingCount };
};

/**
 * 转换详情数据为表单数据
 * @param detail 权益表详情数据
 * @returns 表单数据和预设值
 */
export const transferDetailToFormData = (
  detail: BenefitDetail, // 使用 any 类型以适应实际的 API 返回数据结构
): BenefitFormData => {
  // 构建表单数据
  const formData: BenefitFormData = {
    isCore: !!detail.isCoreBenefit,
    deviceTypeList: [detail.deviceType] as SuitableDeviceTypeEnum[],
    language: ZH_VALUE,
    name: detail.name,
    alias: detail.alias || '',
    description: detail.description,
    introduction: detail.introduction,
    configImageList: [
      {
        uid: uuid(),
        name: detail.name,
        url: detail.image,
      },
    ],
    valueType:
      detail.attributes?.[0].attributeType || BenefitValueTypeEnum.BOOLEAN,
    defaultValueList: [],
    booleanDefaultValueList: [],
  };

  // 构建预设值数据
  const presetValues: BenefitAttributeFormData[] = detail.attributes.map(
    (attr) =>
      ({
        isDefault: attr.isDefaultAttribute === StatusEnum.ENABLE,
        benefitId: attr.benefitId,
        normalValue: attr.attributeText,
        specialValue: attr.attributeSelectedText,
      } as BenefitAttributeFormData),
  );

  if (formData.valueType === BenefitValueTypeEnum.BOOLEAN) {
    formData.booleanDefaultValueList = presetValues;
  } else {
    formData.defaultValueList = presetValues;
  }
  console.log(formData);

  return formData;
};

// ==================== 表单初始化数据常量 ====================

/**
 * 权益表编辑页面表单初始化数据
 * 包含BenefitTableFormData接口的所有字段默认值
 */
export const initialBenefitTableFormData: BenefitFormData = {
  // 基本信息字段
  isCore: false,
  language: ZH_VALUE,
  name: '', // 权益名称
  alias: '', // 权益别名
  description: '', // 权益描述
  introduction: '', // 权益介绍

  // 配置字段
  deviceTypeList: [], // 适用设备列表，默认为空数组
  configImageList: [
    {
      uid: uuid(),
      name: 'logo',
      url: 'https://sandbox-img5.petkit.cn/post/2025/7/25/68833757d6e02000018d8165fuZ1vizh4',
    },
  ], // 配置图片列表，默认为空数组

  // 权益类型配置
  valueType: BenefitValueTypeEnum.BOOLEAN, // 权益类型，默认为布尔型(BenefitValueTypeEnum.BOOLEAN = 0)

  // 默认值配置
  defaultValueList: [
    // 布尔型默认预设值：支持和不支持
    { normalValue: '', specialValue: '', isDefault: true },
    {
      normalValue: '',
      specialValue: '',
      isDefault: false,
    },
  ],

  booleanDefaultValueList: [
    // 布尔型默认预设值：支持和不支持
    { normalValue: '支持', isDefault: true },
    {
      normalValue: '不支持',
      isDefault: false,
    },
  ],
};

/**
 * 转换预设值列表为权益属性参数
 * @param defaultValueList 预设值列表
 * @param valueType 权益类型
 * @returns 权益属性参数数组
 */
export const transferDefaultValuesToAttributes = (
  defaultValueList: BenefitAttributeFormData[],
  valueType: BenefitValueTypeEnum,
): BenefitAttributeParam[] => {
  return defaultValueList
    .filter((item) => {
      // 过滤掉无效的预设值：normalValue为空的项
      return item.normalValue && item.normalValue.trim() !== '';
    })
    .map((item) => {
      const baseAttribute: BenefitAttributeParam = {
        attributeText: item.normalValue || '',
        isDefaultAttribute: +item.isDefault,
        attributeType: valueType,
        attributeSelectedText: item.specialValue,
      };

      // 只有当benefitId存在时才添加该字段
      if (!!item.benefitId) {
        baseAttribute.benefitId = item.benefitId;
      }

      // 枚举型需要添加精选权益值
      if (valueType === BenefitValueTypeEnum.ENUM && item.specialValue) {
        baseAttribute.attributeSelectedText = item.specialValue;
      }

      return baseAttribute;
    });
};

/**
 * 根据权益类型获取对应的预设值列表并转换为属性参数
 * @param formData 表单数据（支持主表单）
 * @returns 权益属性参数数组
 */
export const getAttributesFromFormData = (
  formData: BenefitFormData,
): BenefitAttributeParam[] => {
  const { valueType } = formData;

  // 根据权益类型选择对应的预设值列表
  const defaultValueList =
    valueType === BenefitValueTypeEnum.BOOLEAN
      ? formData.booleanDefaultValueList
      : formData.defaultValueList;

  return transferDefaultValuesToAttributes(defaultValueList, valueType);
};

/**
 * 验证枚举类型的预设值配置
 * @param defaultValueList 预设值列表
 * @param isCore 是否为核心权益
 * @param configName 配置名称（用于错误提示）
 * @returns 验证结果
 */
const validateEnumPresetValues = (
  defaultValueList: BenefitAttributeFormData[],
  isCore: boolean,
  configName: string,
): { isInvalid: boolean; errorInfo?: string } => {
  // 过滤出有效的预设值（normalValue不为空）
  const validNormalValues = defaultValueList.filter(
    (item) => item.normalValue && item.normalValue.trim() !== '',
  );

  // 必须至少有一个normalValue
  if (validNormalValues.length === 0) {
    return {
      isInvalid: true,
      errorInfo: `${configName}中必须至少配置一个预设值`,
    };
  }

  // 如果是核心权益，需要验证specialValue
  if (isCore) {
    // 过滤出有效的specialValue
    const validSpecialValues = defaultValueList.filter(
      (item) => item.specialValue && item.specialValue.trim() !== '',
    );

    // specialValue必须存在
    if (validSpecialValues.length === 0) {
      return {
        isInvalid: true,
        errorInfo: `${configName}中核心权益必须配置精选权益值(specialValue)`,
      };
    }

    // normalValue和specialValue数量必须匹配
    if (validNormalValues.length !== validSpecialValues.length) {
      return {
        isInvalid: true,
        errorInfo: `${configName}中普通权益值(${validNormalValues.length}个)与精选权益值(${validSpecialValues.length}个)数量不匹配`,
      };
    }
  }

  return { isInvalid: false };
};

/**
 * 校验预设值配置
 * @param values 主表单数据
 * @param localeContentList 本地化配置列表
 * @returns 验证结果
 */
export const valideDefaultValue = (
  values: BenefitFormData,
): { isInvalid: boolean; errorInfo?: string } => {
  const { valueType, defaultValueList, isCore } = values;

  // 布尔类型始终允许
  if (valueType === BenefitValueTypeEnum.BOOLEAN) {
    return { isInvalid: false };
  }

  // 枚举类型验证
  if (valueType === BenefitValueTypeEnum.ENUM) {
    // 验证主表单的预设值配置
    const mainValidResult = validateEnumPresetValues(
      defaultValueList,
      isCore,
      '默认配置',
    );
    if (mainValidResult.isInvalid) {
      return mainValidResult;
    }
  }

  return { isInvalid: false };
};

/**
 * 转换表单数据为API提交参数
 * 将Benefit编辑页面的表单数据转换为BenefitSaveParam格式
 * @param formData 表单数据
 * @param presetValues 预设值列表
 * @param localizationList 本地化配置列表
 * @param id 权益表ID（编辑时传入）
 * @returns 符合BenefitSaveParam接口的提交参数
 */
export const transferFormDataToParam = (
  formData: BenefitFormData,
  id?: string,
) => {
  // 构建提交参数 - 合并主表单数据和本地化配置数据
  const param: BenefitSaveParam = {
    isCoreBenefit: +formData.isCore,
    name: formData.name,
    alias: formData.alias,
    description: formData.description,
    introduction: formData.introduction,
    image: formData.configImageList[0]?.url || '',
    supportedDeviceTypes: formData.deviceTypeList, // 映射到BenefitSaveParam的字段名
    attributes: getAttributesFromFormData(formData),
  };

  if (!!id) param.groupId = id;

  return param;
};

// ==================== 使用示例和说明 ====================
