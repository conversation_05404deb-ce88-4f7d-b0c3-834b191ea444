import { getCurrentPrefix } from './currentPrefix';

type PrimitiveType = string | number | boolean;
type SearchParamValue =
  | PrimitiveType
  | undefined
  | Record<string, any>
  | (PrimitiveType | Record<string, any>)[];

interface SearchParamObject {
  [key: string]: SearchParamValue;
}

const isObject = (value: any): value is Record<string, any> => {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
};

const flattenObject = (
  obj: Record<string, any>,
  prefix = '',
): Record<string, PrimitiveType> => {
  return Object.keys(obj).reduce(
    (acc: Record<string, PrimitiveType>, key: string) => {
      const propName = prefix ? `${prefix}[${key}]` : key;

      if (isObject(obj[key])) {
        Object.assign(acc, flattenObject(obj[key], propName));
      } else if (obj[key] !== undefined && !Array.isArray(obj[key])) {
        acc[propName] = obj[key];
      }

      return acc;
    },
    {},
  );
};

export const getUrlWithSearchParam = (
  url: string,
  param: SearchParamObject,
  includeSession = true,
): string => {
  let fullUrl = `${getCurrentPrefix()}${url}`;
  const searchParams = {
    ...param,
    ...(includeSession && {
      'X-Admin-Session': localStorage.getItem('sessionToken'),
    }),
  };

  const searchStr = new URLSearchParams();

  Object.entries(searchParams).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((item) => {
        if (isObject(item)) {
          // 处理对象数组
          const flattenedItem = flattenObject(item, key);
          Object.entries(flattenedItem).forEach(([flatKey, flatValue]) => {
            if (flatValue !== undefined) {
              searchStr.append(flatKey, String(flatValue));
            }
          });
        } else if (item !== undefined) {
          // 处理基本类型数组
          searchStr.append(key, String(item));
        }
      });
    } else if (isObject(value)) {
      // 处理对象
      const flattenedObj = flattenObject(value, key);
      Object.entries(flattenedObj).forEach(([flatKey, flatValue]) => {
        if (flatValue !== undefined) {
          searchStr.append(flatKey, String(flatValue));
        }
      });
    } else if (value !== undefined) {
      // 处理基本类型
      searchStr.append(key, String(value));
    }
  });

  if (searchStr.toString()) {
    fullUrl += url.includes('?')
      ? `&${searchStr.toString()}`
      : `?${searchStr.toString()}`;
  }

  return fullUrl;
};
