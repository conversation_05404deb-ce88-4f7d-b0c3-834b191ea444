import { historyApiOption } from '@/models/history/api';
import request, { Data, PaginatorParam } from '@/utils/request';
import {
  ChangeExpirationRecord,
  ChangeExpirationRecordListParam,
  RefundOrderRecord,
  RefundOrderRecordListParam,
  VirtualCardDistribution,
  VirtualCardDistributionListParam,
} from './interface';

// 获取发卡历史记录
export const fetchVirtualCardDistributionList = (
  param?: VirtualCardDistributionListParam,
): Promise<Data<VirtualCardDistribution>> => {
  const config = historyApiOption.virtualCardDistributionList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 导出发卡历史记录
export const fetchVirtualCardDistributionExportUrl = (
  param: Omit<VirtualCardDistributionListParam, keyof PaginatorParam>,
): Promise<string> => {
  const config = historyApiOption.virtualCardDistributionExport;
  config.option.params = param;
  return request(config.url, config.option);
};

// 修改效期历史记录
export const fetchChangeExpirationList = (
  param: ChangeExpirationRecordListParam,
): Promise<Data<ChangeExpirationRecord>> => {
  const config = historyApiOption.changeExpirationList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 导出修改效期历史记录
export const fetchChangeExpirationListExportUrl = (
  param: Omit<ChangeExpirationRecordListParam, keyof PaginatorParam>,
): Promise<string> => {
  const config = historyApiOption.changeExpirationListExport;
  config.option.params = param;
  return request(config.url, config.option);
};

// 退款历史记录
export const fetchRefundOrderList = (
  param: RefundOrderRecordListParam,
): Promise<Data<RefundOrderRecord>> => {
  const config = historyApiOption.refundOrderList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 导出退款历史记录
export const fetchRefundOrderListExportUrl = (
  param: Omit<RefundOrderRecordListParam, keyof PaginatorParam>,
): Promise<string> => {
  const config = historyApiOption.refundOrderListExport;
  config.option.params = param;
  return request(config.url, config.option);
};
