export interface Route {
  /**
   * Any valid URL path
   */
  path?: string;
  /**
   * A React component to render only when the location matches.
   */
  component?: string | (() => any);
  wrappers?: string[];
  /**
   * navigate to a new location
   */
  redirect?: string;
  /**
   * When true, the active class/style will only be applied if the location is matched exactly.
   */
  exact?: boolean;
  routes?: Route[];
  [k: string]: any;
}

const cnRouters: Route[] = [
  {
    path: `/service`,
    routes: [
      {
        path: `/service`,
        redirect: `/service/cloud-package-sku`,
      },
      {
        path: `/service/cloud-package-sku`,
        component: `@/pages/CloudPackage/Sku`,
        routes: [
          {
            path: `/service/cloud-package-sku`,
            redirect: `/service/cloud-package-sku/list`,
          },
          {
            path: `/service/cloud-package-sku/list`,
            component: `@/pages/CloudPackage/Sku/List`,
          },
          {
            path: `/service/cloud-package-sku/edit/:id`,
            component: `@/pages/CloudPackage/Sku/Edit`,
          },
        ],
      },
      {
        path: `/service/cloud-package/sku/bundle`,
        component: `@/pages/CloudPackage/BundleSku`,
        routes: [
          {
            path: `/service/cloud-package/sku/bundle`,
            redirect: `/service/cloud-package/sku/bundle/list`,
          },
          {
            path: `/service/cloud-package/sku/bundle/list`,
            component: `@/pages/CloudPackage/BundleSku/List`,
          },
          // {
          //   path: `/service/cloud-package/sku/bundle/edit/:id`,
          //   component: `@/pages/CloudPackage/BundleSku/Edit`,
          // },
        ],
      },
      {
        path: `/service/benefit`,
        component: `@/pages/Benefit`,
        routes: [
          {
            path: `/service/benefit`,
            redirect: `/service/benefit/list`,
          },
          {
            path: `/service/benefit/list`,
            component: `@/pages/Benefit/List`,
          },
          {
            path: `/service/benefit/edit/:id`,
            component: `@/pages/Benefit/Edit`,
          },
        ],
      },
      {
        path: `/service/benefit-compare`,
        component: `@/pages/BenefitCompare`,
        routes: [
          {
            path: `/service/benefit-compare`,
            redirect: `/service/benefit-compare/list`,
          },
          {
            path: `/service/benefit-compare/list`,
            component: `@/pages/BenefitCompare/List`,
          },
          {
            path: `/service/benefit-compare/edit/:id`,
            component: `@/pages/BenefitCompare/Edit`,
          },
        ],
      },
      {
        path: `/service/capacity`,
        component: `@/pages/Capacity`,
        routes: [
          {
            path: `/service/capacity`,
            redirect: `/service/capacity/list`,
          },
          {
            path: `/service/capacity/list`,
            component: `@/pages/Capacity/List`,
          },
          {
            path: `/service/capacity/edit/:id`,
            component: `@/pages/Capacity/Edit`,
          },
        ],
      },
      {
        path: `/service/order`,
        component: `@/pages/Order`,
        routes: [
          {
            path: `/service/order`,
            redirect: `/service/order/list`,
          },
          {
            path: `/service/order/list`,
            component: `@/pages/Order/List`,
          },
          // {
          //   path: `/service/capacity/edit/:id`,
          //   component: `@/pages/Capacity/Edit`,
          // },
        ],
      },
      {
        path: `/service/events`,
        component: `@/pages/WarnEvent`,
        routes: [
          {
            path: `/service/events`,
            redirect: `/service/events/list`,
          },
          {
            path: `/service/events/list`,
            component: `@/pages/WarnEvent`,
          },
        ],
      },
      {
        path: `/service/management`,
        component: `@/pages/Service/Management`,
        routes: [
          {
            path: `/service/management/`,
            redirect: `/service/management/list`,
          },
          {
            path: `/service/management/list`,
            component: `@/pages/Service/Management/List`,
          },
          {
            path: `/service/management/list/v2`,
            component: `@/pages/Service/Management/ListV2`,
          },
        ],
      },
      {
        path: '/service/operation/package',
        component: '@/pages/OperationPackage',
        routes: [
          {
            path: '/service/operation/package/',
            redirect: '/service/operation/package/list',
          },
          {
            path: '/service/operation/package/list',
            component: '@/pages/OperationPackage/List',
          },
          {
            path: '/service/operation/package/edit/:id',
            component: '@/pages/OperationPackage/Edit',
          },
        ],
      },
    ],
  },
  {
    paht: '/ai-video-feedback',
    component: `@/pages/AIVideoFeedback`,
    routes: [
      {
        path: '/ai-video-feedback/',
        redirect: '/ai-video-feedback/list',
      },
      {
        path: '/ai-video-feedback/list',
        component: `@/pages/AIVideoFeedback/List`,
      },
    ],
  },
  {
    path: '/coupons',
    routes: [
      {
        path: '/coupons/',
        redirect: '/coupons/virtual-card',
      },
      {
        path: '/coupons/virtual-card',
        component: `@/pages/Coupons/VirtualCard`,
        routes: [
          {
            path: '/coupons/virtual-card/',
            redirect: '/coupons/virtual-card/list',
          },
          {
            path: '/coupons/virtual-card/list',
            component: `@/pages/Coupons/VirtualCard/List`,
          },
          {
            path: '/coupons/virtual-card/edit/:type/:id',
            component: `@/pages/Coupons/VirtualCard/Edit`,
          },
          {
            path: '/coupons/virtual-card/code/list',
            component: '@/pages/Coupons/VirtualCard/CodeList',
          },
        ],
      },
    ],
  },
  {
    path: '/toolbox',
    component: `@/pages/Toolbox`,
  },
  {
    paht: '/currency',
    component: `@/pages/Currency`,
    routes: [
      {
        path: '/currency/',
        redirect: '/currency/list',
      },
      {
        path: '/currency/list',
        component: `@/pages/Currency/List`,
      },
    ],
  },
  {
    path: `/service/subscription`,
    component: `@/pages/Subscription`,
    routes: [
      {
        path: `/service/subscription`,
        redirect: `/service/subscription/list`,
      },
      {
        path: `/service/subscription/list`,
        component: `@/pages/Subscription/List`,
      },
    ],
  },
  {
    path: '/history',
    component: `@/pages/History`,
    routes: [
      {
        path: '/history/distribution/record-list',
        component: '@/pages/History/Distribution/List',
      },
      {
        path: '/history/change-expiration/record-list',
        component: '@/pages/History/ChangeExpiration/List',
      },
      {
        path: '/history/refund/record-list',
        component: '@/pages/History/Refund/List',
      },
    ],
  },
];

const routes: Route[] = [
  {
    path: '/',
    routes: [
      ...cnRouters,
      {
        path: `/`,
        redirect: '/welcome',
      },
      {
        path: `/welcome`,
        component: `@/pages/Welcome`,
      },
    ],
  },
];

export default routes;
