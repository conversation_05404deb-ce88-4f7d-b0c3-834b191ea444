import { utilApiOption } from '@/services/api/util/index';
import request, { Data } from '@/utils/request';
import { Capacity, CapacityListParam } from './interface';

// 获取能力列表
export const fetchCapacityList = (
  param?: CapacityListParam,
): Promise<Data<Capacity>> => {
  const config = utilApiOption.capacityList;
  config.option.params = param || { limit: 100000 };
  return request(config.url, config.option);
};
