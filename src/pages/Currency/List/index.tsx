import { fetchCurrencyList } from '@/models/currency/fetch';
import { Currency } from '@/models/currency/interface';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<Currency[]>([]);

  const columns: Array<ProColumns<Currency>> = [
    { title: '货币代码', dataIndex: 'currencyCode', width: 120 },
    { title: '货币名称', dataIndex: 'currencyName', width: 120 },
    {
      title: '货币符号',
      dataIndex: 'currencySymbol',
      width: 120,
    },
    {
      title: '国家',
      dataIndex: 'country',
      render: (_, record) =>
        (record.country || [])
          .map((ct) => `${ct.countryName}(${ct.countryCode})`)
          .join(),
    },
    {
      title: '支付方式',
      dataIndex: 'paymentPlatform',
      width: 200,
      render: (_, record) => (record.paymentPlatform || []).join(),
    },
  ];

  // 获取列表数据
  const requestCurrencyList = async () => {
    const result = await fetchCurrencyList();
    // console.log(result);
    // const { items, ...rest } = await fetchXxxxxxx(param);
    // setPagination(rest);
    setDataList(result);
  };

  useEffect(() => {
    requestCurrencyList();
  }, []);

  return (
    <ProTable<Currency>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={false}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => requestCurrencyList() }}
    />
  );
};

export default List;
