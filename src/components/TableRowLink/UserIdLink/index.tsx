import { postMessageFunction } from '@/models/common.util';
import { Button } from 'antd';
import React from 'react';

interface Props {
  userId: string;
}

const UserIdLink: React.FC<Props> = ({ userId }: Props) => {
  if (!userId) return <>-</>;

  return (
    <Button
      type="link"
      onClick={() =>
        postMessageFunction({
          type: 'redirect',
          content: { redirectUrl: `/user/users?username=${userId}` },
        })
      }
    >
      {userId}
    </Button>
  );
};

export default UserIdLink;
