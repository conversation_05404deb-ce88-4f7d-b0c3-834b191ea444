import { initPaginatorParam } from '@/utils/request';
import {
  BenefitCompareListParam,
  BenefitListParam,
  ProductSkuListParam,
  ProductSkuUpdatingLogsParam,
  RecordTypeEnum,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  ReNewEnum,
  ServiceTimeUnitEnum,
} from './interface';

export const serviceTimeUnitNameObj: { [key in ServiceTimeUnitEnum]: string } =
  {
    [ServiceTimeUnitEnum.DAY]: '天',
    [ServiceTimeUnitEnum.MONTH]: '月',
    [ServiceTimeUnitEnum.YEAR]: '年',
  };

export const initProductSkuListParam: ProductSkuListParam = {
  ...initPaginatorParam,
};

export const initProductSkuUpdatingLogsParam: ProductSkuUpdatingLogsParam = {
  ...initPaginatorParam,
  skuId: 0,
};

const initRelationProductSkuParam: RelationProductSkuParam = {
  uniteCapacities: [],
  serviceTime: 0,
  level: null,
  serviceTimeUnit: ServiceTimeUnitEnum.YEAR,
  uniteDeviceTypes: [],
  isReNew: ReNewEnum.RENEW,
};

export const initRelationProductSkuListParam: RelationProductSkuListParam = {
  ...initPaginatorParam,
  payload: initRelationProductSkuParam,
};

// 权益相关
export const initBenefitListParam: BenefitListParam = {
  ...initPaginatorParam,
};

export const recordTypeNameObj: { [key in RecordTypeEnum]: string } = {
  [RecordTypeEnum.EVENT]: '事件录制',
  [RecordTypeEnum.CVR]: '持续录制',
};

export const initBenefitCompareListParam: BenefitCompareListParam = {
  ...initPaginatorParam,
};
