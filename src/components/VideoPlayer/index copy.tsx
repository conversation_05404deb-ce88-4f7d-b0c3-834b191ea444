import { useEffect, useRef } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import 'videojs-contrib-hls';

window.videojs = videojs;

interface Props {
  width?: number;
  height?: number;
  src: string;
}

const VideoPlayer = ({ src, width = 800, height = 600 }: Props) => {
  //   const videoRef = useRef();
  // 创建一个 ref 用于引用 video 标签
  const videoNode = useRef(null);
  //   const player = useRef(null);

  useEffect(() => {
    const player = videojs('video-player', {
      controls: true,
      preload: 'auto',
      fluid: true,
    });

    player.src({ src, type: 'application/x-mpegURL' });
    // videojs.Vhs.xhr.beforeRequest = (options) => {
    //   console.log('beforeRequest', options);
    //   options.headers = {
    //     'X-Session': 'f706850c68404a26a02fea231cc1a2011ZJmV9hIWt87H8NC9jyM',
    //   };
    //   options.withCredentials = true;
    //   return options;
    // };

    // (window as any).Hls = Hls;
    // let hlsVideo;
    // const player: DPlayer = new DPlayer({
    //   container: document.getElementById('my-video'),
    //   video: {
    //     url: src,
    //     type: 'customHls',
    //     customType: {
    //       customHls: (video) => {
    //         const hls = new Hls({
    //           debug: false,
    //         });
    //         hlsVideo = video;
    //         hls.config.xhrSetup = (xhr, url) => {
    //           xhr.setRequestHeader(
    //             'X-Session',
    //             'f706850c68404a26a02fea231cc1a2011ZJmV9hIWt87H8NC9jyM',
    //           );
    //           //   xhr.setRequestHeader(
    //           //     'X-Admin-Session',
    //           //     localStorage.getItem('sessionToken') || '',
    //           //   );
    //         };
    //         hls.loadSource(video.src);
    //         hls.attachMedia(video);
    //         hls.on(Hls.Events.MANIFEST_PARSED, () => {
    //           console.log(Hls.Events.MANIFEST_PARSED);
    //           video.play();
    //         });
    //         // 监听视频元素的 play 事件
    //         video.addEventListener('play', function () {
    //           player.play(); // 更新 DPlayer 的播放状态
    //         });
    //         // 监听视频元素的 pause 事件
    //         video.addEventListener('pause', function () {
    //           player.pause(); // 更新 DPlayer 的暂停状态
    //         });
    //         console.log(player);
    //       },
    //     },
    //   },
    // });
    // document
    //   .querySelector('.dplayer-play-icon')
    //   ?.addEventListener('click', (ev) => {
    //     console.log('click', ev);
    //   });
    // player.on('loadeddata', (ev) => {
    //   console.log('loadeddata', hlsVideo, ev);
    // });
    // player.on('suspend', (ev) => {
    //   console.log('suspend', hlsVideo, ev);
    // });
    // 初始化 Video.js 播放器

    return () => {
      if (player) {
        player.dispose();
      }
    };
  }, [src]);

  //   return <div id="my-video" style={{ width, height }}></div>;
  return (
    <video
      id="video-player"
      className="video-js"
      style={{ width, height }}
    ></video>
  );
};

export default VideoPlayer;
