import type { CreateUserData, User } from '@/api/user/types';
import UserTable from '@/components/UserManagement/UserTable';
import { userService } from '@/services/user';
import { PlusOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Modal, message } from 'antd';
import React, { useCallback, useState } from 'react';

/**
 * 用户管理页面 - 遵循PascalCase命名规范
 */
const UserManagementPage: React.FC = () => {
  // 状态变量使用camelCase
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // 方法名使用camelCase
  const handleCreateUser = useCallback(() => {
    setSelectedUser(null);
    setIsEditing(false);
    setIsModalVisible(true);
  }, []);

  const handleEditUser = useCallback((user: User) => {
    setSelectedUser(user);
    setIsEditing(true);
    setIsModalVisible(true);
  }, []);

  const handleUserSelect = useCallback((user: User) => {
    console.log('Selected user:', user);
  }, []);

  const handleModalCancel = useCallback(() => {
    setIsModalVisible(false);
    setSelectedUser(null);
    setIsEditing(false);
  }, []);

  const handleFormSubmit = useCallback(
    async (values: CreateUserData) => {
      try {
        if (isEditing && selectedUser) {
          await userService.updateUser(selectedUser.id, values);
          message.success('用户更新成功');
        } else {
          await userService.createUser(values);
          message.success('用户创建成功');
        }
        setIsModalVisible(false);
        setSelectedUser(null);
        setIsEditing(false);
      } catch (error) {
        message.error(isEditing ? '更新失败' : '创建失败');
      }
    },
    [isEditing, selectedUser],
  );

  return (
    <PageContainer
      title="用户管理"
      breadcrumb={{
        routes: [
          { path: '/', breadcrumbName: '首页' },
          { path: '/user', breadcrumbName: '用户管理' },
        ],
      }}
    >
      <UserTable onUserSelect={handleUserSelect} onUserEdit={handleEditUser} />

      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={handleCreateUser}
        style={{ marginTop: 16 }}
      >
        新建用户
      </Button>

      <Modal
        title={isEditing ? '编辑用户' : '新建用户'}
        open={isModalVisible}
        onCancel={handleModalCancel}
        footer={null}
        destroyOnClose
      >
        <ProForm
          onFinish={handleFormSubmit}
          initialValues={selectedUser || undefined}
        >
          <ProFormText
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          />
          <ProFormText
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          />
          <ProFormSelect
            name="status"
            label="状态"
            options={[
              { label: '激活', value: 'active' },
              { label: '未激活', value: 'inactive' },
            ]}
            rules={[{ required: true, message: '请选择状态' }]}
          />
        </ProForm>
      </Modal>
    </PageContainer>
  );
};

export default UserManagementPage;
