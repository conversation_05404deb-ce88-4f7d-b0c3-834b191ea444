import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { ConnectState } from '@/models/connect';
import { deviceTypeEnum } from '@/models/device/util';
import {
  fetchProductSkuList,
  fetchProductSkuSaleStatusUpdatingByBatch,
} from '@/models/product/fetch';
import {
  ProductSku,
  ProductSkuListParam,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { antdUtils } from '@/utils/antd.util';
import { Paginator, initPaginator } from '@/utils/request';
import { DomainTypeEnum } from '@/utils/targetDomain';
import useUrlState from '@ahooksjs/use-url-state';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useDispatch, useSelector } from '@umijs/max';
import {
  Button,
  Dropdown,
  InputNumber,
  MenuProps,
  Modal,
  Space,
  Switch,
  message,
} from 'antd';
import dayjs from 'dayjs';
import forIn from 'lodash/forIn';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import OperationHistory from '../OperationHistory';
import { ProductSkuListForm } from '../interface';
import { serviceTimeUnitObj, transferListFormToListParam } from '../util';

type BatchTypes = 'batchSale' | 'batchOff';

const List: React.FC = () => {
  const dispatch = useDispatch();
  const [urlParam] = useUrlState<
    ProductSkuListForm & { redirect: 'edit'; skuId: number }
  >();
  const formRef = useRef<ProFormInstance<ProductSkuListForm>>();
  const [dataList, setDataList] = useState<ProductSku[]>([]);
  const [listParam, setListParam] = useState<ProductSkuListParam>();
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<
    Array<number | string>
  >([]);
  const [selectedSkuId, setSelectedSkuId] = useState<number>(0);
  const capacityMap = useSelector(({ util }: ConnectState) => util.capacityMap);
  const batchOperationItemList: MenuProps['items'] = [
    {
      label: '批量上架',
      key: 'batchSale',
    },
    {
      label: '批量下架',
      key: 'batchOff',
    },
  ];

  const showMoreAction = useMemo(() => {
    if (location.href.includes('petktasia')) {
      return false;
    } else if (location.href.includes('eu-pet')) {
      return false;
    } else {
      return true;
    }
  }, []);

  const moreActions: MenuProps['items'] = [
    {
      key: DomainTypeEnum.CURRENT,
      label: <span style={{ cursor: 'pointer' }}>复制</span>,
    },
  ];

  const onClickToCopy = (sku: ProductSku, key: DomainTypeEnum) => {
    console.log(sku, key);
    history.push(
      `/service/cloud-package-sku/edit/0?isCopy=1&skuId=${sku.id}&source=${key}`,
    );
  };

  // 获取列表数据
  const requestSkuList = async (param: ProductSkuListParam) => {
    try {
      setLoading(true);
      const { items, ...rest } = await fetchProductSkuList(param);
      setDataList(items);
      setPaginator(rest);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 批量修改SKU上下架
  const requestProductSkuSaleStatusUpdatingByBatch = async (
    ids: number[],
    status: boolean,
  ) => {
    const result = await fetchProductSkuSaleStatusUpdatingByBatch(ids, +status);
    if (result === ApiSuccessEnum.success) {
      message.success(
        `${ids.length > 1 ? '批量' : ''}${status ? '上' : '下'}架成功！`,
      );
      const newDataList = dataList.map((item) => {
        let { saleStatus } = item;
        if (ids.includes(item.id)) {
          saleStatus = status;
        }
        return {
          ...item,
          saleStatus,
        };
      });
      setDataList(newDataList);
    }
  };

  const switchSaleStatus = (ev: boolean, ids: number[]) => {
    Modal.confirm({
      title: `确认要${ev ? '上架' : '下架'}`,
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        requestProductSkuSaleStatusUpdatingByBatch(ids, ev);
      },
    });
  };

  // 编辑
  const editSku = (id: number) => {
    history.push(`/service/cloud-package-sku/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          // const _param = transferListFormDataToParam(form?.getFieldsValue(), listParam);
          // setListParam(_param);
          const formData: ProductSkuListForm = form?.getFieldsValue();
          // const _urlParam: ProductSkuListForm = {};
          // console.log('searchOptionRender', formData);

          forIn(formData, (value, key) => {
            if (!value) {
              delete formData[key as keyof ProductSkuListForm];
            } else if (
              value === ServiceTimeUnitEnum.MONTH ||
              value === ServiceTimeUnitEnum.YEAR
            ) {
              formData.serviceTime = 1;
              formData.serviceTimeUnit = value;
            }
          });

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/cloudServiceSku`,
              param: formData as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/cloudServiceSku`,
            },
          });
          // setListParam(initProductSkuListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const batchOperations: Record<BatchTypes, () => void> = {
    batchSale: () => {
      if (!selectedRowKeys || !selectedRowKeys.length) {
        message.warning('请先选择需要批量上架的SKU');
        return;
      }
      switchSaleStatus(true, selectedRowKeys as number[]);
    },
    batchOff: () => {
      if (!selectedRowKeys || !selectedRowKeys.length) {
        message.warning('请先选择需要批量下架的SKU');
        return;
      }
      switchSaleStatus(false, selectedRowKeys as number[]);
    },
  };

  const onModalClosed = () => {
    setSelectedSkuId(0);
  };

  const columns: Array<ProColumns<ProductSku>> = [
    {
      title: 'SKU ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: 'SKU名称',
      dataIndex: 'name',
      width: 130,
    },
    {
      title: 'SKU简称',
      dataIndex: 'shortName',
      width: 130,
    },
    {
      title: 'SKU别名',
      dataIndex: 'aliasName',
      width: 120,
    },
    {
      title: 'SKU能力',
      dataIndex: 'capacities',
      valueType: 'select',
      width: 180,
      valueEnum: capacityMap,
      render: (_, row) => (
        <>
          {row.capacities.map((item) => (
            <div key={item.type}>
              {item.name} {item.cycleTime}天循环
            </div>
          ))}
        </>
      ),
    },
    {
      title: '权益优先级',
      dataIndex: 'level',
      width: 80,
      valueType: 'select',
      hideInSearch: true,
      valueEnum: {
        '0': '0',
        1: 1,
        2: 2,
        3: 3,
        4: 4,
      },
      render: (_, row) =>
        row.level !== undefined && row.level !== null ? row.level : '-',
    },
    {
      title: '服务时长',
      dataIndex: 'serviceTime',
      width: 100,
      valueType: 'select',
      valueEnum: {
        [ServiceTimeUnitEnum.MONTH]: '1月',
        [ServiceTimeUnitEnum.YEAR]: '1年',
      },
      render: (_, row) =>
        `${
          row.serviceTimeUnit
            ? serviceTimeUnitObj[row.serviceTimeUnit] || '-'
            : '-'
        }`,
    },
    {
      title: '权益优先级',
      dataIndex: 'level',
      width: 80,
      hideInTable: true,
      valueType: 'select',
      valueEnum: {
        '0': '0',
        1: 1,
        2: 2,
        3: 3,
        4: 4,
      },
      render: (_, row) =>
        row.level !== undefined && row.level !== null ? row.level : '-',
    },
    {
      title: '关联SKU',
      dataIndex: 'relationSkuId',
      search: false,
      width: 100,
    },
    {
      title: '自动续费',
      dataIndex: 'isReNew',
      valueType: 'select',
      valueEnum: { 1: '是', 0: '否' },
      width: 100,
      render: (_, row) => (row.price.isReNew ? '是' : '否'),
    },
    {
      title: '价格(元)',
      dataIndex: 'price',
      search: false,
      width: 100,
      render: (_, row) => row.price.price,
    },
    {
      dataIndex: 'deviceType',
      title: '适用设备',
      width: 100,
      valueType: 'select',
      render: (_, row) => row.deviceTypes[0],
      valueEnum: deviceTypeEnum,
    },
    {
      dataIndex: 'sort',
      title: '优先级',
      width: 80,
      valueType: 'digit',
      render: (_, row) => row.sort || '-',
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <InputNumber
          {...rest}
          placeholder="请输入整数"
          formatter={(value) => (value ? `${parseInt(value)}` : '')}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      search: false,
      width: 180,
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '状态',
      dataIndex: 'saleStatus',
      valueType: 'select',
      valueEnum: { 1: '上架', 0: '下架' },
      width: 100,
      render: (_, row) => (
        <Switch
          checked={!!row.saleStatus}
          unCheckedChildren="下架"
          checkedChildren="上架"
          onChange={(ev) => switchSaleStatus(ev, [row.id])}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 280,
      render: (_, row) => (
        <Space split="|">
          <Button type="link" onClick={() => setSelectedSkuId(row.id)}>
            变更历史
          </Button>
          <Button type="link" onClick={() => editSku(row.id)}>
            编辑
          </Button>
          {showMoreAction ? (
            <Dropdown
              menu={{
                items: moreActions,
                onClick: (item) =>
                  onClickToCopy(row, item.key as DomainTypeEnum),
              }}
              trigger={['click']}
            >
              <Button type="link">
                <Space>
                  更多
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          ) : null}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    dispatch({ type: 'util/requestCapacityList' });
  }, []);

  useEffect(() => {
    if (urlParam.redirect === 'edit' && urlParam.skuId) {
      history.replace(`/service/cloud-package-sku/edit/${urlParam.skuId}`);
      return;
    }

    const form = formRef.current;
    const formData = { ...urlParam };
    if (formData.serviceTime && formData.serviceTimeUnit) {
      formData.serviceTime = formData.serviceTimeUnit;
      delete formData.serviceTimeUnit;
    }
    form?.setFieldsValue(formData);
    setListParam({
      ...transferListFormToListParam(urlParam),
    });
  }, [formRef, urlParam]);

  useEffect(() => {
    if (listParam) {
      requestSkuList(listParam);
    }
  }, [listParam]);

  return (
    <>
      <ProTable<ProductSku>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        formRef={formRef}
        loading={loading}
        rowSelection={{
          onChange: (selectedRowKeys) =>
            setSelectedRowKeys(selectedRowKeys as Array<number | string>),
        }}
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        options={{
          reload: () => {
            if (listParam) {
              setListParam({ ...listParam });
            }
          },
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editSku(0)}
            >
              新增
            </Button>,
            <Dropdown
              key="overlay"
              menu={{
                items: batchOperationItemList,
                onClick: ({ key }) => batchOperations[key as BatchTypes](),
              }}
            >
              <Button>
                批量操作
                <DownOutlined
                  style={{
                    marginLeft: 8,
                  }}
                />
              </Button>
            </Dropdown>,
          ],
        }}
        pagination={{
          pageSize: paginator.limit,
          total: paginator.total,
          pageSizeOptions: antdUtils.defaultPageSizeOptions,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      {selectedSkuId ? (
        <OperationHistory
          skuId={selectedSkuId}
          showModal
          onClose={onModalClosed}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default List;
