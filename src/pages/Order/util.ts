// import { OrderDetail, OrderParam } from '@/models/order/interface';
// import { OrderForm } from './interface';

import { OrderRefundStatus } from './interface';

export const orderRefundStatusObj: { [key in OrderRefundStatus]: string } = {
  [OrderRefundStatus.NONE]: '未发起退款',
  [OrderRefundStatus.REFUND_SUCCESS]: '非订阅退款成功',
  [OrderRefundStatus.SUB_REFUND_SUCCESS]: '订阅退款成功',
};

// export const initialOrderForm: OrderForm = {};

// 将formData转换为param
// export const transferFormDataToParam = (formData: OrderForm, id?: number): OrderParam => {
//   const param: OrderParam = {
//   };
//   id && (param.id = id);
//   return param;
// };

// 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: OrderDetail): OrderForm => {
//   const formData: OrderForm = {
//   };
//   return formData;
// };
