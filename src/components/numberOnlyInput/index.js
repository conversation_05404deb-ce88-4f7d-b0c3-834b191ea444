import { Input } from 'antd';

export default (props) => {
  const {
    disabled,
    placeholder = '只能输入数字',
    addonBefore,
    addonAfter,
    value,
    onNumberChange,
  } = props;

  const isNumberKey = (keyCode) => keyCode >= 48 && keyCode <= 57;
  const isDeleteKey = (keyCode) => keyCode === 8;
  const isSpaceKey = (keyCode) => keyCode === 32;

  const keyArray = (value && value.split('')) || [];
  const handleInputChange = (key, keyCode) => {
    console.log({ key, keyCode });
    if (!isSpaceKey(keyCode)) {
      if (isNumberKey(keyCode)) {
        keyArray.push(key);
      }
      if (isDeleteKey(keyCode)) {
        keyArray.splice(keyArray.length - 1, 1);
      }
      onNumberChange && onNumberChange(keyArray.join(''));
    }
  };

  let timeout = null;
  const handleDebounce = (e) => {
    const { key, keyCode } = e;
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
      return;
    }
    timeout = setTimeout(() => {
      handleInputChange(key, keyCode);
    }, 100);
  };

  return (
    <Input
      value={value}
      disabled={disabled}
      placeholder={placeholder}
      addonBefore={addonBefore}
      addonAfter={addonAfter}
      onKeyUp={(e) => {
        e.persist();
        handleDebounce(e);
      }}
    />
  );
};
