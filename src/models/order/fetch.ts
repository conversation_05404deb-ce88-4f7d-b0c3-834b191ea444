import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import { orderApiOption } from './api';
import { Order, OrderListParam } from './interface';

// 获取Order列表
export const fetchOrderList = (
  param?: OrderListParam,
): Promise<Data<Order>> => {
  const config = orderApiOption.orderList;
  if (param) config.option.params = param;
  return request(config.url, config.option);
};

export const fetchOrderListExport = (
  param?: Omit<OrderListParam, 'limit' | 'offset'>,
): Promise<string> => {
  const config = orderApiOption.orderListExport;
  if (param) config.option.params = param;
  return request(config.url, config.option);
};

export const fetchCompleteOrder = (
  orderId: string,
): Promise<ApiSuccessEnum> => {
  const config = orderApiOption.completeOrder;
  config.option.params = { orderId };
  return request(config.url, config.option);
};
