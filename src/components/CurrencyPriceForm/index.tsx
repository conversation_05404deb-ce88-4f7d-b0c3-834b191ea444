import { basicCurrency } from '@/models/currency/util';
import { ProCard } from '@ant-design/pro-components';
import { Col, Form, Input, InputNumber, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { CurrencyPrice } from './interface';
import { initCurrencyPrice } from './util';

interface Props {
  formItems?: ('price' | 'linePrice')[];
  value?: CurrencyPrice;
  onChange?: (value: CurrencyPrice) => void;
  areaCodeList?: string[];
}

const CurrencyPriceForm: React.FC<Props> = ({
  formItems = ['price', 'linePrice'],
  value,
  onChange,
}: Props) => {
  const formItemLayout = {
    wrapperCol: { span: 24 },
    labelCol: { span: 24 },
  };
  const colSpanConfig = {
    xs: 12,
    sm: 12,
    md: 6,
    lg: 6,
    xl: 4,
    xxl: 2,
  };
  // const [otherCurrencyPriceList, setOtherCurrencyPriceList] = useState<
  //   OtherCurrencySkuPrice[]
  // >([]);
  const [currencyPrice, setCurrencyPrice] =
    useState<CurrencyPrice>(initCurrencyPrice);

  const triggerValueChange = (_value: CurrencyPrice) => {
    onChange?.(_value);
    setCurrencyPrice(_value);
  };

  const onSelectorChange = (type: 'price' | 'linePrice', _value: number) => {
    const newCurrency: CurrencyPrice = {
      ...currencyPrice,
      [type]: _value,
    };
    triggerValueChange(newCurrency);
  };

  useEffect(() => {
    if (!value || value.price === undefined || value.price === null) {
      // 说明创建时首次进入
      return;
    }
    const _currencyPrice = value || initCurrencyPrice;
    setCurrencyPrice(_currencyPrice);
    // if (_currencyPrice.priceList && _currencyPrice.priceList.length > 0) {
    //   setShowOtherCurrencyPriceList(true);
    // }
  }, [value]);

  return (
    <section>
      <ProCard title="基准货币价格" colSpan={24}>
        <Row gutter={16} align={'middle'}>
          <Col {...colSpanConfig}>
            <Form.Item label="货币代码" {...formItemLayout}>
              <Input disabled value={basicCurrency.currencyCode} />
            </Form.Item>
          </Col>
          <Col {...colSpanConfig}>
            <Form.Item label="货币名称" {...formItemLayout}>
              <Input disabled value={basicCurrency.currencyName} />
            </Form.Item>
          </Col>
          <Col {...colSpanConfig}>
            <Form.Item label="货币符号" {...formItemLayout}>
              <Input disabled value={basicCurrency.currencySymbol} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="价格(元)" {...formItemLayout} required>
              <InputNumber
                controls={false}
                wheel={false}
                placeholder="请输入价格(元)"
                min={0}
                value={currencyPrice.price}
                onChange={(value) => onSelectorChange('price', value || 0)}
              />
            </Form.Item>
          </Col>
          {formItems.includes('linePrice') && (
            <Col span={8}>
              <Form.Item label="划线价格(元)" {...formItemLayout} rules={[]}>
                <InputNumber
                  controls={false}
                  wheel={false}
                  min={currencyPrice.price || 0}
                  placeholder="请输入划线价(元)"
                  value={currencyPrice.linePrice}
                  onChange={(value) =>
                    onSelectorChange('linePrice', value || 0)
                  }
                />
              </Form.Item>
            </Col>
          )}
        </Row>
      </ProCard>
    </section>
  );
};

export default CurrencyPriceForm;
