import { PayTypeEnum } from '../order/interface';
import { ReNewEnum, RecordTypeEnum } from '../product/interface';

export enum ServiceSkuStatusEnum {
  OUT_DATE = 0,
  EFFECTING = 1,
  WAIT_EFFECT = 2,
  DELETED = 3,
}

export enum ServicePlanStatusEnum {
  // 生效中
  EFFECTING = 1,
  // 已过期
  OUT_DATE = 2,
  // 待生效
  WAIT_EFFECT = 3,
}

export enum ServicePlanStatusV2Enum {
  // 已过期
  OUT_DATE = 1,
  // 生效中
  EFFECTING = 2,
  // 待生效
  WAIT_EFFECT = 3,
}

export enum ServicePlanRefundStateEnum {
  /***
   * 退款成功
   */
  REFUND_SUCCEED = 'REFUND_SUCCEED',
  /***
   * 退款失败
   */
  REFUND_FAILED = 'REFUND_FAILED',
  /**
   * 退款中
   */
  REFUNDING = 'REFUNDING',
}

export enum ServicePlanChargeTypeEnum {
  /**
   * 免费（活动）
   */
  FREE = 'FREE',
  /**
   * 充值
   */
  CHARGE = 'CHARGE',
  /**
   * 转移服务
   */
  TRANSFER = 'TRANSFER',
  /**
   * AI共创活动奖励奖励
   */
  REWARD_AI_CO_CREATION = 'REWARD_AI_CO_CREATION',
  /**
   * 上传素材活动奖励
   */
  REWARD_UPLOAD_RESOURCE = 'REWARD_UPLOAD_RESOURCE',
}

// 服务管理Sku列表数据
export interface ServiceSku {
  id: number;
  skuId: number;
  shortName: string;
  capacity: string;
  cloudStorage: RecordTypeEnum;
  cycle: number;
  serviceDuration: string;
  autoPay: ReNewEnum;
  // 0已过期 1生效中 2待生效
  skuStatus: ServiceSkuStatusEnum;
  orderId: string;
  timeZone: number;
  createdAt: string;
  indateStr: string;
  zoneId: string;
}

// 服务管理Plan列表数据
export interface ServicePlan {
  id: number;
  skuId: number;
  shortName: string;
  capacities: { type: string; cycleTime: string; name: string }[];
  chargeType: ServicePlanChargeTypeEnum;
  serviceDuration: string;
  autoPay: number;
  skuStatus: ServicePlanStatusV2Enum;
  orderId: string;
  zoneOffset: number;
  createdAt: string;
  indate: number;
  indateStr: string;
  payWay: PayTypeEnum;
  tradeNo: string;
  refundState: ServicePlanRefundStateEnum;
  zoneId: string;
  workTime: number;
  workTimeStr: string;
}

export interface UpdatingSkuExpirationParam {
  id: number;
  date: number;
}

export interface UpdatingSkuExpirationParamV2 {
  planId: number;
  expiration: number;
  deviceType: string;
  changeReason: string;
}
