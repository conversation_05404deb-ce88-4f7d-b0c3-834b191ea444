import { ApiSuccessEnum } from '@/models/common.interface';
import {
  deviceList,
  saleStatusName,
  spanConfig,
  transferArrayToObject,
} from '@/models/common.util';
import {
  fetchBenefitCompareList,
  fetchBenefitCompareStatusUpdate,
} from '@/models/product/fetch';
import {
  BenefitCompare,
  BenefitCompareListParam,
} from '@/models/product/interface';
import { initBenefitCompareListParam } from '@/models/product/util';
import { antdUtils } from '@/utils/antd.util';
import { initPaginator, Paginator } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, message, Switch } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<BenefitCompare[]>([]);
  const [listParam, setListParam] = useState<BenefitCompareListParam>(
    initBenefitCompareListParam,
  );
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const [loading, setLoading] = useState(false);

  // 获取列表数据
  const requestBenefitCompareList = async (param: BenefitCompareListParam) => {
    console.log('requestBenefitCompareList', param);
    setLoading(true);
    try {
      const { items, ...rest } = await fetchBenefitCompareList(param);
      setPagination(rest);
      setDataList(items);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 修改状态
  const requestBenefitCompareStatusUpdate = async (
    row: BenefitCompare,
    status: boolean,
  ) => {
    const result = await fetchBenefitCompareStatusUpdate({
      id: row.id,
      status,
    });
    if (result === ApiSuccessEnum.success) {
      message.success(`${status ? '启用' : '禁用'}成功!`);
      setListParam({ ...listParam });
    }
  };

  // 编辑
  const editBenefitCompare = (id: number) => {
    history.push(`/service/benefit-compare/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    const formData = form?.getFieldsValue();
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          console.log(formData);
          setListParam({ ...listParam, ...formData });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam(initBenefitCompareListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;
    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<BenefitCompare>> = [
    {
      dataIndex: 'deviceType',
      title: '设备名称',
      valueType: 'select',
      valueEnum: transferArrayToObject(deviceList),
    },
    {
      dataIndex: 'image',
      title: '配图',
      valueType: 'image',
      search: false,
    },
    {
      dataIndex: 'status',
      title: '状态',
      valueType: 'select',
      valueEnum: () => {
        return { 0: saleStatusName[0], 1: saleStatusName[1] };
      },
      render: (_, row) => (
        <Switch
          checked={row.status}
          unCheckedChildren={saleStatusName[0]}
          checkedChildren={saleStatusName[1]}
          onChange={(ev) => requestBenefitCompareStatusUpdate(row, ev)}
        />
      ),
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 180,
      search: false,
      render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      dataIndex: 'updateTime',
      title: '修改时间',
      width: 180,
      search: false,
      render: (_, row) => dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      dataIndex: 'action',
      title: '操作',
      search: false,
      render: (_, row) => (
        <>
          <Button type="link" onClick={() => editBenefitCompare(row.id)}>
            编辑
          </Button>
        </>
      ),
    },
  ];

  useEffect(() => {
    requestBenefitCompareList(listParam);
  }, [listParam]);

  return (
    <ProTable<BenefitCompare>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      loading={loading}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editBenefitCompare(0)}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        ...antdUtils.transferPaginatorToTablePagination(pagination),
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
