import { history, useParams } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
/**
 * 权益表编辑页面 - 统一处理创建和编辑逻辑
 */

import {
  initialBenefitTableFormData,
  transferDetailToFormData,
  transferFormDataToParam,
  valideDefaultValue,
} from '../util';

import {
  fetchBenefitCreate,
  fetchBenefitDetail,
  fetchBenefitUpdate,
} from '@/api/benefit/fetch';
import { benefitSuitableDeviceTypeOptions } from '@/api/benefit/utils';
import useUrlState from '@ahooksjs/use-url-state';
import { Card, Select, Switch } from 'antd';
import { BenefitFormData, UrlParam } from '../interface';
// import BenefitForm from './components/BenefitForm';
import BenefitForm from './components/BenefitForm';

const BenefitEdit: React.FC = () => {
  const urlRestParam = useParams<{ id: string }>();
  const isEdit = !!urlRestParam.id;
  const [form] = Form.useForm<BenefitFormData>();
  const [loading, setLoading] = useState(false);

  // 获取URL参数
  const [urlParams] = useUrlState<UrlParam>();

  // 计算表单初始值 - 根据URL参数中的deviceType设置初始值
  const getFormInitialValues = (): BenefitFormData => {
    const baseInitialValues = { ...initialBenefitTableFormData };

    // 如果URL参数中存在deviceType且不为'ALL'，设置为初始值
    if (urlParams?.deviceType && urlParams.deviceType !== 'ALL') {
      baseInitialValues.deviceTypeList = [urlParams.deviceType as any];
    }

    return baseInitialValues;
  };

  // 判断是否应该禁用设备类型选择器
  const isDeviceTypeDisabled =
    urlParams?.deviceType && urlParams.deviceType !== 'ALL';

  // 加载权益表详情
  const loadBenefitDetail = async (groupId: string) => {
    if (!isEdit) return;

    try {
      setLoading(true);
      const detail = await fetchBenefitDetail(groupId);

      // 使用工具函数处理数据转换
      const formData = transferDetailToFormData(detail);

      console.log(formData);
      // // 填充表单数据，包括预设值
      form.setFieldsValue(formData);

      // 设置本地化配置 - 暂时为空，后续可以根据实际数据结构转换
      // setLocaleContentList([]);
    } catch (error: any) {
      message.error(`加载权益表详情失败: ${error.message}`);
      // history.back();
    } finally {
      setLoading(false);
    }
  };

  // 保存权益表 - 方法名使用camelCase，统一处理创建和编辑逻辑
  const handleSave = async (values: any) => {
    try {
      const { isInvalid, errorInfo } = valideDefaultValue(values);
      // 验证预设值
      if (isInvalid) {
        message.error(errorInfo);
        return;
      }

      // 构建提交参数 - 合并主表单数据和本地化配置数据
      const param = transferFormDataToParam(values, urlRestParam.id);

      console.log('submitData', param, values);
      // return;

      setLoading(true);

      if (isEdit) {
        await fetchBenefitUpdate(param);
        message.success('更新成功');
      } else {
        await fetchBenefitCreate(param);
        message.success('创建成功');
      }

      // history.push('/service/benefit');
      history.back();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(urlRestParam.id);
    if (!urlRestParam.id) return;
    loadBenefitDetail(urlRestParam.id);
  }, [urlRestParam]);

  // 处理URL参数变化，更新表单初始值
  useEffect(() => {
    // 只在非编辑模式下处理URL参数
    if (!isEdit && urlParams?.deviceType && urlParams.deviceType !== 'ALL') {
      const currentValues = form.getFieldsValue();
      form.setFieldsValue({
        ...currentValues,
        deviceTypeList: [urlParams.deviceType as any],
      });
    }
  }, [urlParams?.deviceType, isEdit, form]);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* 主表单区域 */}
        <Card
          title={isEdit ? '编辑权益表' : '新建权益表'}
          className="mb-6 shadow-sm"
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            className="grid grid-cols-1 gap-6"
            initialValues={getFormInitialValues()}
          >
            {/* 适用设备 */}
            <Form.Item
              name="deviceTypeList"
              label="适用设备"
              rules={[{ required: true, message: '请选择适用设备' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择适用设备"
                options={benefitSuitableDeviceTypeOptions}
                disabled={isDeviceTypeDisabled}
              />
            </Form.Item>

            <Form.Item
              name="isCore"
              label="是否核心权益"
              required
              valuePropName="checked"
            >
              <Switch unCheckedChildren="否" checkedChildren="是" />
            </Form.Item>

            {/* 使用封装的表单组件 */}
            <BenefitForm form={form} />
          </Form>
        </Card>

        {/* 操作按钮区域 */}
        <div className="mt-6 flex justify-center space-x-4">
          <Button onClick={history.back}>取消</Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            loading={loading}
          >
            保存
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BenefitEdit;
