import { Effect, Reducer } from '@umijs/max';
import { SelectOption } from '../common.interface';
import { fetchActivityContentInfo } from './fetch';

export interface PackageState {
  activityContentInfo?: { [key: string]: string };
  activityContentOptionList: SelectOption[];
}

export const initPackageState: PackageState = {
  activityContentOptionList: [],
};

export interface PackageModel {
  namespace: 'package';
  state: PackageState;
  effects: {
    requestActivityContentInfo: Effect;
  };
  reducers: {
    requestActivityContentInfoSuccess: Reducer<
      PackageState,
      {
        type: 'requestActivityContentInfoSuccess';
        payload: { [key: string]: string };
      }
    >;
  };
}

const packageModel: PackageModel = {
  namespace: 'package',
  state: initPackageState,
  effects: {
    *requestActivityContentInfo(action, { call, put }) {
      const packageList: { [key: string]: string } = yield call(
        fetchActivityContentInfo,
      );
      yield put({
        type: 'requestActivityContentInfoSuccess',
        payload: packageList,
      });
    },
  },
  reducers: {
    requestActivityContentInfoSuccess(
      state = initPackageState,
      { payload },
    ): PackageState {
      const options = Object.entries(payload).map(([key, value]) => ({
        value: key,
        label: value,
      }));
      return {
        ...state,
        activityContentInfo: payload,
        activityContentOptionList: options,
      };
    },
  },
};

export default packageModel;
