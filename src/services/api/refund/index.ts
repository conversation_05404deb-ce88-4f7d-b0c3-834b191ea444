import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const refundApiOption: Record<
  | 'refundPaymentInfo'
  | 'refund'
  | 'refundInfoById'
  | 'refundDetail'
  | 'cancelSubscription',
  RequestOption
> = {
  // 获取订单余额信息
  refundPaymentInfo: {
    url: '/adm/bs/refund/getPaymentInfo/:orderId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 退款
  refund: {
    url: '/adm/bs/refund',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据退款ID查询退款
  refundInfoById: {
    url: '/adm/bs/refund/query/:refundId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取退款详情
  refundDetail: {
    url: '/adm/bs/refund/detail/:orderId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  cancelSubscription: {
    url: '/adm/bs/subscription/unSign',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
