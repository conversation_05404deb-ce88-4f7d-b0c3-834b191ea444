import { RequestMethod, RequestOption } from '@mantas/request';

export const materialApiOption: Record<
  | 'materialList'
  | 'materialCreation'
  | 'materialUpdate'
  | 'materialDetail'
  | 'materialDeletion',
  RequestOption
> = {
  // 获取Material列表
  materialList: {
    url: '/material/list',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 创建Material
  materialCreation: {
    url: '/material',
    option: {
      method: RequestMethod.Post,
    },
  },
  // 更新Material
  materialUpdate: {
    url: '/material',
    option: {
      method: RequestMethod.Put,
    },
  },
  // 根据Material的id获取详情数据
  materialDetail: {
    url: '/material',
    option: {
      method: RequestMethod.Get,
    },
  },
  // 删除Material
  materialDeletion: {
    url: '/material',
    option: {
      method: RequestMethod.Delete,
    },
  },
};
