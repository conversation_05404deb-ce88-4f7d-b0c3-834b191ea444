import { ApiSuccessEnum } from '@/models/common.interface';
import {
  Event,
  EventListParam,
  EventUpdateParam,
} from '@/models/WarnEvent/interface';
import { eventApiOption } from '@/services/api/WarnEvent';
import request, { Data } from '@/utils/request';

export const fetchEventList = (
  param?: EventListParam,
): Promise<Data<Event>> => {
  const config = eventApiOption.eventList;
  param && (config.option.params = param);
  return request(config.url, config.option);
};

export const fetchEventUpdating = (
  param: EventUpdateParam,
): Promise<ApiSuccessEnum> => {
  const config = eventApiOption.updateStatus;
  config.option.params = param;
  return request(config.url, config.option);
};
