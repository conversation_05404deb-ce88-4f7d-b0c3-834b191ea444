import { spanConfig } from '@/models/common.util';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { fetchAssociableProductSkuList } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
} from '@/models/product/interface';
import { Paginator, initPaginator } from '@/utils/request';
import { BaseQueryFilterProps, ProTable } from '@ant-design/pro-components';
import { Button, Modal, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { deviceSkuAssociationTableColumns } from './util';

interface Props {
  /** Modal是否可见 */
  visible: boolean;
  /** 设备类型 */
  deviceType: SuitableDeviceTypeEnum;
  /** 关联参数 */
  relationParam?: RelationProductSkuListParam;
  /** 确认选择SKU的回调 */
  onConfirm: (deviceType: SuitableDeviceTypeEnum, sku: ProductSku) => void;
  /** 取消的回调 */
  onCancel: () => void;
  /** Modal标题 */
  title?: string;
}

const DeviceSkuAssociationModal: React.FC<Props> = ({
  visible,
  deviceType,
  relationParam,
  onConfirm,
  onCancel,
  title = '关联SKU',
}) => {
  const [dataList, setDataList] = useState<ProductSku[]>([]);
  const [listParam, setListParam] = useState<RelationProductSkuListParam>();
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [selectedSkuIds, setSelectedSkuIds] = useState<number[]>([]);
  const [selectedSku, setSelectedSku] = useState<ProductSku>();
  const [loading, setLoading] = useState(false);

  // 初始化查询参数
  const initializeParam = () => {
    if (relationParam) {
      setListParam(relationParam);
    } else {
      // 默认参数，按设备类型筛选
      const defaultParam: RelationProductSkuListParam = {
        limit: 20,
        offset: 0,
        payload: {
          uniteDeviceTypes: [deviceType],
          uniteCapacities: [],
        },
      };
      setListParam(defaultParam);
    }
  };

  // 请求SKU列表
  const requestSkuList = async (param: RelationProductSkuListParam) => {
    try {
      setLoading(true);
      const { items, ...rest } = await fetchAssociableProductSkuList(param);
      setDataList(items);
      setPaginator(rest);
    } catch (error) {
      message.error('获取SKU列表失败');
      console.error('Failed to fetch SKU list:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setSelectedSkuIds([]);
    setSelectedSku(undefined);
    onCancel();
  };

  // 处理确认
  const handleConfirm = () => {
    if (!selectedSku) {
      message.warning('请先选择需要关联的SKU');
      return;
    }
    onConfirm(deviceType, selectedSku);
    handleCancel();
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue() || {};
          const { id, name, aliasName } = formData;
          if (listParam) {
            const _param: RelationProductSkuListParam = {
              ...listParam,
              offset: 0, // 重置分页
              payload: {
                ...listParam.payload,
                skuId: id,
                skuName: name,
                skuAlias: aliasName,
              },
            };
            setListParam(_param);
          }
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          initializeParam();
        }}
      >
        重置
      </Button>,
    ];
  };

  // 分页变化处理
  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;

    let index = page;
    // 如果分页大小不等于列表参数的限制，则设置为1
    if (pageSize !== listParam.limit) {
      index = 1;
    }

    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  // 选择变化处理
  const onSelectChanged = (selectedRowKeys: React.Key[]) => {
    setSelectedSkuIds(selectedRowKeys as number[]);
  };

  // 监听visible变化，初始化参数
  useEffect(() => {
    if (visible) {
      initializeParam();
      setSelectedSkuIds([]);
      setSelectedSku(undefined);
    }
  }, [visible, deviceType, relationParam]);

  // 监听参数变化，请求数据
  useEffect(() => {
    if (listParam && visible) {
      requestSkuList(listParam);
    }
  }, [listParam, visible]);

  // 监听选择变化，设置选中的SKU
  useEffect(() => {
    const sku = dataList.find((item) => item.id === selectedSkuIds[0]);
    setSelectedSku(sku);
  }, [selectedSkuIds, dataList]);

  const footer = (
    <Row justify="center">
      <Button type="primary" onClick={handleConfirm} loading={loading}>
        确认关联
      </Button>
      <Button onClick={handleCancel} style={{ marginLeft: 8 }}>
        取消
      </Button>
    </Row>
  );

  return (
    <Modal
      title={`${title} - 设备类型: ${deviceType}`}
      open={visible}
      width={1200}
      footer={footer}
      onCancel={handleCancel}
      destroyOnClose
      maskClosable={false}
    >
      <ProTable<ProductSku>
        dataSource={dataList}
        columns={deviceSkuAssociationTableColumns}
        loading={loading}
        defaultSize="small"
        rowKey="id"
        rowSelection={{
          type: 'radio',
          selectedRowKeys: selectedSkuIds,
          onChange: onSelectChanged,
        }}
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: deviceSkuAssociationTableColumns
            .filter((col) => col.width)
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: paginator.limit,
          total: paginator.total,
          current: Math.trunc(paginator.offset / paginator.limit) + 1,
          showQuickJumper: true,
          showSizeChanger: true,
          onChange: onPaginationChanged,
          onShowSizeChange: onPaginationChanged,
        }}
        options={false}
        toolBarRender={false}
      />
    </Modal>
  );
};

export default DeviceSkuAssociationModal;
