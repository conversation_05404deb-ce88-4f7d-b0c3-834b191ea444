import { ApiSuccessEnum } from '@/models/common.interface';
import { fetchDeviceInfo } from '@/models/device/fetch';
import { DeviceInfo, DeviceInfoParam } from '@/models/device/interface';
import { fetchServiceTransferringV2 } from '@/models/service/fetch';
import { SearchOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Button, Empty, Form, Input, Modal, Space, message } from 'antd';
import { isFunction } from 'lodash';
import React, { useState } from 'react';

interface Props {
  deviceType: string;
  serviceId: number;
  showModal: boolean;
  currentUserId: number;
  onClose?: (shouldRefresh?: boolean) => void;
}

interface FormData {
  sn: string;
}

const initFormData: FormData = {
  sn: '',
};

const ServiceTransferringV2: React.FC<Props> = ({
  showModal,
  serviceId,
  onClose,
  currentUserId,
  deviceType,
}: Props) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>();

  const submit = (ev: FormData) => {
    if (!deviceInfo) {
      message.warning('请回车或者点击输入框结尾的放大镜进行设备搜索');
      return;
    }

    const userId = deviceInfo.userId;

    if (!userId || !deviceInfo.sn) {
      message.warning('请先提供一个合法的SN号！');
      return;
    }

    Modal.confirm({
      title: '转移确认',
      content: `确认转移给用户ID为${userId}的用户么？`,
      onOk: async () => {
        setLoading(true);
        try {
          let result = await fetchServiceTransferringV2(
            // 该处userId要传转移时的userId
            // 【【Server】操作服务转移，h3/service/transfer接口报错
            // https://www.tapd.cn/22993481/bugtrace/bugs/view?bug_id=1122993481001056912
            {
              serviceId,
              transfereeSn: ev.sn,
              userId: currentUserId,
              deviceType: deviceType,
            },
          );
          if (result === ApiSuccessEnum.success) {
            message.success('转移成功！');
            if (isFunction(onClose)) onClose(true);
          }
        } catch (e) {
          console.log(e);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const requestDeviceInfo = async (sn: string) => {
    const param: DeviceInfoParam = {
      sn,
      deviceType,
    };
    const result = await fetchDeviceInfo(param);
    // console.log(result);
    return result;
  };

  const onSnSearched = async (ev?: React.KeyboardEvent<HTMLInputElement>) => {
    if (ev) ev.preventDefault();
    const sn = form.getFieldValue('sn');
    if (!sn) {
      setDeviceInfo(undefined);
      return;
    }
    const result = await requestDeviceInfo(sn);
    // console.log(result);
    setDeviceInfo(result);
  };

  return (
    <Modal
      width={600}
      destroyOnClose
      open={showModal}
      footer={null}
      title="转移服务"
      onCancel={() => isFunction(onClose) && onClose()}
    >
      <Form form={form} onFinish={submit} initialValues={initFormData}>
        <Form.Item
          name="sn"
          label="设备SN码"
          extra={
            // (skuDetail && skuDetail.shareUrl) ||
            // skuErrorInfo && (
            //   <ProCard bordered>
            //     <div className={styles.skuError}>
            //       <CloseCircleFilled className={styles.skuErrorIcon} />
            //       {skuErrorInfo}
            //     </div>
            //   </ProCard>
            deviceInfo ? (
              <ProCard bordered>
                {deviceInfo.deviceName || deviceInfo.userId ? (
                  <>
                    <div style={{ color: '#333', marginBottom: 12 }}>
                      用户ID：{deviceInfo.userId}
                    </div>
                    <div style={{ color: '#333' }}>
                      设备名称：{deviceInfo.deviceName || '-'}
                    </div>
                  </>
                ) : (
                  <Empty />
                )}
              </ProCard>
            ) : (
              '输入完后，请回车或者点击输入框结尾的放大镜进行设备搜索'
            )
          }
        >
          <Input
            placeholder="请输入设备SN码"
            onChange={() => setDeviceInfo(undefined)}
            onPressEnter={onSnSearched}
            allowClear
            suffix={<SearchOutlined onClick={() => onSnSearched()} />}
          />
        </Form.Item>

        <Form.Item style={{ textAlign: 'right' }}>
          <Space>
            <Button
              htmlType="button"
              type="primary"
              loading={loading}
              disabled={loading}
              onClick={form.submit}
            >
              提交
            </Button>
            <Button
              type="default"
              htmlType="button"
              onClick={() => {
                form.resetFields();
                if (isFunction(onClose)) onClose();
              }}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ServiceTransferringV2;
