/**
 * 权益表相关的类型定义
 */

import { StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';
import { BenefitValueTypeEnum } from './utils';

// 权益信息接口
export interface BenefitInfo {
  groupId: string;
  name: string;
  alias?: string;
  icon: string;
  description: string;
  image: string;
  introduction: string;
  isCoreBenefit: number;
  sort: number;
  createTime: string;
  updateTime?: string;
  deviceType?: string;
  attributes: BenefitAttribute[];
  value?: string;
  status?: number;
}

// 权益属性信息
export interface BenefitAttribute {
  benefitId: number;
  isDefaultAttribute: StatusEnum;
  attributeType: BenefitValueTypeEnum;
  attributeText: string;
  attributeSelectedText: string;
}

// 权益列表查询参数
export interface BenefitListParam extends PaginatorParam {
  name?: string;
  deviceType?: string;
}

// 权益详情
export interface BenefitDetail extends BenefitInfo {
  translations?: BenefitTranslation[];
}

// 权益翻译信息
export interface BenefitTranslation {
  language: string;
  name: string;
  description: string;
  introduction: string;
  image: string;
  attributes: BenefitAttribute[];
}

export interface BenefitAttributeParam {
  benefitId?: number;
  isDefaultAttribute: StatusEnum;
  attributeType: BenefitValueTypeEnum;
  attributeText: string;
  attributeSelectedText?: string;
}

// 权益创建参数
export interface BenefitSaveParam {
  groupId?: string;
  name: string;
  alias: string;
  icon?: string;
  description: string;
  image: string;
  introduction: string;
  isCoreBenefit: StatusEnum;
  supportedDeviceTypes: string[];
  attributes: BenefitAttributeParam[];
  translations?: BenefitTranslation[];
}

// 权益更新参数
export interface BenefitUpdateParam extends BenefitSaveParam {
  groupId: string;
}

// 权益重排序参数项
export interface BenefitReorderItem {
  groupId: string;
  sort: number;
}

// 权益重排序参数
export interface BenefitReorderParam {
  deviceType: string;
  sortList: BenefitReorderItem[];
}

// 权益删除参数
export interface BenefitDeleteParam {
  groupId: string;
}
