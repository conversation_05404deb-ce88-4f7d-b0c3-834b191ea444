import { spanConfig } from '@/models/common.util';
import { fetchAssociableProductSkuList } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
} from '@/models/product/interface';
import { initPagination, Pagination } from '@/utils/request';
import { ProTable } from '@ant-design/pro-components';
import { BaseQueryFilterProps } from '@ant-design/pro-form';
import { Button, message, Modal, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { associateSkuTableColumns } from '../util';

interface Props {
  open: boolean;
  param: RelationProductSkuListParam;
  onOk: (sku: ProductSku) => void;
  onCancel: Function;
}

const SkuSelector: React.FC<Props> = ({
  param,
  open,
  onOk,
  onCancel,
}: Props) => {
  const [dataList, setDataList] = useState<ProductSku[]>([]);
  const [listParam, setListParam] =
    useState<RelationProductSkuListParam>(param);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [selectedSkuIds, setSelectedSkuIds] = useState<number[]>([]);
  const [selectedSku, setSelectedSku] = useState<ProductSku>();

  useEffect(() => {
    requestAssociateProductSkuList(listParam);
  }, [listParam]);

  useEffect(() => {
    const sku = dataList.find((item) => item.id === selectedSkuIds[0]);
    setSelectedSku(sku);
  }, [selectedSkuIds, dataList]);

  const requestAssociateProductSkuList = async (
    _param: RelationProductSkuListParam,
  ) => {
    const { items, ...rest } = await fetchAssociableProductSkuList(_param);
    setDataList(items);
    setPagination(rest);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue() || {};
          const { id, name, aliasName, saleStatus } = formData;
          const _param: RelationProductSkuListParam = {
            ...param,
            payload: {
              ...param.payload,
              skuId: id,
              skuName: name,
              skuAlias: aliasName,
              saleStatus,
            },
          };
          setListParam({
            ..._param,
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({ ...param });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const onConfirm = () => {
    if (!selectedSku) {
      message.warning('请先选择需要关联的SKU');
      return;
    }
    onOk(selectedSku);
    onCanceled();
  };

  const onSelectChanged = (ev: Array<number | string>) => {
    setSelectedSkuIds(ev as number[]);
  };

  const onCanceled = () => {
    setSelectedSkuIds([]);
    onCancel();
  };

  const footer = (
    <Row justify="center">
      <Button type="primary" onClick={onConfirm}>
        提交
      </Button>
      <Button onClick={onCanceled}>取消</Button>
    </Row>
  );

  return (
    <Modal
      destroyOnClose
      width={1000}
      open={open}
      footer={footer}
      onCancel={() => onCancel()}
    >
      <ProTable<ProductSku>
        dataSource={dataList}
        columns={associateSkuTableColumns.concat([
          {
            title: '状态',
            dataIndex: 'saleStatus',
            valueType: 'select',
            valueEnum: { 1: '上架', 0: '下架' },
            fixed: 'right',
            render: (_, row) =>
              row.saleStatus ? (
                <p style={{ color: '#3199F5' }}>上架</p>
              ) : (
                '下架'
              ),
          },
        ])}
        defaultSize="small"
        rowKey="id"
        rowSelection={{ type: 'radio', onChange: onSelectChanged }}
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: associateSkuTableColumns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          current: Math.trunc(pagination.offset / pagination.limit) + 1,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default SkuSelector;
