import { initCurrencyPrice } from '@/components/CurrencyPriceForm/util';
import { ActProp } from '@/models/actProp/interface';
import { StatusEnum } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  ActTimeEnum,
  ActTypeEnum,
  OperationPackageDetail,
  OperationPackageParam,
} from '@/models/package/interface';
import { ProductSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import { uuid } from '@/utils/uuid';
import dayjs from 'dayjs';
import { ImageConfig, ImageTypeEnum, OperationPackageForm } from './interface';

// const demoUrl =
//   'https://sandbox-img5.petkit.cn/post/2023/11/30/65685973b38ffb000e3a75faekjGhzL6o';

export const initImageConfig: ImageConfig = {
  [ImageTypeEnum.ACT]: {
    zh_CN: true,
  },
  [ImageTypeEnum.POP]: {},
};

export const imageTypeNames: { [key in ImageTypeEnum]: string } = {
  [ImageTypeEnum.ACT]: '活动',
  [ImageTypeEnum.POP]: '弹窗',
};

export const initialOperationPackageForm: OperationPackageForm = {
  deviceType: null,
  content: null,
  prop: null,
  actImage: {
    zh_CN: [],
    en_US: [],
  },
  popImage: {
    zh_CN: [],
    en_US: [],
  },
  priority: null,
  actTime: ActTimeEnum.CUSTOM,
  times: [],
  activityName: '',
  isNeedUpdateSku: StatusEnum.ENABLE,
  // 服务时长
  serviceTime: null,
  serviceTimeUnit: ServiceTimeUnitEnum.MONTH,
  activitySkuName: '',
  // 活动价格
  currencyPrice: initCurrencyPrice,
  // 活动角标
  activityCornerIcons: [],
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: OperationPackageForm,
  relativeSkuList: ProductSku[],
  actPropList: ActProp[],
  imageConfig: ImageConfig,
  id?: number,
): OperationPackageParam => {
  const param: OperationPackageParam = {
    payload: {
      deviceType: formData.deviceType || SuitableDeviceTypeEnum.D4sh,
      actPropId: formData.prop || 0,
      actProp:
        actPropList.find((item) => item.id === formData.prop)?.actProp || '',
      actContent: formData.content || '',
      priority: formData.priority || 0,
      actName: formData.activityName,
      relationSkuId: relativeSkuList[0].id,
      actTime: formData.actTime,
      // effectTime: null,
      // inEffectTime: null,
      imageParam: {
        popChineseImage: '',
        actChineseImage: '',
        popEnglishImage: '',
        actEnglishImage: '',
      },
      changeSku: !!formData.isNeedUpdateSku,
    },
  };

  if (imageConfig.act.zh_CN && formData.actImage?.zh_CN?.[0]?.url) {
    param.payload.imageParam.actChineseImage = formData.actImage.zh_CN[0].url;
  }
  if (imageConfig.act.en_US && formData.actImage?.en_US?.[0]?.url) {
    param.payload.imageParam.actEnglishImage = formData.actImage.en_US[0].url;
  }
  if (imageConfig.pop.zh_CN && formData.popImage?.zh_CN?.[0]?.url) {
    param.payload.imageParam.popChineseImage = formData.popImage.zh_CN[0].url;
  }
  if (imageConfig.pop.en_US && formData.popImage?.en_US?.[0]?.url) {
    param.payload.imageParam.popEnglishImage = formData.popImage.en_US[0].url;
  }
  // forIn<ImageConfig>(imageConfig, (value, key) => {
  //   forIn(value, (item, itemKey) => {
  //     if (item) {
  //       param.payload.imageParam[
  //         `${key}${itemKey === 'zh_US' ? 'Chinese' : 'English'}Image`
  //       ] = formData[`${key}Image`][itemKey][0].url;
  //     }
  //   });
  // });

  if (formData.actTime === ActTimeEnum.CUSTOM) {
    param.payload.effectTime = dayjs(formData.times[0]).valueOf();
    param.payload.inEffectTime = dayjs(formData.times[1]).valueOf();
  }

  if (formData.isNeedUpdateSku) {
    param.payload.actSkuName = formData.activitySkuName;
    param.payload.serviceTime = formData.serviceTime || 1;
    param.payload.serviceTimeUnit = formData.serviceTimeUnit;
    // param.payload.actPrice = formData.currencyPrice.price || 0;
    param.payload.actPrice = {
      price: formData.currencyPrice.price || 0,
      actPriceList: formData.currencyPrice.priceList || [],
    };
    param.payload.cornerMarkIcon = formData.activityCornerIcons[0]?.url || '';
  }

  // 处理地区
  // console.log(formData.regions);
  // if (formData.regions && formData.regions.length) {
  //   const allRegionCodeList = regionOptionList.map((item) => item.value);
  //   const areaList: AreaInfo[] = [];
  //   formData.regions
  //     .filter((code) => allRegionCodeList.includes(code))
  //     .forEach((code) => {
  //       const region = regionOptionList.find((ro) => ro.value === code);
  //       areaList.push({
  //         areaCode: `${region?.value || ''}`,
  //         areaName: region?.label || '',
  //       });
  //     });
  //   param.payload.areaList = areaList;
  // }

  if (id) param.payload.id = id;
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: OperationPackageDetail,
): OperationPackageForm => {
  const formData: OperationPackageForm = {
    deviceType: detail.deviceType,
    content: detail.actContent as ActTypeEnum,
    prop: detail.actPropId,
    priority: detail.priority,
    times: [],
    actTime: detail.actTimeEnum || ActTimeEnum.CUSTOM,
    activityName: detail.actName,
    // 服务时长
    serviceTime: detail.serviceTime,
    serviceTimeUnit: detail.serviceTimeUnit,
    activitySkuName: detail.actSkuName,
    // 活动价格
    currencyPrice: {
      price: detail.actPrice,
      priceList: detail.price.priceList || [],
    },
    // 活动角标
    activityCornerIcons: detail.cornerMarkIcon
      ? [
          {
            uid: uuid(),
            name: '活动角标',
            url: detail.cornerMarkIcon,
          },
        ]
      : [],
    isNeedUpdateSku: detail.changeSku,
    actImage: {
      zh_CN: detail.imageRet?.actChineseImage
        ? [
            {
              uid: uuid(),
              name: '中文活动配图',
              url: detail.imageRet.actChineseImage,
            },
          ]
        : [],
      en_US: detail.imageRet?.actEnglishImage
        ? [
            {
              uid: uuid(),
              name: '英文活动配图',
              url: detail.imageRet.actEnglishImage,
            },
          ]
        : [],
    },
    popImage: {
      zh_CN: detail.imageRet?.popChineseImage
        ? [
            {
              uid: uuid(),
              name: '中文活动配图',
              url: detail.imageRet.popChineseImage,
            },
          ]
        : [],
      en_US: detail.imageRet?.popEnglishImage
        ? [
            {
              uid: uuid(),
              name: '英文活动配图',
              url: detail.imageRet.popEnglishImage,
            },
          ]
        : [],
    },
  };
  if (detail.effectDate) {
    formData.times.push(dayjs(detail.effectDate));
  }
  if (detail.inEffectDate) {
    formData.times.push(dayjs(detail.inEffectDate));
  }
  return formData;
};
