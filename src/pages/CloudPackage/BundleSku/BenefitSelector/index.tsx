import { fetchBenefitList } from '@/api/benefit/fetch';
import { BenefitInfo, BenefitListParam } from '@/api/benefit/types';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';
import { spanConfig } from '@/models/common.util';
import { initBenefitListParam } from '@/models/product/util';
import { antdUtils } from '@/utils/antd.util';
import { initPaginator, Paginator } from '@/utils/request';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, message, Modal, Row, Space } from 'antd';
import { union, unionBy, uniq } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';

interface Props {
  open: boolean;
  selectedBenefits: BenefitInfo[];
  onOk: (benefitList: BenefitInfo[]) => void;
  onCancel: () => void;
}

const BenefitSelector: React.FC<Props> = ({
  open,
  selectedBenefits = [],
  onOk,
  onCancel,
}: Props) => {
  const [dataList, setDataList] = useState<BenefitInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(
    selectedBenefits.map((item) => item.groupId),
  );
  const [listParam, setListParam] = useState<BenefitListParam>({
    ...initBenefitListParam,
  });
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [selectedBenefitList, setSelectedBenefitList] =
    useState<BenefitInfo[]>(selectedBenefits);

  const onCancelled = () => {
    setSelectedRowKeys([]);
    onCancel();
  };

  const requestBenfitList = async (_listParam: BenefitListParam) => {
    try {
      const { items, ...rest } = await fetchBenefitList(_listParam);
      // 转换新的权益数据结构为旧的Benefit结构以保持兼容性
      setDataList(items);
      setPaginator(rest);
    } catch (error) {
      console.error('获取权益列表失败:', error);
      message.error('获取权益列表失败');
      setDataList([]);
      setPaginator(initPaginator);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue() || {};
          const { name } = formData;
          setListParam({
            ...initBenefitListParam,
            name,
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({ ...initBenefitListParam });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const onConfirm = () => {
    if (!selectedRowKeys || !selectedRowKeys.length) {
      message.warning('请先选择需要关联的SKU');
      return;
    }
    // console.log(selectedRowKeys, selectedBenefitList);
    onOk(selectedBenefitList);
    onCancelled();
  };

  const onTableRowSelected = useCallback(
    (benefit: BenefitInfo, isSelected: boolean) => {
      if (isSelected) {
        setSelectedRowKeys(uniq([...selectedRowKeys, benefit.groupId]));
        setSelectedBenefitList([...selectedBenefitList, { ...benefit }]);
      } else {
        const selectedRowKeyIndex = selectedRowKeys.findIndex(
          (rowKey) => rowKey === benefit.groupId,
        );
        selectedRowKeys.splice(selectedRowKeyIndex, 1);
        setSelectedRowKeys([...selectedRowKeys]);
        const selectedBenefitIndex = selectedBenefitList.findIndex(
          (bf) => bf.groupId === benefit.groupId,
        );
        selectedBenefitList.splice(selectedBenefitIndex, 1);
        setSelectedBenefitList([...selectedBenefitList]);
      }
    },
    [selectedRowKeys, selectedBenefitList],
  );

  const onAllTableRowSelected = useCallback(
    (isSelected: boolean, _: BenefitInfo[], changeRows: BenefitInfo[]) => {
      console.log(isSelected, changeRows);
      if (isSelected) {
        // 联合并去重
        setSelectedRowKeys(
          union(
            selectedRowKeys,
            changeRows.map((row) => row.groupId),
          ),
        );
        setSelectedBenefitList(unionBy(selectedBenefitList, changeRows, 'id'));
      } else {
        const currentRowKeyIndexs: number[] = [];
        const currentBenefitIndexs: number[] = [];
        changeRows.forEach((row) => {
          const rowKeyIndex = selectedRowKeys.findIndex(
            (rowkey) => rowkey === row.groupId,
          );
          currentRowKeyIndexs.push(rowKeyIndex);
          const benefitIndex = selectedBenefitList.findIndex(
            (benefit) => benefit.groupId === row.groupId,
          );
          currentBenefitIndexs.push(benefitIndex);
        });
        const _selectedRowKeys = selectedRowKeys.filter(
          (_, index) => !currentRowKeyIndexs.includes(index),
        );
        // currentRowKeyIndexs.forEach((index) => {
        //   selectedRowKeys.splice(index, 1);
        // });
        const _selectedBenefitList = selectedBenefitList.filter(
          (_, index) => !currentBenefitIndexs.includes(index),
        );
        // currentBenefitIndexs.forEach((index) => {
        //   selectedBenefitList.splice(index, 1);
        // });

        setSelectedRowKeys([..._selectedRowKeys]);
        setSelectedBenefitList([..._selectedBenefitList]);
      }
    },
    [dataList, selectedBenefitList, selectedRowKeys],
  );

  useEffect(() => {
    requestBenfitList(listParam);
  }, [listParam]);

  const columns: ProColumns<BenefitInfo>[] = [
    {
      title: '权益名称',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '是否核心权益',
      dataIndex: 'isCoreBenefit',
      width: 120,
      search: false,
      render: (isCoreBenefit) => (
        <span>{isCoreBenefit === 1 ? '是' : '否'}</span>
      ),
    },
    {
      title: '权益描述',
      dataIndex: 'description',
      search: false,
      render: (description) => <span>{description || '-'}</span>,
    },
  ];

  const footer = (
    <Row justify="center">
      <Space style={{ textAlign: 'center' }}>
        <Button type="primary" onClick={onConfirm}>
          提交
        </Button>
        <Button onClick={onCancelled}>取消</Button>
      </Space>
    </Row>
  );

  return (
    <Modal
      destroyOnClose
      width={1000}
      open={open}
      footer={footer}
      onCancel={() => onCancel()}
    >
      <ProTable<BenefitInfo>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onSelect: onTableRowSelected,
          onSelectAll: onAllTableRowSelected,
        }}
        tableAlertOptionRender={() => {
          return (
            <Space size={16}>
              <a onClick={() => setSelectedRowKeys([])}>取消选择</a>
            </Space>
          );
        }}
        options={{
          reload: () => setListParam({ ...listParam }),
        }}
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: associateSkuTableColumns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          ...antdUtils.transferPaginatorToTablePagination(paginator),
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default BenefitSelector;
