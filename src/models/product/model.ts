import { ConnectState } from '../connect';
import { fetchBenefitList } from './fetch';

import { Data, initPaginatorParam } from '@/utils/request';
import { Effect, Reducer } from '@umijs/max';
import { TransferItem } from 'antd/es/transfer';
import { Benefit } from './interface';

export interface ProductState {
  benefitList: Benefit[];
  benefitTransferItems: TransferItem[];
}

export const initProductState: ProductState = {
  benefitList: [],
  benefitTransferItems: [],
};

export interface ProductModel {
  namespace: 'product';
  state: ProductState;
  effects: {
    requestBenefitList: Effect;
  };
  reducers: {
    requestBenefitListSuccess: Reducer<
      ProductState,
      { type: 'requestBenefitListSuccess'; payload: Benefit[] }
    >;
  };
}

const productModel: ProductModel = {
  namespace: 'product',
  state: initProductState,
  effects: {
    *requestBenefitList(action, { call, put, select }) {
      const benefitList: Benefit[] = yield select(
        ({ product }: ConnectState) => product.benefitList,
      );
      if (!benefitList || !benefitList.length) {
        const result: Data<Benefit> = yield call(fetchBenefitList, {
          ...initPaginatorParam,
          limit: 10000,
        });
        yield put({
          type: 'requestBenefitListSuccess',
          payload: result.items,
        });
      }
    },
  },
  reducers: {
    requestBenefitListSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      return {
        ...state,
        benefitList: payload,
        benefitTransferItems: payload.map((item) => ({
          key: `${item.id}`,
          title: item.name,
          description: item.description,
        })),
      };
    },
  },
};

export default productModel;
