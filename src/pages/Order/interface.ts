import { StatusEnum } from '@/models/common.interface';
import { OrderStatusEnum, PayTypeEnum } from '@/models/order/interface';
import { Dayjs } from 'dayjs';

export enum OrderRefundStatus {
  /**
   * 订阅退款成
   */
  SUB_REFUND_SUCCESS = 1,
  /**
   * 非订阅退款成功
   */
  REFUND_SUCCESS = 2,
  /**
   * 未发起退款
   */
  NONE = 3,
}

export interface OrderTableForm {
  orderId?: string;
  skuId?: string;
  skuName?: string;
  userId?: string;
  device: string;
  state?: OrderStatusEnum;
  platform?: PayTypeEnum;
  isRenew?: StatusEnum;
  createTime?: Dayjs[];
  isActivity?: StatusEnum;
  isUpgrade?: StatusEnum;
  deviceType?: string;
  tradeNo?: string;
}
