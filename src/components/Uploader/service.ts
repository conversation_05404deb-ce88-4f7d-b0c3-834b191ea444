import { RequestMethod } from '@/models/common.enum';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import request from '@/utils/request';
import { RequestOption } from '@mantas/request';
import * as qiniu from 'qiniu-js';
import { CompressOptions } from 'qiniu-js/esm/utils/compress';
import { QiniuTokenResult, UploadTokenTypeEnum } from './interface';
import { uploadTypeUrl } from './utils';

export const fetchQiniuToken = (
  option?: RequestOption,
): Promise<QiniuTokenResult> => {
  const config: RequestOption = {
    url: option && option.url ? option.url : '/adm/app/upload_image_token',
    option: {
      method: RequestMethod.Post,
      params: {
        namespace: 'post',
      },
      // customerDomain: getCurrentDomain(),
      prefix: getCurrentPrefix(),
    },
  };
  return request(config.url, config.option);
};

export const uploaderService = {
  qiniuTokenKey: {
    key: 'post/2022/1/13/61dfb42d8f8086000deac528JgjJ3J4w4',
    token:
      'n9mmVEuBrO_IkIENoFQjBd8Y1qNAD-_tB0ALUFTY:Ru2yj2w1A9FBtcKmzHQo0MtJEKc=:eyJzY29wZSI6InBldGtpdC1zLXRlc3Q6cG9zdC8yMDIyLzEvMTMvNjFkZmI0MmQ4ZjgwODYwMDBkZWFjNTI4SmdqSjNKNHc0IiwicmV0dXJuQm9keSI6IntcInVybFwiOiBcImh0dHA6Ly9zYW5kYm94LmltZzUucGV0a2l0LmNuL3Bvc3QvMjAyMi8xLzEzLzYxZGZiNDJkOGY4MDg2MDAwZGVhYzUyOEpnakozSjR3NFwiLFwic2l6ZVwiOiAkKGZzaXplKSxcIm5hbWVcIjogJChmbmFtZSksXCJpbWFnZUluZm9cIjogJChpbWFnZUluZm8pfSIsImRlYWRsaW5lIjoxNjQyMDUyNDA1fQ==',
  },

  getQiniuTokenKey: async (type: UploadTokenTypeEnum, customerUrl: string) => {
    try {
      if (
        !uploaderService.qiniuTokenKey ||
        !uploaderService.qiniuTokenKey.token ||
        !!uploaderService.qiniuTokenKey.key
      ) {
        const result = await fetchQiniuToken({
          url:
            type === UploadTokenTypeEnum.CUSTOMER
              ? customerUrl
              : uploadTypeUrl[type],
          option: {},
        });
        uploaderService.qiniuTokenKey = result;
      }
      return uploaderService.qiniuTokenKey;
    } catch (e) {
      console.log(e);
    }
  },

  compressImage: async (
    file: File,
    option: CompressOptions,
  ): Promise<qiniu.CompressResult> => {
    let result: qiniu.CompressResult = { dist: file, width: 0, height: 0 };
    if (!option.maxWidth && !option.maxHeight) return result;

    if (!option.maxHeight) delete option.maxHeight;

    if (!option.maxWidth) delete option.maxWidth;

    result = await qiniu.compressImage(file as File, {
      ...option,
      noCompressIfLarger: true,
    });
    return result;
  },
};
