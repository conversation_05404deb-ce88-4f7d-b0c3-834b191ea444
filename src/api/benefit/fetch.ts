/**
 * 权益表相关的API请求函数
 */

import { ApiSuccessEnum } from '@/models/common.interface';
import request from '@/utils/request';
import { benefitTableApi } from './api';
import type {
  BenefitDetail,
  BenefitInfo,
  BenefitListParam,
  BenefitReorderParam,
  BenefitSaveParam,
} from './types';

/**
 * 获取权益表列表
 * @param params 查询参数
 * @returns 权益表列表数据
 */
export const fetchBenefitList = (
  params: BenefitListParam,
): Promise<BenefitInfo[]> => {
  const config = benefitTableApi.benefitList;
  config.option.params = params;
  return request(config.url, config.option);
};

/**
 * 获取权益表详情
 * @param id 权益表ID
 * @returns 权益表详情数据
 */
export const fetchBenefitDetail = (id: string): Promise<BenefitDetail> => {
  const config = benefitTableApi.benefitDetail;
  config.option.params = { groupId: id };
  return request(config.url, config.option);
};

/**
 * 创建权益表
 * @param data 创建参数
 * @returns 创建结果
 */
export const fetchBenefitCreate = (
  param: BenefitSaveParam,
): Promise<ApiSuccessEnum> => {
  const config = benefitTableApi.benefitCreate;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * 更新权益表
 * @param data 更新参数
 * @returns 更新结果
 */
export const fetchBenefitUpdate = (
  param: BenefitSaveParam,
): Promise<ApiSuccessEnum> => {
  const config = benefitTableApi.benefitUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * 删除权益表
 * @param id 权益表ID
 * @returns 删除结果
 */
export const fetchBenefitDelete = (id: number): Promise<ApiSuccessEnum> => {
  const config = benefitTableApi.benefitDelete;
  config.option.data = { groupId: id };
  return request(config.url, config.option);
};

/**
 * 权益重排序
 * @param data 重排序参数
 * @returns 重排序结果
 */
export const fetchBenefitReorder = (
  params: BenefitReorderParam,
): Promise<ApiSuccessEnum> => {
  const config = benefitTableApi.benefitReorder;
  config.option.data = params;
  return request(config.url, config.option);
};

/**
 * 权益状态切换
 * @param id 权益表ID
 * @param status 状态值
 * @returns 状态切换结果
 */
export const fetchBenefitStatusSwitch = (
  id: number,
  status: number,
): Promise<ApiSuccessEnum> => {
  const config = benefitTableApi.benefitStatusSwitch;
  config.option.data = { groupId: id, status };
  return request(config.url, config.option);
};
