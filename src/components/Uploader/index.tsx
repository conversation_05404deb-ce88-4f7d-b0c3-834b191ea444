import { PlusOutlined } from '@ant-design/icons';
import { message, Modal, Upload, UploadProps } from 'antd';
import { RcFile, UploadChangeParam } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { isFunction } from 'lodash';
import * as qiniu from 'qiniu-js';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import { QiniuUploadResult, SizeEnum, UploadTokenTypeEnum } from './interface';
import { uploaderService } from './service';
import { getByteSize, getImageSize } from './utils';

export interface UploaderProps {
  id?: string;
  type?: UploadTokenTypeEnum;
  customerUrl?: string;
  fileList?: Array<UploadFile<QiniuUploadResult>>;
  // Mb单位
  maxSize?: number;
  imageFileWidth?: number;
  imageFileHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  maxCount?: number;
  onChange?: (info: UploadChangeParam) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onDownload?: (file: UploadFile<QiniuUploadResult>) => void;
  onPreview?: (file: UploadFile<QiniuUploadResult>) => void;
  onRemove?: (file: UploadFile<QiniuUploadResult>) => void;
  customerRequest?: UploadProps['customRequest'];
}

const Uploader = ({
  id,
  type = UploadTokenTypeEnum.IMAGE,
  customerUrl = '',
  fileList,
  maxSize = 30,
  maxWidth = 0,
  maxHeight = 0,
  maxCount = 1,
  imageFileWidth,
  imageFileHeight,
  onChange,
  onDrop,
  onDownload,
  onPreview,
  onRemove,
  customerRequest,
}: UploaderProps) => {
  const [limitPictureSize, setLimitPictureSize] = useState(
    getByteSize(maxSize, SizeEnum.MB),
  );

  const acceptValue = useMemo(() => {
    if (type === UploadTokenTypeEnum.IMAGE) {
      return 'image/jpeg, image/png, image/jpg, image/gif, image/webp';
    }
    return '';
  }, [type]);

  useEffect(() => {
    setLimitPictureSize(getByteSize(maxSize, SizeEnum.MB));
  }, [maxSize]);

  const UploadButton = () => (
    <div className={styles.uploadButton}>
      <PlusOutlined className={styles.uploadButtonIcon} />
      <div className={styles.uploadButtonText}>上传</div>
    </div>
  );

  const beforeUploadFile = async (file: RcFile) => {
    // 判断type是否为UploadTokenTypeEnum.IMAGE
    if (type !== UploadTokenTypeEnum.IMAGE) return true;

    const isImage = file.type.startsWith('image/');

    if (!isImage) {
      message.warning('请上传图片');
      return false;
    }

    const imageSize = await getImageSize(file);
    let canUpload = false;

    if (imageFileWidth && imageFileHeight) {
      canUpload =
        (imageSize.width === imageFileWidth &&
          imageSize.height === imageFileHeight) ||
        imageSize.width / imageSize.height === imageFileWidth / imageFileHeight;
      if (!canUpload)
        message.warning(
          `图片尺寸必须为${imageFileWidth}px * ${imageFileHeight}px，或者相同宽高比`,
        );
      // console.log('canUpload', canUpload, imageSize);
      return canUpload;
    }

    if (imageFileWidth) {
      canUpload = imageSize.width === imageFileWidth;
      if (!canUpload) message.warning(`图片的宽度必须为${imageFileWidth}px`);
      return canUpload;
    }

    if (imageFileHeight) {
      canUpload = imageSize.height === imageFileHeight;
      if (!canUpload) message.warning(`图片的高度必须为${imageFileHeight}px`);
      return canUpload;
    }

    if (file.size > limitPictureSize) {
      message.warning(`图片大小已经超过了${maxSize}Mb，请重新上传`);
      return false;
    }

    return true;
  };

  const onUploadChange = (
    info: UploadChangeParam<UploadFile<QiniuUploadResult>>,
  ) => {
    // console.log('onUploadChange', info);
    const index = info.fileList.findIndex(
      (_file) => _file.uid === info.file.uid,
    );

    if (info.file.status === 'error' && !info.file.response) {
      info.fileList.splice(index, 1);
    } else if (info.file.status === 'done' && info.file.response) {
      info.file.url = (info.file.response as QiniuUploadResult).url || '';
      info.fileList[index].url = info.file.url;
    }
    if (isFunction(onChange)) onChange(info);
  };

  const onUploadDrop = (event: React.DragEvent<HTMLDivElement>) => {
    if (isFunction(onDrop)) onDrop(event);
  };

  const onUploadDownload = (file: UploadFile<QiniuUploadResult>) => {
    if (isFunction(onDownload)) onDownload(file);
  };

  const onUploadPreview = (file: UploadFile<QiniuUploadResult>) => {
    const url = file.url || file.thumbUrl || file.response?.url;
    Modal.info({
      title: file.name,
      content: <img style={{ width: '100%' }} src={url} />,
    });
    if (isFunction(onPreview)) onPreview(file);
  };

  const onUploadRemove = (file: UploadFile<QiniuUploadResult>) => {
    if (isFunction(onRemove)) onRemove(file);
  };

  return (
    <Upload
      id={id}
      accept={acceptValue}
      listType="picture-card"
      maxCount={maxCount}
      multiple={maxCount > 1}
      // action="/api/common/upload"
      name="uploadFiles"
      fileList={fileList}
      beforeUpload={beforeUploadFile}
      onChange={onUploadChange}
      onDrop={onUploadDrop}
      onDownload={onUploadDownload}
      onPreview={onUploadPreview}
      onRemove={onUploadRemove}
      customRequest={async (options) => {
        if (customerRequest) {
          customerRequest(options);
          return;
        }
        const { file, onProgress, onError, onSuccess } = options;
        const qiniuTokenKey = await uploaderService.getQiniuTokenKey(
          type,
          customerUrl,
        );
        const result = await uploaderService.compressImage(file as File, {
          maxWidth,
          maxHeight,
        });
        if (!qiniuTokenKey) {
          message.warning('没有权限，请重新上传');
          return;
        }
        const observable = qiniu.upload(
          result.dist as File,
          qiniuTokenKey.key,
          qiniuTokenKey.token || '',
        );
        // const subscription =
        observable.subscribe({
          next: (item) => {
            if (isFunction(onProgress)) onProgress(item.total);
          },
          error: (err) => {
            if (isFunction(onError)) onError(err, options);
          },
          complete: onSuccess,
        });
      }}
    >
      <UploadButton />
    </Upload>
  );
};

export default Uploader;
