import { actPropApiOption } from '@/services/api/actProp';
import request from '@/utils/request';
import { ActProp, ActPropParam } from './interface';

// 获取ActProp列表
export const fetchActPropList = (): Promise<ActProp[]> => {
  const config = actPropApiOption.actPropList;
  return request(config.url, config.option);
};

// 创建ActProp
export const fetchActPropCreation = (param: ActPropParam): Promise<void> => {
  const config = actPropApiOption.actPropCreation;
  config.option.data = { payload: JSON.stringify(param) };
  return request(config.url, config.option);
};
