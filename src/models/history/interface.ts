import { PaginatorParam } from '@/utils/request';

/**
 * ============================
 * 虚拟卡发放操作记录
 */
export interface VirtualCardDistributionListParam extends PaginatorParam {
  batchId?: string;
  deviceTypes?: string;
  skuNames?: string;
  startOperationTime?: number;
  endOperationTime?: number;
}

export interface VirtualCardDistribution {
  id: number;
  batchId: string;
  sendNum: number;
  deviceSkus: { deviceType: string; skuName: string }[];
  operationTime: number;
  operatorName: string;
  demandSide: string;
}
/**
 * ============================
 */

/**
 * ============================
 * 修改效期操作记录
 */
export interface ChangeExpirationRecordListParam extends PaginatorParam {
  orderId?: string;
  deviceType?: string;
  planName?: string;
  startOperationTime?: number;
  endOperationTime?: number;
}
export interface ChangeExpirationRecord {
  operationTime: number;
  operatorName: string;
  planId: number;
  beforeChangeExpiration: number;
  afterChangeExpiration: number;
  orderId: number;
  deviceType: string;
  skuName: string;
}
/**
 * ============================
 */

/**
 * ============================
 * 退款操作记录
 */
export enum RefundTypeEnum {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
}

export interface RefundOrderRecordListParam extends PaginatorParam {
  orderId?: string;
  skuName?: string;
  deviceType?: string;
  startOperationTime?: number;
  endOperationTime?: number;
}
export interface RefundOrderRecord {
  id: number;
  orderId: string;
  operatorName: string;
  operationTime: number;
  refundId: number;
  refundAmount: number;
  refundType: string;
  skuName: string;
  deviceType: string;
}
/**
 * ============================
 */
