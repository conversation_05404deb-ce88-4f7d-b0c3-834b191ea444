import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const packageApiOption: Record<
  | 'opertionPackageList'
  | 'operationPackageStatusSwtich'
  | 'operationPackageDeletion'
  | 'activityContentInfo'
  | 'activityPackageSave'
  | 'activityPackageDetail'
  | 'activityPackagePriorityValidator',
  RequestOption
> = {
  // 获取Package列表
  opertionPackageList: {
    url: '/adm/bs/act/package/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  operationPackageStatusSwtich: {
    url: '/adm/bs/act/package',
    option: {
      ...defaultOptions,
      method: RequestMethod.Put,
    },
  },
  operationPackageDeletion: {
    url: '/adm/bs/act/package',
    option: {
      ...defaultOptions,
      method: RequestMethod.Delete,
    },
  },
  activityContentInfo: {
    url: '/adm/bs/act/package/content',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  activityPackageSave: {
    url: '/adm/bs/act/package',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  activityPackageDetail: {
    url: '/adm/bs/act/package/:id',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  activityPackagePriorityValidator: {
    url: '/adm/bs/act/package/priority/check',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
};
