import { PaginatorParam } from '@/utils/request';

export enum SubscriptionPeriodTypeEnum {
  /**
   * 天
   */
  DAY = 'DAY',
  /**
   * 月
   */
  MONTH = 'MONTH',
  /**
   * 年
   */
  YEAR = 'YEAR',
}

export enum SubscriptionStateEnum {
  /**
   * 签约中
   */
  SIGNING = 'Signing',
  /**
   * 可用
   */
  ACTIVITY = 'Activity',
  /**
   * 已解约
   */
  DISABLED = 'Disabled',
  /**
   * 已取消
   */
  CANCELLED = 'Cancelled',
}

// 列表参数数据
export interface SubscriptionListParam extends PaginatorParam {
  subNo?: string;
  deviceId?: number;
  userId?: string;
  state?: SubscriptionStateEnum;
  deviceType?: string;
  startTime?: number;
  endTime?: number;
}

// 列表数据
export interface Subscription {
  id: number;
  userId: number;
  subscriptionNo: string;
  platform: string;
  agreementNo: string;
  exchangeNo: string;
  payload: string;
  state: SubscriptionStateEnum;
  periodNum: number;
  periodType: SubscriptionPeriodTypeEnum;
  amount: number;
  currency: string;
  totalPeriod: number;
  skuId: number;
  skuName: string;
  deviceId: number;
  deviceType: string;
  firstDeductDay: string;
  subscribedTime: string;
  createTime: string;
  bind: boolean;
  firstAmount: number;
  upgrade: number;
  platformAccountId: number;
  // 支付失败原因
  lastDeductErrorMsg: string;
}
