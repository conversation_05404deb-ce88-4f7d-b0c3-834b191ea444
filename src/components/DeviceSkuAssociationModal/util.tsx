import { ProductSku } from '@/models/product/interface';
import { serviceTimeUnitNameObj } from '@/models/product/util';
import { ProColumns } from '@ant-design/pro-components';
import { Tag } from 'antd';
import dayjs from 'dayjs';

// 设备类型关联SKU表格列定义
export const deviceSkuAssociationTableColumns: Array<ProColumns<ProductSku>> = [
  {
    title: 'SKU ID',
    dataIndex: 'id',
    width: 100,
    hideInSearch: true,
  },
  {
    title: 'SKU ID',
    dataIndex: 'id',
    width: 100,
    hideInTable: true,
    fieldProps: {
      placeholder: '请输入SKU ID',
    },
  },
  {
    title: 'SKU名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    fieldProps: {
      placeholder: '请输入SKU名称',
    },
  },
  {
    title: 'SKU别名',
    dataIndex: 'aliasName',
    width: 150,
    ellipsis: true,
    fieldProps: {
      placeholder: '请输入SKU别名',
    },
  },
  {
    title: 'SKU能力',
    dataIndex: 'capacities',
    width: 180,
    search: false,
    render: (_, record) => (
      <div>
        {(record.capacities || []).map((capacity, index) => (
          <Tag key={String(index)} color="blue" style={{ margin: '2px' }}>
            {capacity.name}
            {capacity.cycleTime ? `(${capacity.cycleTime}天循环)` : ''}
          </Tag>
        ))}
      </div>
    ),
  },
  {
    title: '适用设备',
    dataIndex: 'deviceTypes',
    width: 140,
    search: false,
    render: (_, record) => (
      <div>
        {(record.deviceTypes || []).map((deviceType, index) => (
          <Tag key={String(index)} color="green" style={{ margin: '2px' }}>
            {deviceType}
          </Tag>
        ))}
      </div>
    ),
  },
  {
    title: '服务时长',
    dataIndex: 'serviceTime',
    search: false,
    width: 120,
    render: (_, row) =>
      `${row.serviceTime}${serviceTimeUnitNameObj[row.serviceTimeUnit]}`,
  },
  {
    title: '价格(元)',
    dataIndex: 'price',
    search: false,
    width: 100,
    render: (_, row) => `¥${row.price.price}`,
  },
  {
    title: '自动续费',
    dataIndex: 'isRenew',
    search: false,
    width: 100,
    render: (_, row) => (
      <Tag color={row.price.isReNew ? 'success' : 'default'}>
        {row.price.isReNew ? '是' : '否'}
      </Tag>
    ),
  },
  {
    title: '在售状态',
    dataIndex: 'saleStatus',
    search: false,
    width: 100,
    render: (_, row) => (
      <Tag color={row.saleStatus ? 'success' : 'error'}>
        {row.saleStatus ? '在售' : '下架'}
      </Tag>
    ),
  },
  {
    title: '关联SKU',
    dataIndex: 'relationSkuId',
    search: false,
    width: 120,
    render: (_, row) => row.relationSkuId || '--',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    search: false,
    width: 160,
    render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
  },
];
