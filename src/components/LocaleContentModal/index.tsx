import { SelectOption } from '@/models/common.interface';
import { Form, FormInstance, Modal, Select } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { LocaleContentForm } from './interface';

// 定义暴露的form接口
export interface LocaleContentModalRef {
  form: FormInstance;
}

interface Props<T extends LocaleContentForm> {
  visible: boolean;
  onConfirm?: (formData: T) => void;
  onCancel: () => void;
  detail?: T;
  localeOptionList: SelectOption[];
  initialFormValues: T;
  children: React.ReactNode;
  title?: string;
}

const LocaleContentModal = forwardRef<LocaleContentModalRef, Props<any>>(
  function LocaleContentModal<T extends LocaleContentForm>(
    {
      localeOptionList,
      visible,
      onCancel,
      detail,
      onConfirm,
      initialFormValues,
      children,
      title = '编辑本地化内容',
    }: Props<T>,
    ref: React.Ref<LocaleContentModalRef>,
  ) {
    const [form] = Form.useForm();

    // 暴露form实例
    useImperativeHandle(ref, () => ({ form }), [form]);
    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };

    useEffect(() => {
      if (!detail) return;
      form.setFieldsValue(detail);
    }, [detail]);

    const onOk = async () => {
      try {
        const fd = await form.validateFields();
        if (onConfirm) onConfirm(fd);
        form.resetFields();
      } catch (error: any) {
        const firstError = error.errorFields[0];
        form.scrollToField(firstError.name);
      }
    };

    const onCancelled = () => {
      form.resetFields();
      onCancel();
    };

    return (
      <Modal
        width={1000}
        title={title}
        open={visible}
        onOk={onOk}
        onCancel={onCancelled}
      >
        <Form
          form={form}
          initialValues={initialFormValues}
          className="grid grid-cols-1 gap-6"
          layout="vertical"
        >
          <Form.Item
            name="language"
            label="语言"
            rules={[{ required: true, message: '请选择语言' }]}
          >
            <Select
              showSearch
              options={localeOptionList}
              filterOption={(input, option) =>
                (option?.value as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase()) ||
                (option?.label as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>
          {children}
        </Form>
      </Modal>
    );
  },
);

export default LocaleContentModal;
