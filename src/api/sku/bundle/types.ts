/**
 * 组合SKU管理相关的类型定义
 */

import { StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';

// 服务时长单位枚举
export enum ServiceTimeUnitEnum {
  YEAR = 'YEAR',
  MONTH = 'MONTH',
  DAY = 'DAY',
}

// 销售状态枚举
export enum SaleStatusEnum {
  OFFLINE = 0, // 下架
  ONLINE = 1, // 上架
}

// 操作类型枚举
export enum OperateTypeEnum {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  UPDATE_STATUS = 'UPDATE_STATUS',
}

// 货币信息接口
export interface CurrencyInfo {
  currencyCode: string;
  currencyName: string;
  currencySymbol: string;
  minimumAmount?: number;
  paymentMethods?: string[];
  country?: string[];
}

// 货币价格接口
export interface CurrencyPrice {
  currencyCode: string;
  currencyName: string;
  currencySymbol: string;
  minimumAmount?: number;
  price: number;
  usdPrice: number;
}

// 组合SKU货币价格接口
export interface SkuBundleCurrencyPrice {
  currencyCode: string;
  currencyName: string;
  currencySymbol: string;
  price: number;
  usdPrice: number;
  bundleSkuId?: number;
  linePrice?: number;
  firstPhasePrice?: number;
  unitPrice?: number;
}

// 组合SKU价格信息接口
export interface ProductSkuBundlePrice {
  price: number; // 商品售价
  linePrice?: number; // 划线价
  firstPhasePrice?: number; // 首购价
  unitPrice?: number; // 单价
  priceList?: SkuBundleCurrencyPrice[]; // 多币种价格列表
  firstPhasePriceList?: CurrencyPrice[]; // 多币种首购价列表
  isReNew?: StatusEnum; // 是否自动续费 0:否 1:是
  currency?: CurrencyInfo; // 货币信息
}

// SKU关联信息接口
export interface SkuRelation {
  skuId: number; // 普通SKU ID
  skuType: string; // 设备类型
}

// 组合SKU基础信息接口
export interface ProductSKUBundle {
  id: number; // 组合SKU ID
  name: string; // 商品名称
  shortName: string; // 商品简称
  aliasName?: string; // 商品别名
  discountName?: string; // 优惠名称
  supportedDeviceNum: number; // 支持设备数量
  serviceTime: number; // 服务时长
  serviceTimeUnit: ServiceTimeUnitEnum; // 服务时长单位
  price: ProductSkuBundlePrice; // 价格信息
  cornerMarkIcon?: string; // 角标图标
  skuRelations: SkuRelation[]; // 关联SKU列表
  cycleTime?: number; // 循环周期(天)
  level: number; // 组合SKU等级
  sort: number; // 优先级排序
  saleStatus: SaleStatusEnum; // 上下架状态
}

// 组合SKU列表查询参数
export interface BundleSkuListParam extends PaginatorParam {
  bundleSkuId?: number; // 组合SKU ID
  name?: string; // 商品名称(模糊)
  shortName?: string; // 商品简称(模糊)
  aliasName?: string; // 商品别名(模糊)
  serviceTime?: number; // 服务时长
  isRenew?: StatusEnum; // 是否自动续费 0/1
  level?: number; // 等级
  saleStatus?: SaleStatusEnum; // 上下架状态
  orderBySql?: string; // 排序字段
}

// 组合SKU创建参数
export interface ProductSkuBundleCreationParams {
  name: string; // 商品名称
  shortName: string; // 商品简称
  aliasName?: string; // 商品别名
  discountName?: string; // 优惠名称
  supportedDeviceNum: number; // 支持设备数量
  serviceTime: number; // 服务时长
  serviceTimeUnit: ServiceTimeUnitEnum; // 服务时长单位
  price: ProductSkuBundlePrice; // 价格信息
  cornerMarkIcon?: string; // 角标图标
  skuRelations: SkuRelation[]; // 关联SKU ID列表
  cycleTime?: number; // 循环周期(天)
  level: number; // 组合SKU等级
  sort: number; // 优先级排序
}

// 组合SKU更新参数
export interface ProductSKUBundleUpdateParams
  extends ProductSkuBundleCreationParams {
  id: number; // 组合SKU ID
  saleStatus: SaleStatusEnum; // 上架状态
  syncSupportedDeviceType: StatusEnum; // 是否同步已生成服务支持的设备类型
}

// 组合SKU变更记录
export interface CloudStorageProductSKUBundleChange {
  id: number;
  bundleSkuId: number;
  operatorId: number;
  operatorName: string;
  operateType: OperateTypeEnum; // 操作类型
  field: string; // 变更字段
  beforeContent: string; // 变更前内容
  afterContent: string; // 变更后内容
  createTime: string; // 创建时间
}

// 变更记录查询参数
export interface BundleSkuChangeLogsParam extends PaginatorParam {
  bundleSkuId: number; // 组合SKU ID
}
