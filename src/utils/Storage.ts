/**
 * @name StorageType
 *
 * @description 提供storage的类型
 */
export type StorageType = 'local' | 'session';

/**
 * <AUTHOR>
 * @name Storage
 *
 * @description 给开发者提供操作local or session storage的api；基于h5的sessionStorage/localStorage开发
 *
 */
export class StorageService {
  private _localStorage: Storage;
  private _sessionStorage: Storage;

  constructor() {
    this._localStorage = window.localStorage;
    this._sessionStorage = window.sessionStorage;
  }

  setLocalItem<T>(key: string, value: T): void {
    this._setItem<T>('local', key, value);
  }

  getLocalItem<T>(key: string): T | null {
    return this._getItem('local', key);
  }

  removeLocalItem(key: string) {
    this._removeItem('local', key);
  }

  removeAllLocalStorage() {
    this._removeAll('local');
  }

  setSessionItem<T>(key: string, value: T): void {
    this._setItem<T>('session', key, value);
  }

  getSessionItem<T>(key: string): T | null {
    return this._getItem('session', key);
  }

  removeSessionItem(key: string) {
    this._removeItem('session', key);
  }

  removeAllSessionStorage() {
    this._removeAll('session');
  }

  private _setItem<T>(type: StorageType, key: string, value: T) {
    try {
      const _item: string = JSON.stringify(value);
      this[`_${type}Storage`].setItem(key, _item);
    } catch (e) {
      console.log(e);
    }
  }

  private _getItem<T>(type: StorageType, key: string): T | null {
    let item: T | null = null;
    try {
      const _item = this[`_${type}Storage`].getItem(key);
      if (_item) {
        // item = JSON.parse(_item);
        item = _item;
      }
    } catch (e) {
      console.log(e);
    }
    return item;
  }

  private _removeItem(type: StorageType, key: string) {
    try {
      this[`_${type}Storage`].removeItem(key);
    } catch (e) {
      console.log(e);
    }
  }

  private _removeAll(type: StorageType) {
    try {
      this[`_${type}Storage`].clear();
    } catch (e) {
      console.log(e);
    }
  }
}

export const storageService: StorageService = new StorageService();
