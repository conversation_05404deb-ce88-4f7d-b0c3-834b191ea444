import { getCurrentDomain } from '@/utils/currentDomain';
import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const appApiOption: Record<
  'localeList' | 'uploadToken' | 'appUploadOssToken',
  RequestOption
> = {
  // 获取App列表
  localeList: {
    url: '/adm/app/locales',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取上传Token
  uploadToken: {
    url: '/adm/app/upload_image_token',
    option: {
      method: RequestMethod.Post,
      customerDomain: getCurrentDomain(),
    },
  },
  appUploadOssToken: {
    url: '/adm/app/uploadOSStoken',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
