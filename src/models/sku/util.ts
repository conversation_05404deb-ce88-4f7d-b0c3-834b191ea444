import {
  ServicePlanChargeTypeEnum,
  ServicePlanRefundStateEnum,
  ServicePlanStatusEnum,
  ServicePlanStatusV2Enum,
  ServiceSkuStatusEnum,
} from './interface';

export const serviceSkuStatusNameObj: {
  [key in ServiceSkuStatusEnum]: string;
} = {
  [ServiceSkuStatusEnum.OUT_DATE]: '已过期',
  [ServiceSkuStatusEnum.EFFECTING]: '生效中',
  [ServiceSkuStatusEnum.WAIT_EFFECT]: '待生效',
  [ServiceSkuStatusEnum.DELETED]: '已删除',
};

// 服务Plan退款状态
export const servicePlanRefundStateName: {
  [key in ServicePlanRefundStateEnum]: string;
} = {
  [ServicePlanRefundStateEnum.REFUND_SUCCEED]: '已退款',
  [ServicePlanRefundStateEnum.REFUND_FAILED]: '退款失败',
  [ServicePlanRefundStateEnum.REFUNDING]: '退款中',
};

// 服务Plan的状态名称
export const servicePlanStatusName: { [key in ServicePlanStatusEnum]: string } =
  {
    [ServicePlanStatusEnum.EFFECTING]: '生效中',
    [ServicePlanStatusEnum.OUT_DATE]: '已过期',
    [ServicePlanStatusEnum.WAIT_EFFECT]: '待生效',
  };

// 服务Plan的状态名称
export const servicePlanStatusV2Name: {
  [key in ServicePlanStatusV2Enum]: string;
} = {
  [ServicePlanStatusV2Enum.EFFECTING]: '生效中',
  [ServicePlanStatusV2Enum.OUT_DATE]: '已过期',
  [ServicePlanStatusV2Enum.WAIT_EFFECT]: '待生效',
};

export const servicePlanChargeTypeName: {
  [key in ServicePlanChargeTypeEnum]: string;
} = {
  [ServicePlanChargeTypeEnum.FREE]: '免费领取',
  [ServicePlanChargeTypeEnum.CHARGE]: '购买',
  [ServicePlanChargeTypeEnum.TRANSFER]: '转移',
  [ServicePlanChargeTypeEnum.REWARD_AI_CO_CREATION]: '奖励',
  [ServicePlanChargeTypeEnum.REWARD_UPLOAD_RESOURCE]: '奖励',
};
