import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import { Button, Card, Space, message } from 'antd';
import React from 'react';
import { DeviceSkuAssociationModal, useDeviceSkuAssociation } from './index';

/**
 * 设备类型关联SKU Modal使用示例
 */
const DeviceSkuAssociationExample: React.FC = () => {
  // 使用Hook管理Modal状态
  const {
    visible,
    currentDeviceType,
    relationParam,
    showModal,
    hideModal,
    handleConfirm,
  } = useDeviceSkuAssociation((deviceType, sku) => {
    // 关联成功后的处理逻辑
    message.success(
      `设备类型 ${deviceType} 成功关联SKU: ${sku.name} (ID: ${sku.id})`,
    );
    console.log('关联成功:', { deviceType, sku });

    // 这里可以调用API保存关联关系
    // await saveDeviceSkuAssociation(deviceType, sku.id);
  });

  // 处理不同设备类型的关联
  const handleAssociateDevice = (deviceType: SuitableDeviceTypeEnum) => {
    // 可以传入自定义的关联参数
    const customParam = {
      limit: 20,
      offset: 0,
      payload: {
        uniteDeviceTypes: [deviceType],
        uniteCapacities: [],
        // 可以添加更多筛选条件
        // saleStatus: SaleStatusEnum.ON_SALE, // 只显示在售的SKU
        // level: 1, // 只显示特定等级的SKU
      },
    };

    showModal(deviceType, customParam);
  };

  return (
    <Card title="设备类型关联SKU示例" style={{ margin: 16 }}>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <h4>选择设备类型进行SKU关联：</h4>
          <Space wrap>
            <Button
              type="primary"
              onClick={() => handleAssociateDevice(SuitableDeviceTypeEnum.D4sh)}
            >
              关联 D4sh 设备
            </Button>
            <Button
              type="primary"
              onClick={() => handleAssociateDevice(SuitableDeviceTypeEnum.D4h)}
            >
              关联 D4h 设备
            </Button>
            <Button
              type="primary"
              onClick={() => handleAssociateDevice(SuitableDeviceTypeEnum.T5)}
            >
              关联 T5 设备
            </Button>
            <Button
              type="primary"
              onClick={() => handleAssociateDevice(SuitableDeviceTypeEnum.T6)}
            >
              关联 T6 设备
            </Button>
            <Button
              type="primary"
              onClick={() => handleAssociateDevice(SuitableDeviceTypeEnum.T7)}
            >
              关联 T7 设备
            </Button>
          </Space>
        </div>

        <div>
          <h4>使用说明：</h4>
          <ul>
            <li>点击上方按钮可以打开对应设备类型的SKU关联弹框</li>
            <li>弹框会自动筛选适合该设备类型的SKU列表</li>
            <li>支持通过SKU ID、名称、别名进行搜索</li>
            <li>支持单选SKU进行关联</li>
            <li>确认后会触发关联成功回调</li>
          </ul>
        </div>
      </Space>

      {/* 设备类型关联SKU Modal */}
      {currentDeviceType && (
        <DeviceSkuAssociationModal
          visible={visible}
          deviceType={currentDeviceType}
          relationParam={relationParam}
          onConfirm={handleConfirm}
          onCancel={hideModal}
          title="关联设备SKU"
        />
      )}
    </Card>
  );
};

export default DeviceSkuAssociationExample;
