import { devicepackageApiOption } from '@/services/api/devicepackage';
import request from '@/utils/request';
import {
  DevicepackageDevice,
  DevicepackageDeviceLinkParam,
  DevicepackageDevicesParam,
  DevicepackageUnlinkParam,
} from './interface';

// 获取Devicepackage列表
export const fetchDevicepackageDevices = (
  param: DevicepackageDevicesParam,
): Promise<DevicepackageDevice[]> => {
  const config = devicepackageApiOption.devicepackageDevices;
  config.option.params = param;
  return request(config.url, config.option);
};
// 获取Devicepackage列表
export const fetchDevicepackageDeviceLink = (
  param: DevicepackageDeviceLinkParam,
): Promise<void> => {
  const config = devicepackageApiOption.devicepackageDeviceLink;
  config.option.params = param;
  return request(config.url, config.option);
};

// 解除关联设备
export const fetchDevicepackageUnlink = (
  param: DevicepackageUnlinkParam,
): Promise<void> => {
  const config = devicepackageApiOption.devicepackageUnlink;
  config.option.params = param;
  return request(config.url, config.option);
};
