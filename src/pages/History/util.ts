import { HistoryDetail, HistoryParam } from '@/models/history/interface';
import { HistoryForm, HistoryRecordTypeEnum } from './interface';

export const historyRecordTypeMap: { [key in HistoryRecordTypeEnum]: string } =
  {
    [HistoryRecordTypeEnum.DISTRIBUTION]: '虚拟卡发放',
    [HistoryRecordTypeEnum.CHANGE_EXPIRATION]: '更改过期时间',
    [HistoryRecordTypeEnum.REFUND]: '订单退款后',
  };

export const initialHistoryForm: HistoryForm = {};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: HistoryForm,
  id?: number,
): HistoryParam => {
  const param: HistoryParam = {};
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: HistoryDetail,
): HistoryForm => {
  const formData: HistoryForm = {};
  return formData;
};
