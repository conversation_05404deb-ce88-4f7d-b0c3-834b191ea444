import { Effect, Reducer } from '@umijs/max';
import { SelectOption } from '../common.interface';
import { fetchCurrencyApiOption } from './fetch';

export interface CurrencyState {
  currencyCodeList: string[];
  currencyCodeOptionList: SelectOption[];
  currencyCodeObj: { [key: string]: string };
}

export const initCurrencyState: CurrencyState = {
  currencyCodeList: [],
  currencyCodeOptionList: [],
  currencyCodeObj: {},
};

export interface CurrencyModel {
  namespace: 'currency';
  state: CurrencyState;
  effects: {
    requestCurrencyCodeList: Effect;
  };
  reducers: {
    requestCurrencyCodeListSuccess: Reducer<
      CurrencyState,
      { type: 'requestCurrencyCodeListSuccess'; payload: string[] }
    >;
  };
}

const currencyModel: CurrencyModel = {
  namespace: 'currency',
  state: initCurrencyState,
  effects: {
    *requestCurrencyCodeList(_, { call, put }) {
      const currencyCodeList: string[] = yield call(fetchCurrencyApiOption);
      yield put({
        type: 'requestCurrencyCodeListSuccess',
        payload: currencyCodeList,
      });
    },
  },
  reducers: {
    requestCurrencyCodeListSuccess(
      state = initCurrencyState,
      { payload },
    ): CurrencyState {
      const currencyCodeObj: { [key: string]: string } = {};
      const currencyCodeOptionList: SelectOption[] = [];
      payload.forEach((code) => {
        const option: SelectOption = { label: code, value: code };
        currencyCodeOptionList.push(option);
        currencyCodeObj[code] = code;
      });
      return {
        ...state,
        currencyCodeList: payload,
        currencyCodeOptionList,
        currencyCodeObj,
      };
    },
  },
};

export default currencyModel;
