/**
 * SKU编辑页面 - 权益配置区域组件
 * 集成新的增强权益选择器
 */

import { SkuBenefitItem, fetchSkuBenefitsUpdate } from '@/api/benefit';
import SortableRow from '@/components/SortableRow';
import { Benefit, ProductSkuBenefit } from '@/models/product/interface';
import {
  DragOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  Alert,
  Button,
  Empty,
  Image,
  Input,
  Popconfirm,
  Space,
  Switch,
  Table,
  Tooltip,
  Typography,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useCallback, useEffect, useState } from 'react';
import BenefitSelector from '../BenefitSelector'; // 保留原有选择器作为备选
import EnhancedBenefitSelector from '../EnhancedBenefitSelector';

const { Text } = Typography;

interface BenefitConfigSectionProps {
  skuId?: number;
  deviceType?: string;
  benefitList: ProductSkuBenefit[];
  onBenefitListChange: (benefits: ProductSkuBenefit[]) => void;
  disabled?: boolean;
}

const BenefitConfigSection: React.FC<BenefitConfigSectionProps> = ({
  skuId,
  deviceType: initialDeviceType,
  benefitList,
  onBenefitListChange,
  disabled = false,
}) => {
  const [currentDeviceType, setCurrentDeviceType] = useState<string>(
    initialDeviceType || '',
  );
  const [showEnhancedSelector, setShowEnhancedSelector] = useState(false);
  const [showLegacySelector, setShowLegacySelector] = useState(false);
  const [enhancedBenefits, setEnhancedBenefits] = useState<SkuBenefitItem[]>(
    [],
  );

  // 将现有权益列表转换为增强格式
  useEffect(() => {
    const convertedBenefits: SkuBenefitItem[] = benefitList.map(
      (benefit, index) => ({
        benefitId: benefit.id,
        attributeIds: [], // 权益属性ID列表，需要根据实际情况设置
        sort: index + 1,
        handpick: benefit.handpick || false,
      }),
    );
    setEnhancedBenefits(convertedBenefits);
  }, [benefitList]);

  // 精选权益操作
  const handleHandpickChange = useCallback(
    (handpick: boolean, benefit: ProductSkuBenefit) => {
      const updatedBenefits = benefitList.map((item) =>
        item.id === benefit.id ? { ...item, handpick } : item,
      );
      onBenefitListChange(updatedBenefits);
    },
    [benefitList, onBenefitListChange],
  );

  // 删除关联权益
  const handleRemoveBenefit = useCallback(
    (benefit: ProductSkuBenefit) => {
      const updatedBenefits = benefitList.filter(
        (item) => item.id !== benefit.id,
      );
      onBenefitListChange(updatedBenefits);
    },
    [benefitList, onBenefitListChange],
  );

  // 权益拖拽排序
  const handleDragEnd = useCallback(
    ({ active, over }: DragEndEvent) => {
      if (active.id !== over?.id) {
        const activeIndex = benefitList.findIndex((bf) => bf.id === active.id);
        const overIndex = benefitList.findIndex((bf) => bf.id === over?.id);
        const reorderedBenefits = arrayMove(
          benefitList,
          activeIndex,
          overIndex,
        );
        onBenefitListChange(reorderedBenefits);
      }
    },
    [benefitList, onBenefitListChange],
  );

  // 增强权益选择器确认
  const handleEnhancedBenefitConfirm = useCallback(
    async (selectedBenefits: SkuBenefitItem[]) => {
      try {
        // 如果有SKU ID，保存到后端
        if (skuId && currentDeviceType) {
          await fetchSkuBenefitsUpdate({
            skuId,
            deviceType: currentDeviceType,
            benefits: selectedBenefits,
          });
        }

        // 转换为现有格式并更新状态
        const convertedBenefits: ProductSkuBenefit[] = selectedBenefits.map(
          (item) => {
            const existingBenefit = benefitList.find(
              (b) => b.id === item.benefitId,
            );
            return {
              id: item.benefitId,
              name: existingBenefit?.name || `权益${item.benefitId}`,
              icon: existingBenefit?.icon || '',
              description: existingBenefit?.description || '',
              image: existingBenefit?.image || '',
              isCoreBenefit: existingBenefit?.isCoreBenefit,
              value: existingBenefit?.value || '默认',
              handpick: item.handpick,
              sort: item.sort,
            };
          },
        );

        onBenefitListChange(convertedBenefits);
        setEnhancedBenefits(selectedBenefits);
        setShowEnhancedSelector(false);
      } catch (error) {
        console.error('保存权益配置失败:', error);
      }
    },
    [skuId, currentDeviceType, benefitList, onBenefitListChange],
  );

  // 传统权益选择器确认
  const handleLegacyBenefitConfirm = useCallback(
    (selectedBenefits: Benefit[]) => {
      const convertedBenefits: ProductSkuBenefit[] = selectedBenefits.map(
        (bf) => {
          const currentBenefit = benefitList.find((item) => item.id === bf.id);
          return {
            id: bf.id,
            name: bf.name,
            icon: bf.icon,
            description: bf.description,
            image: bf.image,
            isCoreBenefit: bf.isCoreBenefit,
            value: bf.value || '默认',
            handpick: currentBenefit?.handpick || false,
          };
        },
      );
      onBenefitListChange(convertedBenefits);
      setShowLegacySelector(false);
    },
    [benefitList, onBenefitListChange],
  );

  // 编辑状态管理
  const [editingValue, setEditingValue] = useState<{
    benefitId: number;
    value: string;
  } | null>(null);

  // 权益值双击编辑处理
  const handleValueDoubleClick = useCallback(
    (benefit: ProductSkuBenefit) => {
      if (!disabled) {
        setEditingValue({
          benefitId: benefit.id,
          value: benefit.value || '',
        });
      }
    },
    [disabled],
  );

  // 保存权益值
  const handleValueSave = useCallback(
    (benefitId: number, newValue: string) => {
      const updatedBenefits = benefitList.map((item) =>
        item.id === benefitId ? { ...item, value: newValue } : item,
      );
      onBenefitListChange(updatedBenefits);
      setEditingValue(null);
    },
    [benefitList, onBenefitListChange],
  );

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>, benefitId: number) => {
      if (e.key === 'Enter') {
        handleValueSave(benefitId, e.currentTarget.value);
      } else if (e.key === 'Escape') {
        setEditingValue(null);
      }
    },
    [handleValueSave],
  );

  // 表格列配置
  const columns: ColumnsType<ProductSkuBenefit> = [
    {
      key: 'sort',
      width: 60,
      render: () => <DragOutlined style={{ cursor: 'grab', color: '#999' }} />,
    },
    {
      title: '权益名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name) => <Text strong>{name}</Text>,
    },
    {
      title: '是否核心权益',
      dataIndex: 'isCoreBenefit',
      key: 'isCoreBenefit',
      width: 120,
      render: (isCoreBenefit) => (
        <Text>{isCoreBenefit === 1 ? '是' : '否'}</Text>
      ),
    },
    {
      title: '权益描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (description) => (
        <Tooltip title={description}>
          <Text ellipsis style={{ maxWidth: 180 }}>
            {description || '-'}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '权益配图',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      render: (image) =>
        image ? (
          <Image
            src={image}
            alt="权益配图"
            width={40}
            height={40}
            style={{ objectFit: 'cover' }}
            preview={{
              mask: '预览',
            }}
          />
        ) : (
          <Text type="secondary">-</Text>
        ),
    },
    {
      title: '预设值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      width: 120,
      render: (defaultValue, record) => (
        <Text ellipsis style={{ maxWidth: 100 }}>
          {defaultValue || record.value || '默认'}
        </Text>
      ),
    },
    {
      title: '精选推荐',
      dataIndex: 'handpick',
      key: 'handpick',
      width: 100,
      render: (handpick, row) => (
        <Switch
          size="small"
          unCheckedChildren="否"
          checkedChildren="是"
          checked={handpick}
          onChange={(checked) => handleHandpickChange(checked, row)}
          disabled={disabled}
        />
      ),
    },
    {
      title: '权益值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => {
        const isEditing = editingValue?.benefitId === record.id;

        if (isEditing) {
          return (
            <Input
              size="small"
              defaultValue={editingValue.value}
              autoFocus
              onBlur={(e) => handleValueSave(record.id, e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, record.id)}
            />
          );
        }

        return (
          <Text
            style={{ cursor: disabled ? 'default' : 'pointer' }}
            onDoubleClick={() => handleValueDoubleClick(record)}
          >
            {value || '默认'}
          </Text>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, row) => (
        <Space>
          <Popconfirm
            title="确定删除该权益吗？"
            onConfirm={() => handleRemoveBenefit(row)}
            disabled={disabled}
          >
            <Button type="link" danger disabled={disabled}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProCard
        title="关联权益"
        extra={
          <Space>
            <Button
              type="link"
              icon={<PlusOutlined />}
              onClick={() => setShowEnhancedSelector(true)}
              disabled={disabled}
            >
              智能权益配置
            </Button>
            <Button
              type="link"
              icon={<PlusOutlined />}
              onClick={() => setShowLegacySelector(true)}
              disabled={disabled}
            >
              传统权益选择
            </Button>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        {/* 权益配置说明 */}
        <Alert
          message="权益配置说明"
          description="使用智能权益配置可以根据设备类型自动过滤权益，并支持权益值的精确配置。传统权益选择保持原有的选择方式。"
          type="info"
          icon={<InfoCircleOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 权益列表 */}
        {benefitList && benefitList.length > 0 ? (
          <DndContext
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={benefitList.map((item) => item.id)}
              strategy={verticalListSortingStrategy}
            >
              <Table<ProductSkuBenefit>
                components={{
                  body: {
                    row: SortableRow,
                  },
                }}
                rowKey="id"
                columns={columns}
                dataSource={benefitList}
                pagination={false}
                size="small"
              />
            </SortableContext>
          </DndContext>
        ) : (
          <Empty
            description="暂无关联权益"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </ProCard>

      {/* 增强权益选择器 */}
      {showEnhancedSelector && (
        <EnhancedBenefitSelector
          value={enhancedBenefits}
          onChange={handleEnhancedBenefitConfirm}
          deviceType={currentDeviceType}
          onDeviceTypeChange={setCurrentDeviceType}
          disabled={disabled}
        />
      )}

      {/* 传统权益选择器 */}
      {showLegacySelector && (
        <BenefitSelector
          selectedBenefits={benefitList.map(
            (bf) =>
              ({
                ...bf,
                status: 1,
                createTime: 0,
                updateTime: 0,
              } as Benefit),
          )}
          open={showLegacySelector}
          onOk={handleLegacyBenefitConfirm}
          onCancel={() => setShowLegacySelector(false)}
        />
      )}
    </>
  );
};

export default BenefitConfigSection;
