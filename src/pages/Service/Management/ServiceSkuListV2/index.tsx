import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { ApiSuccessEnum } from '@/models/common.interface';
import { ReNewEnum } from '@/models/product/interface';
import {
  fetchCancelSubscription,
  fetchRefundDetail,
  fetchRefundRetrieveBalanceDetail,
} from '@/models/refund/fetch';
import {
  AvailableRefundInfo,
  RefundDetail as RefundDetaiInfo,
} from '@/models/refund/interface';
import {
  fetchServicePlanList,
  fetchUpdatingSkuExpirationDateV2,
} from '@/models/sku/fetch';
import {
  ServicePlan,
  ServicePlanStatusV2Enum,
  UpdatingSkuExpirationParamV2,
} from '@/models/sku/interface';
import {
  servicePlanChargeTypeName,
  servicePlanRefundStateName,
  servicePlanStatusV2Name,
} from '@/models/sku/util';
import { DownOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  Input,
  MenuProps,
  Modal,
  Space,
  Typography,
  message,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import Refund from './Refund';
import RefundDetail from './RefundDetail';
import styles from './index.less';

interface Props {
  deviceType: string;
  serviceId: number;
  showModal: boolean;
  onExpirationChangeSuccess?: () => void;
  onRefundSuccess?: () => void;
  onClose?: (shouldRefresh?: boolean) => void;
}

interface FormData {
  expirationDate: Dayjs | null;
  reason: string;
}

const initFormData: FormData = {
  expirationDate: null,
  reason: '',
};

const ServiceSkuList: React.FC<Props> = ({
  serviceId,
  showModal,
  onClose,
  deviceType,
  onExpirationChangeSuccess,
  onRefundSuccess,
}: Props) => {
  const [dataList, setDataList] = useState<ServicePlan[]>([]);
  const [originDataList, setOriginDataList] = useState<ServicePlan[]>([]);
  const [showEdit, setShowEdit] = useState(false);
  const [showInvalidate, setShowInvalidate] = useState(false);
  const [selectedServiceSku, setSelectedServiceSku] = useState<ServicePlan>();
  const [form] = Form.useForm<FormData>();
  // 失效功能的专用表单
  const [invalidateForm] = Form.useForm<{ reason: string }>();
  const [loading, setLoading] = useState(false);
  const [selectedData, setSelectedData] = useState<ServicePlan>();
  // 可退款的信息
  const [availableRefundInfo, setAvailableRefundInfo] =
    useState<AvailableRefundInfo>();
  const [selectedOrderRefundDetail, setSelectedOrderRefundDetail] =
    useState<RefundDetaiInfo>();

  // 获取列表数据
  const requestServiceSkuList = async (_serviceId: number) => {
    let list = await fetchServicePlanList(_serviceId, deviceType);
    setDataList(list);
    setOriginDataList(list);
  };

  // 修改效期
  const editServiceSku = (row: ServicePlan) => {
    setShowEdit(true);
    setSelectedServiceSku(row);
    console.log(row);
    form.setFieldsValue({ expirationDate: dayjs(row.indateStr) });
    setSelectedData(row);
  };

  const filterDataList = (searchValue: string) => {
    if (!searchValue) setDataList([...originDataList]);

    const resultDataList = originDataList.filter((item) => {
      let rowStr = '';
      let key: keyof ServicePlan;
      for (key in item) {
        if (item[key]) {
          rowStr += item[key];
        }
      }
      return rowStr.includes(searchValue);
    });
    setDataList([...resultDataList]);
  };

  const holdTheSameDateTime = (currentTime: Dayjs, timeZone: string): Dayjs => {
    // console.log(currentTime.utcOffset(), dayjs().tz(timeZone).utcOffset());
    const offset = currentTime.utcOffset() - dayjs().tz(timeZone).utcOffset();
    console.log(
      currentTime.utcOffset(),
      dayjs().tz(timeZone).utcOffset(),
      offset,
    );
    const targetDateTime = currentTime.add(offset, 'minutes').tz(timeZone);
    return targetDateTime;
  };

  // 失效服务套餐
  const invalidateServiceSku = (row: ServicePlan) => {
    // 显示失效弹窗
    setShowInvalidate(true);
    // 保存当前选中的套餐
    setSelectedServiceSku(row);
    // 清空表单原因字段
    invalidateForm.resetFields();
    setSelectedData(row);
  };

  // 提交失效请求
  const submitInvalidate = (ev: { reason: string }) => {
    if (!selectedServiceSku) return;

    // 弹出确认框再次确认操作
    Modal.confirm({
      title: '失效确认',
      content: '确认将此服务套餐设为失效状态？此操作将立即生效且不可撤销。',
      onOk: async () => {
        setLoading(true);
        try {
          // 根据plan状态明确判断使用哪个时间点
          let baseTime;
          if (
            selectedServiceSku.skuStatus === ServicePlanStatusV2Enum.WAIT_EFFECT
          ) {
            // 待生效状态：使用开始生效时间进行失效处理
            baseTime = dayjs(selectedServiceSku.workTime * 1000);
          } else {
            // 生效中状态或其他状态：使用当前时间进行失效处理
            baseTime = dayjs();
          }

          // 统一调用holdTheSameDateTime方法
          const targetDateTime = holdTheSameDateTime(
            baseTime,
            selectedServiceSku.zoneId,
          );

          console.log(targetDateTime, targetDateTime.unix());

          // 调用接口将套餐设为失效状态
          const result = await fetchUpdatingSkuExpirationDateV2({
            planId: selectedServiceSku.id,
            // 使用到期时间戳，立即失效
            expiration: targetDateTime.unix(),
            // expiration: dayjs().unix() - 1,
            deviceType,
            changeReason: ev.reason,
          } as UpdatingSkuExpirationParamV2);

          if (result === ApiSuccessEnum.success) {
            message.success('套餐已成功设为失效状态！');
            // 刷新列表数据
            requestServiceSkuList(serviceId);
            // 关闭失效弹窗
            setShowInvalidate(false);
            onExpirationChangeSuccess?.();
          }
        } catch (e) {
          console.log(e);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 退款
  const refundService = async (row: ServicePlan) => {
    const { orderId } = row;
    const result = await fetchRefundRetrieveBalanceDetail(orderId);
    console.log(result);
    if (!result.availableRefundAmount) {
      message.warning('当前订单无法退款!');
      return;
    }
    setSelectedData(row);
    setAvailableRefundInfo(result);
  };

  // 退款详情
  const refundDetail = async (row: ServicePlan) => {
    const { orderId } = row;
    const result = await fetchRefundDetail(orderId);
    if (!result.refundList || !result.refundList.length) {
      message.warning('当前订单无退款记录!');
      return;
    }
    setSelectedOrderRefundDetail(result);
  };

  // 取消签约
  const cancelSubscription = async (row: ServicePlan) => {
    Modal.confirm({
      content: '提交后，将取消当前套餐的订阅，是否确认？',
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        const { orderId } = row;
        try {
          await fetchCancelSubscription(orderId);
          message.success('已成功解约');
          requestServiceSkuList(serviceId);
        } catch (error) {
          console.log(error);
        }
      },
    });
  };

  const submit = (ev: FormData) => {
    if (!selectedServiceSku || !ev.expirationDate) return;
    // const expirationDate = ev.expirationDate?.tz('PRC');

    const targetDateTime = holdTheSameDateTime(
      ev.expirationDate,
      selectedServiceSku.zoneId,
    );
    // console.log(ev.expirationDate, ev.expirationDate.valueOf());
    const formatedExpirationDate = targetDateTime.format('YYYY-MM-DD');
    Modal.confirm({
      title: '修改确认',
      content: `修改后，服务将于${formatedExpirationDate} 23:59:59到期，是否确认修改？`,
      onOk: async () => {
        setLoading(true);
        try {
          // 直接使用新接口调用，不判断是否是d4sh
          const result = await fetchUpdatingSkuExpirationDateV2({
            planId: selectedServiceSku.id,
            // 使用设备时区的时间戳
            expiration: targetDateTime.endOf('d').unix(),
            deviceType,
            changeReason: ev.reason,
          } as UpdatingSkuExpirationParamV2);
          // if (deviceType.toLowerCase() === 'd4sh') {
          //   result =
          // } else {
          //   result = await fetchUpdatingSkuExpirationDate(
          //     {
          //       id: selectedServiceSku.id,
          //       // 使用设备时区的时间戳
          //       date: targetDateTime.endOf('d').unix(),
          //     },
          //     deviceType,
          //   );
          // }
          if (result === ApiSuccessEnum.success) {
            message.success('修改成功！');
            requestServiceSkuList(serviceId);
            setShowEdit(false);
            onExpirationChangeSuccess?.();
          }
        } catch (e) {
          console.log(e);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const columns: Array<ProColumns<ServicePlan>> = [
    {
      title: 'SKU ID',
      dataIndex: 'skuId',
      width: 100,
      render: (text, row) => (
        <RedirectLink
          text={text}
          linkUrl="/business/cloudServiceSku"
          params={{ id: row.skuId }}
        />
      ),
    },
    {
      title: 'Plan ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: 'Plan名称',
      dataIndex: 'shortName',
      width: 150,
    },
    {
      title: '服务来源',
      dataIndex: 'chargeType',
      width: 100,
      render: (_, record) => servicePlanChargeTypeName[record.chargeType],
    },
    {
      title: '订单编号',
      dataIndex: 'orderId',
      width: 250,
      render: (text, row) => (
        <RedirectLink
          text={text}
          linkUrl="/business/order"
          params={{ orderId: row.orderId }}
        />
      ),
    },
    {
      title: '支付方式',
      dataIndex: 'payWay',
      width: 100,
      // render: (_, row) => serviceSkuStatusNameObj[row.skuStatus],
    },
    {
      title: '平台订单ID',
      dataIndex: 'tradeNo',
      width: 250,
    },
    {
      title: '退款状态',
      dataIndex: 'refundState',
      width: 100,
      render: (_, record) =>
        servicePlanRefundStateName[record.refundState] || '未退款',
    },
    {
      title: 'Plan状态',
      dataIndex: 'skuStatus',
      width: 100,
      render: (_, record) => servicePlanStatusV2Name[record.skuStatus],
    },
    {
      title: 'Plan能力',
      dataIndex: 'capacity',
      width: 200,
      render: (_, record) => {
        return (record.capacities || []).map((cap, index) => (
          <div key={String(index)}>
            {cap.name} {cap.cycleTime}天循环
          </div>
        ));
      },
    },
    {
      title: '服务时长',
      dataIndex: 'serviceDuration',
      width: 120,
    },
    {
      title: '自动续费',
      dataIndex: 'skuId',
      render: (_, row) => (row.autoPay ? '是' : '否'),
      width: 120,
    },
    {
      title: '设备时区',
      dataIndex: 'zoneId',
      width: 120,
    },
    {
      title: '到期时间',
      dataIndex: 'indateStr',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 100,
      render: (_, row) => {
        const items: MenuProps['items'] = [
          // 添加失效按钮作为第一个操作选项，只有当状态为生效中或待生效时才显示
          ...(row.skuStatus === ServicePlanStatusV2Enum.EFFECTING ||
          row.skuStatus === ServicePlanStatusV2Enum.WAIT_EFFECT
            ? [
                {
                  key: 'invalidate',
                  label: (
                    <a
                      onClick={() => invalidateServiceSku(row)}
                      className={styles.button}
                    >
                      失效
                    </a>
                  ),
                },
              ]
            : []),
          {
            key: 'editExpiration',
            label: (
              <a onClick={() => editServiceSku(row)} className={styles.button}>
                修改效期
              </a>
            ),
          },
          {
            key: 'refund',
            label: (
              <a onClick={() => refundService(row)} className={styles.button}>
                退款
              </a>
            ),
          },
          {
            key: 'refundDetail',
            label: (
              <a onClick={() => refundDetail(row)} className={styles.button}>
                退款详情
              </a>
            ),
          },
        ];
        if (row.autoPay === ReNewEnum.RENEW) {
          items.push({
            key: 'unsign',
            label: (
              <a
                onClick={() => cancelSubscription(row)}
                className={styles.button}
              >
                取消订阅
              </a>
            ),
          });
        }
        return (
          <Dropdown menu={{ items }}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                更多操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  useEffect(() => {
    requestServiceSkuList(serviceId);
  }, []);

  return (
    <>
      <Modal
        key="table"
        width={950}
        destroyOnClose
        open={showModal}
        footer={null}
        onCancel={() => onClose?.()}
      >
        <ProTable<ServicePlan>
          dataSource={dataList}
          columns={columns}
          defaultSize="small"
          rowKey="id"
          search={false}
          scroll={{
            x: columns
              .filter((col) => col.dataIndex !== 'action')
              .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
          }}
          options={{ reload: () => requestServiceSkuList(serviceId) }}
          toolbar={{
            search: {
              onSearch: filterDataList,
            },
          }}
        />
      </Modal>
      <Modal
        width={500}
        key="form"
        destroyOnClose
        open={showEdit}
        title="编辑"
        footer={null}
        onCancel={() => setShowEdit(false)}
      >
        <Typography.Title level={4}>
          注意：失效后，服务将立即到期
        </Typography.Title>
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={form}
          initialValues={initFormData}
          onFinish={submit}
        >
          <Form.Item
            name="expirationDate"
            label="到期时间"
            rules={[{ required: true, message: '请选择到期时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabledDate={(current) => {
                const createdDate = dayjs(
                  selectedData?.createdAt,
                  'YYYY-MM-DD',
                );
                return current && current.isBefore(createdDate, 'day');
              }}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="修改原因"
            rules={[{ required: true, message: '请输入修改原因' }]}
          >
            <Input placeholder="请输入修改原因" />
          </Form.Item>
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Space>
              <Button htmlType="submit" type="primary" loading={loading}>
                提交
              </Button>
              <Button
                type="default"
                htmlType="button"
                onClick={() => {
                  form.resetFields();
                  setShowEdit(false);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        width={600}
        key="invalidateForm"
        destroyOnClose
        open={showInvalidate}
        title="套餐失效"
        footer={null}
        onCancel={() => setShowInvalidate(false)}
      >
        {/* 失效原因输入表单 */}
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          form={invalidateForm}
          initialValues={{ reason: '' }}
          onFinish={submitInvalidate}
        >
          <Form.Item
            name="reason"
            label="失效原因"
            rules={[{ required: true, message: '请输入失效原因' }]}
          >
            <Input placeholder="请输入失效原因" />
          </Form.Item>
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Space>
              <Button htmlType="submit" type="primary" loading={loading}>
                提交
              </Button>
              <Button
                type="default"
                htmlType="button"
                onClick={() => {
                  invalidateForm.resetFields();
                  setShowInvalidate(false);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {selectedData && availableRefundInfo ? (
        <Refund
          serviceSku={selectedData}
          availableRefundInfo={availableRefundInfo}
          onSubmit={() => {
            setAvailableRefundInfo(undefined);
            requestServiceSkuList(serviceId);
            onRefundSuccess?.();
          }}
          onClose={() => setAvailableRefundInfo(undefined)}
        />
      ) : null}
      {selectedOrderRefundDetail ? (
        <RefundDetail
          refundDetail={selectedOrderRefundDetail}
          onClose={() => setSelectedOrderRefundDetail(undefined)}
        ></RefundDetail>
      ) : null}
    </>
  );
};

export default ServiceSkuList;
