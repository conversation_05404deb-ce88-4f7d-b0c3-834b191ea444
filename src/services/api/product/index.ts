import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const productApiOption: Record<
  | 'productSkuList'
  | 'productSkuDetail'
  | 'productSkuUpdatingLogs'
  | 'productSkuSaleStatusUpdating'
  | 'productSkuCreation'
  | 'productSkuUpdating'
  | 'cloudPackageConfigurationDict'
  | 'productSkuSaleStatusUpdatingByBatch'
  | 'associableProductSkuList'
  // 权益相关
  | 'benefitList'
  | 'benefitDetail'
  | 'benefitDeletion'
  | 'benefitCreation'
  | 'benefitUpdating'
  | 'benefitStatusSwitching'
  // SKU能力
  | 'capacityList'
  | 'capacityUpdating'
  // 特权对比
  | 'benefitCompareList'
  | 'benefitCompareDetail'
  | 'benefitCompareAdd'
  | 'benefitCompareUpdate'
  | 'benefitCompareStatusUpdate'
  // 初始化价格
  | 'otherCurrencySkuPriceList'
  | 'basicSkuPriceList'
  // 跨区复制
  | 'forwardLogin'
  | 'syncSku'
  | 'forwardSyncSku'
  | 'syncRelationData',
  RequestOption
> = {
  // 获取Product列表
  productSkuList: {
    url: '/adm/bs/product/sku/find',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取ProductSku详情
  productSkuDetail: {
    url: '/adm/bs/product/sku/get',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取商品SKU的修改记录
  productSkuUpdatingLogs: {
    url: '/adm/bs/product/sku/getChangedLogs',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 商品SKU上/下架
  productSkuSaleStatusUpdating: {
    url: '/adm/bs/product/sku/updateSaleStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建商品SKU
  productSkuCreation: {
    url: '/adm/bs/product/sku/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 修改商品SKU
  productSkuUpdating: {
    url: '/adm/bs/product/sku/update',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取云存套餐配置的字典表
  cloudPackageConfigurationDict: {
    url: '/adm/bs/product/sku/dic',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 批量商品SKU上/下架
  productSkuSaleStatusUpdatingByBatch: {
    url: '/adm/bs/product/sku/batchUpdateSaleStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  associableProductSkuList: {
    url: '/adm/bs/product/sku/getRelationSku',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  benefitList: {
    url: '/adm/bs/product/sku/benefit/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  benefitDetail: {
    url: '/adm/bs/product/sku/benefit/get',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  benefitDeletion: {
    url: '/adm/bs/product/sku/benefit/delete',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  benefitCreation: {
    url: '/adm/bs/product/sku/benefit/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  benefitUpdating: {
    url: '/adm/bs/product/sku/benefit/update',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  benefitStatusSwitching: {
    url: '/adm/bs/product/sku/benefit/updateStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },

  capacityList: {
    url: '/adm/bs/product/sku/capacity/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  capacityUpdating: {
    url: '/adm/bs/product/sku/capacity/updateBenefits',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },

  /**
   * ************************************************************************
   * 特权对比相关接口
   */
  benefitCompareList: {
    url: '/adm/bs/product/sku/benefit/compare/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  benefitCompareDetail: {
    url: '/adm/bs/product/sku/benefit/compare/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  benefitCompareAdd: {
    url: '/adm/bs/product/sku/benefit/compare/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  benefitCompareUpdate: {
    url: '/adm/bs/product/sku/benefit/compare/update',
    option: {
      ...defaultOptions,
      method: RequestMethod.Put,
    },
  },
  benefitCompareStatusUpdate: {
    url: '/adm/bs/product/sku/benefit/compare/updateStatus',
    option: {
      ...defaultOptions,
      method: RequestMethod.Put,
    },
  },
  /**
   * ************************************************************************
   */
  /**
   * ************************************************************************
   * 获取sku价格列表
   */
  otherCurrencySkuPriceList: {
    url: '/adm/bs/product/sku/getPrices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  basicSkuPriceList: {
    url: '/adm/bs/product/sku/getPriceEnum',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  /**
   * ************************************************************************
   */
  forwardLogin: {
    url: '/adm/bs/product/sku/forwardLogin/:area',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  syncSku: {
    url: '/adm/bs/product/sku/syncSKU',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  forwardSyncSku: {
    url: '/adm/bs/product/sku/forwardSyncSKU/:area',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  syncRelationData: {
    url: '/adm/bs/product/sku/syncRelationData',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
