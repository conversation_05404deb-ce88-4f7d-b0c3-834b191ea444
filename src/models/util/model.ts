import { Data } from '@/utils/request';
import { Effect, Reducer } from '@umijs/max';
import { fetchCapacityList } from './fetch';
import { Capacity } from './interface';

export interface UtilState {
  capacities: Capacity[];
  capacityMap: Map<string, string>;
}

export const initUtilState: UtilState = {
  capacities: [],
  capacityMap: new Map(),
};

export interface UtilModel {
  namespace: 'util';
  state: UtilState;
  effects: {
    requestCapacityList: Effect;
  };
  reducers: {
    requestCapacityListSuccess: Reducer<
      UtilState,
      { type: 'requestCapacityListSuccess'; payload: Capacity[] }
    >;
  };
}

const utilModel: UtilModel = {
  namespace: 'util',
  state: initUtilState,
  effects: {
    *requestCapacityList(action, { call, put }) {
      const { items }: Data<Capacity> = yield call(fetchCapacityList);
      yield put({
        type: 'requestCapacityListSuccess',
        payload: items,
      });
    },
  },
  reducers: {
    requestCapacityListSuccess(state = initUtilState, { payload }): UtilState {
      const map = new Map<string, string>();
      payload.forEach((item) => {
        map.set(item.type, item.name);
      });
      return {
        ...state,
        capacities: payload,
        capacityMap: map,
      };
    },
  },
};

export default utilModel;
