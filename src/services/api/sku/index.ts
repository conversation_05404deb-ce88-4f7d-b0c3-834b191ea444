import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const skuApiOption: Record<
  | 'serviceSkuList'
  | 'servicePlanList'
  | 'updatingSkuExpirationDate'
  | 'updatingSkuExpirationDateV2',
  RequestOption
> = {
  // 获取Sku列表
  serviceSkuList: {
    url: '/adm/:device/sku/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取Sku列表 - 服务解耦 - v2.0.0
  servicePlanList: {
    url: '/adm/bs/devicepackage/plan/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  updatingSkuExpirationDate: {
    url: '/adm/:device/sku/indate/change',
    option: {
      ...defaultOptions,
      headers: {
        ...defaultOptions.headers,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: RequestMethod.Post,
    },
  },
  updatingSkuExpirationDateV2: {
    url: '/adm/bs/devicepackage/plan/expiration/change',
    option: {
      ...defaultOptions,
      headers: {
        ...defaultOptions.headers,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: RequestMethod.Post,
    },
  },
};
