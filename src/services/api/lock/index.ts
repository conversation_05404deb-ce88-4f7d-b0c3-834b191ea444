import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const lockApiOption: Record<
  | 'lockMaterialList'
  | 'lockMaterialDetail'
  | 'lockMaterialSaving'
  | 'lockMaterialDeletion',
  RequestOption
> = {
  // 获取LockMaterial列表
  lockMaterialList: {
    url: '/adm/lock/materials',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据LockMaterial的id获取详情数据
  lockMaterialDetail: {
    url: '/adm/lock/material',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 保存LockMaterial
  lockMaterialSaving: {
    url: '/adm/lock/material_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除LockMaterial
  lockMaterialDeletion: {
    url: '/adm/lock/material_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
