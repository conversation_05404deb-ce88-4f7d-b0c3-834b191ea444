import { ChangeExpirationRecordListParam } from '@/models/history/interface';
import { initChangeExpirationRecordListParam } from '@/models/history/util';
import dayjs from 'dayjs';
import { ChangeExpirationTableForm, UrlParam } from './interface';

export const transferFormDataToUrlParam = (
  formData: ChangeExpirationTableForm,
): UrlParam => {
  const param: UrlParam = {};

  if (formData.orderId) {
    param.orderId = formData.orderId;
  }

  if (formData.deviceType) {
    param.deviceType = formData.deviceType as string;
  }

  if (formData.skuName) {
    param.skuName = formData.skuName;
  }

  if (formData.operatorTime && formData.operatorTime.length > 0) {
    param.startTime = formData.operatorTime[0].startOf('day').valueOf();
    param.endTime = formData.operatorTime[1].endOf('day').valueOf();
  }

  return param;
};

export const transferUrlParamToFormData = (
  urlParam: UrlParam,
): ChangeExpirationTableForm => {
  const formData: ChangeExpirationTableForm = {};

  // 基础字段映射
  if (urlParam.orderId) formData.orderId = urlParam.orderId;
  if (urlParam.deviceType) formData.deviceType = urlParam.deviceType;
  if (urlParam.skuName) formData.skuName = urlParam.skuName;

  // 处理时间
  if (urlParam.startTime && urlParam.endTime) {
    formData.operatorTime = [
      dayjs(+urlParam.startTime),
      dayjs(+urlParam.endTime),
    ];
  }
  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): ChangeExpirationRecordListParam => {
  const listParam: ChangeExpirationRecordListParam = {
    ...initChangeExpirationRecordListParam,
  };
  if (urlParam.orderId) {
    listParam.orderId = urlParam.orderId;
  }
  if (urlParam.deviceType) {
    listParam.deviceType = urlParam.deviceType;
  }
  if (urlParam.startTime && urlParam.endTime) {
    listParam.startOperationTime = +urlParam.startTime;
    listParam.endOperationTime = +urlParam.endTime;
  }
  if (urlParam.skuName) listParam.planName = urlParam.skuName;
  return listParam;
};
