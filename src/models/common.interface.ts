import { DefaultOptionType } from 'antd/lib/cascader';
import React from 'react';

export type EditorType = 'create' | 'update' | undefined;

export enum StatusEnum {
  ENABLE = 1,
  DISABLE = 0,
}

export interface ResponseLogicError {
  code: number;
  msg: string;
}

export interface Key {
  key: string;
}

export type KeyValue = string | number;
export type ValueType = string | number;

// export interface Node {
//   checkable?: boolean;
//   disabled?: boolean;
//   disableCheckbox?: boolean;
//   icon?: IconType;
//   isLeaf?: boolean;
//   title?: React.ReactNode;
//   selectable?: boolean;
//   switcherIcon?: IconType;
//   className?: string;
//   style?: React.CSSProperties;
// }

// antd没有暴露Data node数据结构
export interface DataNode extends Node {
  key: string | number;
  children?: DataNode[];
  parent?: DataNode;
}

// antd没有暴露Tree node数据结构
export interface TreeNode extends Node {
  key?: string | number;
  value: string | number;
  label?: React.ReactNode;
  children?: TreeNode[];
  parent?: TreeNode;
  /** Customize data info */
  [prop: string]: any;
}

export interface SelectOption<T = ValueType> {
  label: string;
  value: T;
  disabled?: boolean;
}

export type CascaderOption = DefaultOptionType;

export enum ApiSuccessEnum {
  success = 'success',
}

export interface PMData {
  type: PMDataType;
  content?: PMDataContent;
}

export type PMDataType = 'redirect' | 'relogin';

export interface PMDataContent {
  redirectUrl?: string;
  param?: Record<string, any>;
}

export interface BreadcrumbInfo {
  path: string;
  breadcrumbName: string;
}

// 推断出Array泛型中的interface
export type ArrElement<ArrType> = ArrType extends readonly (infer ElementType)[]
  ? ElementType
  : never;
