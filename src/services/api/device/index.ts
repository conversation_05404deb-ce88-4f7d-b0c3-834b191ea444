import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from './../util';

export const deviceApiOption: Record<
  // bs中的获取device信息接口
  | 'deviceInfo'
  | 'deviceList'
  | 'link'
  | 'unlink'
  | 'linkHistory'
  | 'snList'
  | 'deviceSnDeletion'
  | 'deviceSnSaving'
  // 固件相关
  | 'firmwareList'
  | 'firmwareDetail'
  | 'firmwareSaving'
  | 'firmwareBetaDeviceList'
  | 'firmwareBetaDeviceAdding'
  | 'firmwareBetaDeviceDeletion'
  | 'firmwareNotifyingUpgrade'
  | 'firmwareDeletion'
  | 'deviceCapacityList'
  | 'deviceCapacityDetail'
  | 'deviceCapacityCreation'
  | 'deviceCapacityUpdate'
  // 视频反馈
  | 'aiVideoFeedback'
  | 'm3u8VideoInfo',
  RequestOption
> = {
  deviceInfo: {
    url: '/adm/bs/device',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取Device列表
  deviceList: {
    url: '/adm/:device/devices',
    option: {
      ...defaultOptions,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      method: RequestMethod.Post,
    },
  },
  // 获取Device列表
  deviceCapacityList: {
    url: '/adm/bs/device/capacity/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  deviceCapacityDetail: {
    url: '/adm/bs/device/capacity/detail/:id',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  deviceCapacityCreation: {
    url: '/adm/bs/device/capacity/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  deviceCapacityUpdate: {
    url: '/adm/bs/device/capacity/update',
    option: {
      ...defaultOptions,
      method: RequestMethod.Put,
    },
  },
  // 绑定
  link: {
    url: '/adm/:device/link',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 解绑
  unlink: {
    url: '/adm/:device/unlink',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  linkHistory: {
    url: '/adm/:device/link_history',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 序列号列表
  snList: {
    url: '/adm/:device/sn_list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除序列号
  deviceSnDeletion: {
    url: '/adm/:device/sn_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 保存序列号
  deviceSnSaving: {
    url: '/adm/:device/sn_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备固件列表
  firmwareList: {
    url: '/adm/:device/firmwares',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备固件详情
  firmwareDetail: {
    url: '/adm/:device/firmware',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件保存
  firmwareSaving: {
    url: '/adm/:device/firmware_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件内测设备列表
  firmwareBetaDeviceList: {
    url: '/adm/:device/firmware_betadevices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件添加内测设备
  firmwareBetaDeviceAdding: {
    url: '/adm/:device/firmware_add_betadevice',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件删除内测设备
  firmwareBetaDeviceDeletion: {
    url: '/adm/:device/firmware_remove_betadevice',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件通知升级
  firmwareNotifyingUpgrade: {
    url: '/adm/:device/firmware_notify',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件删除
  firmwareDeletion: {
    url: '/adm/:device/firmware_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // ai视频反馈
  aiVideoFeedback: {
    url: '/adm/:device/getVideos',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // M3U8数据
  m3u8VideoInfo: {
    url: '/adm/:device/getM3u8ForAdm',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
      requestType: 'form',
    },
  },
};
