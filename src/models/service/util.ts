import { initPaginatorParam } from '@/utils/request';
import {
  ServiceListParam,
  ServiceListV2Param,
  ServicePlanChangeTypeEnum,
  ServiceStatusEnum,
  ServiceUpdatingHistoryListParam,
  ServiceUpdatingTypeEnum,
} from './interface';

export const initServiceListParam: ServiceListParam = {
  ...initPaginatorParam,
};

export const initServiceListV2Param: ServiceListV2Param = {
  ...initPaginatorParam,
};

export const serviceStatusNameObj: { [key in ServiceStatusEnum]: string } = {
  [ServiceStatusEnum.ON_SERVICE]: '生效中',
  [ServiceStatusEnum.OUT_DATE]: '已过期',
};

export const initServiceUpdatingHistoryListParam: ServiceUpdatingHistoryListParam =
  {
    ...initPaginatorParam,
    serviceId: 0,
  };

export const serviceUpdatingTypeNameObj: {
  [key in ServiceUpdatingTypeEnum]: string;
} = {
  [ServiceUpdatingTypeEnum.DEVICE_SN]: '设备SN',
  [ServiceUpdatingTypeEnum.USER_ID]: '用户ID',
};

// 变更历史中的变更类型名称
export const servicePlanChangeTypeName: {
  [key in ServicePlanChangeTypeEnum]: string;
} = {
  [ServicePlanChangeTypeEnum.TRANSFER]: 'Plan变更',
  [ServicePlanChangeTypeEnum.CREATE]: '服务创建',
  [ServicePlanChangeTypeEnum.PLAN_UPDATE]: 'Plan变更',
  [ServicePlanChangeTypeEnum.APPEND_PLAN]: 'Plan变更',
  [ServicePlanChangeTypeEnum.SERVICE_LINK]: '服务变更',
  [ServicePlanChangeTypeEnum.SERVICE_UNLINK]: '服务变更',
  [ServicePlanChangeTypeEnum.DELETE_SERVICE]: '服务变更',
  [ServicePlanChangeTypeEnum.REFUND]: 'Plan变更',
};
