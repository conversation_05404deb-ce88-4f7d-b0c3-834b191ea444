import { Paginator, PaginatorParam } from '@/utils/request';
import { TablePaginationConfig } from 'antd';

export const antdUtils = {
  defaultPageSizeOptions: [10, 25, 50, 100],
  transferPaginatorToTablePagination: (paginator: Paginator) => {
    const tablePagination: TablePaginationConfig = {
      total: paginator.total,
      current: Math.trunc(paginator.offset / paginator.limit) + 1,
      pageSize: paginator.limit,
    };
    return tablePagination;
  },

  getPaginatorParamByTablePaginationChange: (
    paginatorParam: PaginatorParam,
    page: number,
    pageSize: number,
  ) => {
    const _paginatorParam: PaginatorParam = {
      limit: pageSize,
      offset: 0,
    };
    let index = page;
    if (pageSize !== paginatorParam.limit) {
      index = 1;
    }
    _paginatorParam.offset = pageSize * (index - 1);
    return _paginatorParam;
  },
};
