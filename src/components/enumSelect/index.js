import { isNullOrUndefined } from '@/utils/lang';
import { useDispatch } from '@umijs/max';
import { Radio, Select } from 'antd';
import { useEffect, useState } from 'react';

export default (props) => {
  const dispatch = useDispatch();

  const {
    placeholder = '请选择',
    type = 'select',
    hasAll = false,
    disabled,
    enumType,
    value,
  } = props;

  const [options, setOptions] = useState([]);

  const getOrderTypeEnumList = () => {
    dispatch({
      type: 'global/getCommEnumList',
      payload: {
        enumType,
      },
      success: (datas) => {
        setOptions(datas);
      },
    });
  };

  useEffect(() => {
    if (enumType) {
      getOrderTypeEnumList();
    }
  }, [enumType]);

  const selectRender = () => (
    <Select
      disabled={disabled}
      placeholder={placeholder}
      allowClear
      value={isNullOrUndefined(value) ? undefined : String(value)}
      onChange={(e) => {
        props.onChange && props.onChange(e);
      }}
    >
      {hasAll && (
        <Select.Option key="asdfsd" value="">
          全部
        </Select.Option>
      )}
      {options.map((item) => (
        <Select.Option key={item.key} value={item.key}>
          {item.value}
        </Select.Option>
      ))}
    </Select>
  );

  const radioButtonRender = () => {
    return (
      <Radio.Group
        disabled={disabled}
        buttonStyle="solid"
        value={isNullOrUndefined(value) ? undefined : String(value)}
        onChange={(e) => {
          props.onChange && props.onChange(e);
        }}
      >
        {hasAll && (
          <Radio.Button key="asdfsd" value="">
            全部
          </Radio.Button>
        )}
        {options.map((item) => (
          <Radio.Button key={item.key} value={item.key}>
            {item.value}
          </Radio.Button>
        ))}
      </Radio.Group>
    );
  };

  return type === 'select' ? selectRender() : radioButtonRender();
};
