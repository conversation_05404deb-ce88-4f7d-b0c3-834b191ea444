import { CurrencyPrice } from '@/components/CurrencyPriceForm/interface';
import { StatusEnum } from '@/models/common.interface';
import { SuitableDeviceTypeEnum } from '@/models/device/interface';
import {
  ProductSkuCapacityTypeEnum,
  ReNewEnum,
  SaleStatusEnum,
  ServiceTimeUnitEnum,
} from '@/models/product/interface';
import { DomainTypeEnum } from '@/utils/targetDomain';
import { UploadFile } from 'antd/lib/upload';

export interface UrlParam {
  isCopy?: StatusEnum;
  skuId?: number;
  source?: DomainTypeEnum;
}

export interface ProductSkuListForm {
  id?: number;
  name?: string;
  shortName?: string;
  aliasName?: string;
  capacities?: ProductSkuCapacityTypeEnum;
  isReNew?: ReNewEnum;
  saleStatus?: SaleStatusEnum;
  deviceType?: string;
  level?: number;
  sort?: number;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
}

export interface ProductSkuForm {
  // SKU内容
  name: string;
  simpleName: string;
  aliasName: string;
  level: number | null;
  sort: number | null;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceType: SuitableDeviceTypeEnum | null;
  // SKU能力
  skuCapacities: {
    type: string;
    name: string;
    minCycleTime: number;
    cycleTime: number | null;
    enable: boolean;
  }[];
  // SKU价格
  currencyPrice: CurrencyPrice;
  isAutoRenew: boolean;
  isFirstPreferential: boolean;
  // preferentialPrice?: number;
  preferentialPrice?: CurrencyPrice;
  // 营销设置
  cornerIcons: UploadFile[];
  // description: string;
}
