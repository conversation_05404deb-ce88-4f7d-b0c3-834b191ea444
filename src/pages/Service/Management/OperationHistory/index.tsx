import { Key } from '@/models/common.interface';
import { fetchServiceUpdatingHistoryList } from '@/models/service/fetch';
import {
  ServiceUpdatingHistory,
  ServiceUpdatingHistoryListParam,
} from '@/models/service/interface';
import {
  initServiceUpdatingHistoryListParam,
  serviceUpdatingTypeNameObj,
} from '@/models/service/util';
import { Paginator, initPaginator } from '@/utils/request';
import { uuid } from '@/utils/uuid';
import { Modal, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';

interface Props {
  deviceType: string;
  serviceId: number;
  showModal: boolean;
  onClose?: () => void;
}

const OperationHistoryV2: React.FC<Props> = ({
  showModal,
  serviceId,
  onClose,
  deviceType,
}: Props) => {
  const [listParam, setListParam] = useState<ServiceUpdatingHistoryListParam>({
    ...initServiceUpdatingHistoryListParam,
    serviceId,
  });
  const [paginator, setPaginator] = useState<Paginator>(initPaginator);
  const [list, setList] = useState<Array<ServiceUpdatingHistory & Key>>([]);

  const requestHistoryList = async (
    _listParam: ServiceUpdatingHistoryListParam,
  ) => {
    let result = await fetchServiceUpdatingHistoryList(_listParam, deviceType);
    if (!result) return;
    const { items, ...rest } = result;
    setPaginator(rest);
    setList(items.map((item) => ({ ...item, key: uuid() })));
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let current = page;
    if (pageSize !== listParam.limit) {
      current = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (current - 1) * pageSize,
    });
  };

  const columns: ColumnsType<ServiceUpdatingHistory> = [
    {
      title: '变更字段',
      dataIndex: 'operateType',
      width: 120,
      render: (_, row) => serviceUpdatingTypeNameObj[row.changeType] || '-',
    },
    {
      title: '变更前',
      dataIndex: 'before',
      width: 200,
    },
    {
      title: '变更后',
      dataIndex: 'after',
      width: 200,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTimeStr',
      width: 200,
    },
  ];

  useEffect(() => {
    if (listParam && listParam.serviceId) {
      requestHistoryList(listParam);
    }
  }, [listParam]);

  return (
    <Modal
      width={950}
      destroyOnClose
      open={showModal}
      footer={null}
      title="变更历史"
      onCancel={() => isFunction(onClose) && onClose()}
    >
      <Table<ServiceUpdatingHistory>
        dataSource={list}
        columns={columns}
        size="small"
        rowKey="key"
        scroll={{
          x: columns.reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          total: paginator.total,
          pageSize: paginator.limit,
          showSizeChanger: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default OperationHistoryV2;
