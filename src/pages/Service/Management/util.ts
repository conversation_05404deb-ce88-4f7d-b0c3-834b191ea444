// import {
//   ManagementDetail,
//   ManagementParam,
// } from '@/models/management/interface';
// import { ManagementForm } from './interface';

// export const initialManagementForm: ManagementForm = {
// };

// // 将formData转换为param
// export const transferFormDataToParam = (formData: ManagementForm, id?: number): ManagementParam => {
//   const param: ManagementParam = {
//   };
//   id && (param.id = id);
//   return param;
// };

// // 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: ManagementDetail): ManagementForm => {
//   const formData: ManagementForm = {
//   };
//   return formData;
// };
