import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const orderApiOption: Record<
  'orderList' | 'orderListExport' | 'completeOrder',
  RequestOption
> = {
  // 获取Order列表
  orderList: {
    url: '/adm/bs/order/queryList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  orderListExport: {
    url: '/adm/bs/order/export',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  completeOrder: {
    url: '/adm/bs/order/completeServiceInfo',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
