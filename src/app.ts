import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);
// import { BasicLayoutProps, MenuDataItem, Settings as LayoutSettings } from '@ant-design/pro-components';
// import { cn } from './locale';

// const changeChildrenPath = (menuDataList: MenuDataItem[]): MenuDataItem[] => {
//   if (!menuDataList) return [];
//   const lang = cn;
//   return menuDataList.map((item) => ({
//     ...item,
//     path: `${item.path?.replace(':language', lang)}`,
//     children: changeChildrenPath(item.children || []),
//   }));
// };

// export const layout = ({
//   initialState,
// }: {
//   initialState: { settings?: LayoutSettings };
// }): BasicLayoutProps => {
//   return {
//     // rightContentRender: () => <RightContent />,
//     onPageChange: () => {
//       // const { location } = history;
//       // // 如果没有登录，重定向到 login
//       // if (!currentUser && location.pathname !== '/user/login') {
//       //   history.push('/user/login');
//       // }
//     },
//     menuDataRender: (menuDataList) => {
//       const menu = menuDataList.find((item) => item.path === '/');
//       if (!menu) return [];
//       return menu && menu.children ? changeChildrenPath(menu.children) : [];
//     },
//     ...initialState?.settings,
//   };
// };
