import RedirectLink from '@/components/TableRowLink/RedirectLink';
import { postMessageFunction } from '@/models/common.util';
import { SearchTypeEnum, WifiSearchTypeEnum } from '@/models/device/interface';
import { fetchServiceList } from '@/models/service/fetch';
import { Service, ServiceListParam } from '@/models/service/interface';
import {
  initServiceListParam,
  serviceStatusNameObj,
} from '@/models/service/util';
import { antdUtils } from '@/utils/antd.util';
import global from '@/utils/global';
import { Paginator, initPaginator } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Select, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import OperationHistory from '../OperationHistory';
import ServiceSkuList from '../ServiceSkuList';
import ServiceTransferring from '../ServiceTransferring';
import { ServiceListForm } from '../interface';
// import { postMessageFunction } from '@/models/common.util';

const List: React.FC = () => {
  // const urlRestParam = useParams<{ device: string }>();
  const [urlParam, setUrlParam] = useUrlState<ServiceListForm>();
  const [dataList, setDataList] = useState<Service[]>([]);
  const [listParam, setListParam] = useState<ServiceListParam>();
  const [pagination, setPagination] = useState<Paginator>(initPaginator);
  const [showOperationHistory, setShowOperationHistory] = useState(false);
  const [showServiceTransferring, setShowServiceTransferring] = useState(false);
  const [showServiceSkuList, setShowServiceSkuList] = useState(false);
  const [selectedService, setSelectedService] = useState<Service>();
  const [deviceType, setDeviceType] = useState('');
  const [loading, setLoading] = useState(false);
  const formRef = useRef<ProFormInstance<ServiceListForm>>();

  // 获取列表数据
  const requestServiceList = async (
    param: ServiceListParam,
    _deviceType: string,
  ) => {
    setLoading(true);
    try {
      const { items, ...rest } = await fetchServiceList(param, _deviceType);
      setPagination(rest);
      setDataList(items);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const showHistory = (row: Service) => {
    setSelectedService(row);
    setShowOperationHistory(true);
  };

  const transferService = (row: Service) => {
    setSelectedService(row);
    setShowServiceTransferring(true);
  };

  const showSkuList = (row: Service) => {
    setSelectedService(row);
    setShowServiceSkuList(true);
    // 重定向到SKU管理页面去
    // postMessageFunction({
    //   type: 'redirect',
    //   content: { redirectUrl: `/business/cloudServiceSku?id=${row.skuId}` },
    // });
  };

  // const relateDevice = (row: Service) => {
  //   setSelectedService(row);
  // };

  const onModelClosed = (shouldRefresh?: boolean) => {
    setShowOperationHistory(false);
    setShowServiceTransferring(false);
    setShowServiceSkuList(false);
    setSelectedService(undefined);
    if (shouldRefresh) {
      setListParam({ ...initServiceListParam, ...listParam });
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        loading={loading}
        disabled={loading}
        onClick={() => {
          const formData: ServiceListForm = form?.getFieldsValue();
          let key: keyof ServiceListForm;
          for (key in formData) {
            if (formData[key] === undefined || !formData[key]) {
              delete formData[key];
            }
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/business/cloudService`,
              param: formData as unknown as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setUrlParam(form?.getFieldsValue());
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;

    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  // 当SKU详情中存在成功退款的时候，需要刷新当页页面的数据
  const onRefundSuccess = () => {
    setListParam({ ...initServiceListParam, ...listParam });
  };

  const columns: Array<ProColumns<Service>> = [
    {
      title: '服务ID',
      width: 80,
      dataIndex: 'serviceId',
    },
    {
      title: '用户ID',
      width: 100,
      dataIndex: 'userId',
      render: (text, row) =>
        row.userId ? (
          <RedirectLink
            text={text}
            linkUrl="/user/users"
            params={{
              username: row.userId,
            }}
          />
        ) : (
          '-'
        ),
    },
    // {
    //   title: '设备ID',
    //   width: 100,
    //   dataIndex: 'deviceId',
    //   render: (_, row) =>
    //     deviceType && row.deviceId ? (
    //       <RedirectLink
    //         text={row.deviceId}
    //         linkUrl={`/${urlParam.deviceType}/devices`}
    //         params={{
    //           type: global.bluetoothDeviceList.includes(deviceType)
    //             ? SearchTypeEnum.ID
    //             : WifiSearchTypeEnum.ID,
    //           s: row.deviceId,
    //         }}
    //       />
    //     ) : (
    //       row.deviceId || '-'
    //     ),
    // },
    {
      title: '设备SN',
      dataIndex: 'sn',
      width: 150,
      render: (_, row) =>
        deviceType && row.sn ? (
          <RedirectLink
            text={row.sn}
            linkUrl={`/${urlParam.deviceType}/devices`}
            params={{
              type: global.bluetoothDeviceList.includes(deviceType)
                ? SearchTypeEnum.Sn
                : WifiSearchTypeEnum.SN,
              s: row.sn,
            }}
          />
        ) : (
          row.sn || '-'
        ),
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      width: 100,
      search: false,
      render: (_, row) => (row.deviceType || '-').toUpperCase(),
    },
    {
      title: 'Plan名称',
      width: 150,
      dataIndex: 'shortName',
      search: false,
    },
    {
      title: 'Plan能力',
      width: 160,
      dataIndex: 'capacity',
      search: false,
      tooltip: '指正在生效的Plan能力',
      render: (_, row) =>
        (row.capacity || []).map((cap, index) => (
          <div key={String(index)}>
            {cap.type} {cap.cycleTime}天循环
          </div>
        )),
    },
    {
      title: '服务状态',
      width: 100,
      dataIndex: 'serviceStatus',
      valueType: 'select',
      valueEnum: serviceStatusNameObj,
      render: (_, row) => serviceStatusNameObj[row.serviceStatus],
    },
    {
      title: '到期时间',
      width: 180,
      dataIndex: 'indateStr',
      search: false,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 240,
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <Space size="large">
          <a onClick={() => showHistory(row)}>变更历史</a>
          <a onClick={() => transferService(row)}>转移服务</a>
          <a onClick={() => showSkuList(row)}>服务Plan</a>
          {/* {!row.sn ? (
            <a key={'relative'} onClick={() => relateDevice(row)}>
              关联设备
            </a>
          ) : null} */}
        </Space>
      ),
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: { d4h: 'D4H', t5: 'T5', t6: 'T6', t7: 'T7' },
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} allowClear />
      ),
    },
  ];

  useEffect(() => {
    const _listParam = { ...initServiceListParam, ...urlParam };
    delete _listParam.deviceType;
    setListParam({ ..._listParam });
    setDeviceType(urlParam.deviceType);

    if (JSON.stringify(urlParam) === '{}') return;

    const form = formRef.current;
    form?.setFieldsValue(urlParam);
  }, [urlParam]);

  useEffect(() => {
    if (listParam && deviceType) requestServiceList(listParam, deviceType);
  }, [listParam]);

  return (
    <>
      <ProTable<Service>
        dataSource={dataList}
        formRef={formRef}
        columns={columns}
        defaultSize="small"
        rowKey="serviceId"
        loading={loading}
        search={{
          defaultCollapsed: false,
          span: 6,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        // toolbar={{
        //   actions: [
        //     <Button
        //       key="button"
        //       type="primary"
        //       icon={<PlusOutlined />}
        //       onClick={() => editService(0)}
        //     >
        //       新增
        //     </Button>,
        //   ],
        // }}
        pagination={{
          ...antdUtils.transferPaginatorToTablePagination(pagination),
          onChange: onPaginationChanged,
        }}
      />
      {showOperationHistory && selectedService ? (
        <OperationHistory
          deviceType={deviceType}
          showModal
          serviceId={selectedService.serviceId}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
      {showServiceTransferring && selectedService ? (
        <ServiceTransferring
          deviceType={deviceType}
          showModal
          currentUserId={selectedService.userId}
          serviceId={selectedService.serviceId}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
      {showServiceSkuList && selectedService ? (
        <ServiceSkuList
          deviceType={deviceType}
          showModal
          serviceId={selectedService.serviceId}
          onRefundSuccess={onRefundSuccess}
          onClose={onModelClosed}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default List;
