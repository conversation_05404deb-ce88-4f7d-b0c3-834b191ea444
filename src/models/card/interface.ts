import { PaginatorParam } from '@/utils/request';
import { SuitableDeviceTypeEnum } from '../device/interface';
import { ServiceTimeUnitEnum } from '../product/interface';

export enum CouponCardUsageModeEnum {
  USER = 'USER',
  NUM = 'NUM',
}

// 卡号兑换状态
export enum ExchangeStatusEnum {
  /**
   * 0 未分配
   */
  UNASSIGNED = 0,
  /**
   * 1 未兑换
   */
  UNEXCHANGE = 1,
  /**
   * 2 已兑换
   */
  EXCHANGED = 2,
  /**
   * 3 已过期
   */
  EXPIRED = 3,
  /**
   * 4 已废弃
   */
  ABANDONED = 4,
}

export enum SendWayEnum {
  MANUAL = 'MANUAL',
  AUTO = 'AUTO',
}

export interface CouponCardSkuParam {
  deviceType: SuitableDeviceTypeEnum;
  productSkuId: number;
  skuName: string;
}

export type CouponCardSku = CouponCardSkuParam;

// 列表参数数据
export interface CouponCardListParam extends PaginatorParam {
  deviceType?: SuitableDeviceTypeEnum;
  batchId?: string;
  cardCouponName?: string;
  cardCouponNickName?: string;
  skuName?: string;
  demandSide?: string;
  startTime?: number;
  endTime?: number;
}

// 列表数据
export interface CouponCard {
  id: number;
  cardCouponName: string;
  cardCouponNickName: string;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  realPrice: number;
  facePrice: number;
  exchangeStart: number;
  exchangeEnd: number;
  couponImage: string;
  demandSide: string;
  sendNum: number;
  operatorName: string;
  operatorId: number;
  createDate: string;
  batchId: string;
  skus: CouponCardSku[];
}

// 创建/编辑提交参数
export interface CouponCardParam {
  couponImage: string;
  cardCouponName: string;
  cardCouponNickName: string;
  demandSide: string;
  expiredTime: number;
  facePrice: number;
  mobileKey: string;
  mode: CouponCardUsageModeEnum;
  realPrice: number;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  skuRequestParams: CouponCardSkuParam[];
  sendNum?: number;
}

// 详情数据
export interface CouponCardDetail {
  id: number;
  cardCouponName: string;
  cardCouponNickName: string;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  realPrice: number;
  facePrice: number;
  exchangeStart: number;
  exchangeEnd: number;
  couponImage: string;
  demandSide: string;
  sendNum: number;
  batchId: string;
  skus: CouponCardSku[];
}

export interface CouponCardCodeListParam extends PaginatorParam {
  cardCode?: string;
  mobile?: string;
  snCode?: string;
  exchangeStatus?: ExchangeStatusEnum;
  exchangeStartTime?: number;
  exchangeEndTime?: number;
  batchId?: string;
  deviceTypes?: SuitableDeviceTypeEnum[];
  activatedDeviceType?: SuitableDeviceTypeEnum;
  startTime?: number;
  endTime?: number;
  sendWay?: SendWayEnum;
}

export interface CouponCardCode {
  cardConfigId: number;
  cardCode: string;
  mobile: string;
  exchangeStatus: ExchangeStatusEnum;
  exchangeStatusDesc: string;
  sendWay: SendWayEnum;
  batchId: string;
  createDate: number;
  // 有多个设备 以逗号分隔
  deviceTypes: string;
  activatedDeviceType: string;
  exchangedDate: string;
  snCode: string;
}

export interface CouponCardUploadResult {
  total: number;
  mobileKey?: string;
  emailKey?: string;
}

export interface CouponCardUploadValidResult {
  total: number;
  invalidNum: number;
  failValidMobiles: string[];
}
