import { VirtualCardDistributionListParam } from '@/models/history/interface';
import { initVirtualCardDistributionListParam } from '@/models/history/util';
import dayjs from 'dayjs';
import { DistributionTableForm, UrlParam } from './interface';

export const transferFormDataToUrlParam = (
  formData: DistributionTableForm,
): UrlParam => {
  const param: UrlParam = {};

  if (formData.batchId) {
    param.batchId = formData.batchId;
  }

  if (formData.deviceType) {
    param.deviceType = formData.deviceType.join(',');
  }

  if (formData.operatorTime && formData.operatorTime.length > 0) {
    param.startTime = formData.operatorTime[0].startOf('day').valueOf();
    param.endTime = formData.operatorTime[1].endOf('day').valueOf();
  }

  return param;
};

export const transferUrlParamToFormData = (
  urlParam: UrlParam,
): DistributionTableForm => {
  const formData: DistributionTableForm = {};

  // 基础字段映射
  if (urlParam.deviceType) formData.deviceType = urlParam.deviceType.split(',');
  if (urlParam.batchId) formData.batchId = urlParam.batchId;

  // 处理时间
  if (urlParam.startTime && urlParam.endTime) {
    formData.operatorTime = [
      dayjs(+urlParam.startTime),
      dayjs(+urlParam.endTime),
    ];
  }
  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): VirtualCardDistributionListParam => {
  const listParam: VirtualCardDistributionListParam = {
    ...initVirtualCardDistributionListParam,
  };
  if (urlParam.batchId) {
    listParam.batchId = urlParam.batchId;
  }
  if (urlParam.deviceType) {
    listParam.deviceTypes = urlParam.deviceType;
  }
  if (urlParam.startTime && urlParam.endTime) {
    listParam.startOperationTime = +urlParam.startTime;
    listParam.endOperationTime = +urlParam.endTime;
  }
  return listParam;
};
